package com.ce.scrm.customer.util.sign;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ce.scrm.customer.util.constant.UtilConstant;
import com.ce.scrm.customer.util.date.DateTimeFormatUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 签名工具类
 * <AUTHOR>
 * @date 2023/4/10 15:49
 * @version 1.0.0
 */
@Slf4j
public class SignUtils {

    /**
     * 获取有序参数集合
     * @param args  参数对象列表
     * <AUTHOR>
     * @date 2023/4/10 16:02
     * @return java.util.Map<java.lang.String, java.lang.String>
     **/
    public static Map<String, String> getParamMap(Object... args) {
        Map<String, Object> paramMap = new HashMap<>();
        //记录需要转化的日期字段
        Set<String> fieldNameSet = new HashSet<>();
        for (Object arg : args) {
            if (arg != null) {
                paramMap.putAll(JSON.parseObject(JSON.toJSONString(arg), new TypeReference<HashMap<String, Object>>() {
                }));
                //记录日期自动转为时间戳的字段
                Class<?> clazz = arg.getClass();
                Field[] fields = clazz.getDeclaredFields();
                fieldNameSet.addAll(Arrays.stream(fields).filter(field -> {
                    try {
                        field.setAccessible(true);
                        return (field.get(arg) instanceof Date);
                    } catch (IllegalAccessException e) {
                        log.error("获取方法数据,无法访问字段，异常为:", e);
                    }
                    return false;
                }).map(Field::getName).collect(Collectors.toSet()));
            }
        }
        //参数排序
        Map<String, String> returnParam = new LinkedHashMap<>();
        String[] paramArray = paramMap.keySet().toArray(new String[0]);
        Arrays.sort(paramArray);
        for (String paramName : paramArray) {
            Object paramValueObject = paramMap.get(paramName);
            if (paramValueObject == null) {
                continue;
            }
            String paramValue;
            if (paramValueObject instanceof String) {
                paramValue = paramValueObject.toString();
            } else if (fieldNameSet.contains(paramName)) {
                paramValue = DateUtil.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(paramValueObject.toString())), ZoneId.systemDefault()), DateTimeFormatUtil.STANDARD_DATE_TIME.getFormat());
            } else {
                paramValue = JSON.toJSONString(paramMap.get(paramName));
            }
            if (StrUtil.isNotBlank(paramValue)) {
                returnParam.put(paramName, paramValue);
            }
        }
        return returnParam;
    }

    /**
     * 生成签名
     * @param requestPath   请求路径
     * @param paramMap  参数集合（有序）
     * <AUTHOR>
     * @date 2023/4/10 16:01
     * @return java.lang.String
     **/
    public static String generateSign(String requestPath, Map<String, String> paramMap) {
        //拼接请求+参数字符串
        StringBuilder paramConcatStringBuilder = new StringBuilder(requestPath);
        paramConcatStringBuilder.append(UtilConstant.URL_PARAM_SEPARATOR);
        paramMap.keySet().forEach(paramKey -> paramConcatStringBuilder.append(paramKey).append(UtilConstant.PARAM_KEY_VALUE_SEPARATOR).append(paramMap.get(paramKey)).append(UtilConstant.PARAM_CONCAT_SEPARATOR));
        String paramConcatString = paramConcatStringBuilder.deleteCharAt(paramConcatStringBuilder.length() - 1).toString();
        log.info("签名生成前的字符串为:{}", paramConcatString);
        //生成签名
        return SecureUtil.md5(paramConcatString);
    }
}