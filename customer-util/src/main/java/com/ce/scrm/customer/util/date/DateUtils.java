package com.ce.scrm.customer.util.date;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 日期工具类
 * <AUTHOR>
 * @date 2023/4/8 15:28
 * @version 1.0.0
 **/
public class DateUtils {

    /**
     * xxl-job延迟秒数
     * 为了解决xxl-job在提交任务是如果距离当前时间过近，可能导致任务无法执行的问题
     */
    private final static int XXL_JOB_DURATION_SECOND = 1;
    /**
     * 秒转毫秒的进制
     */
    private final static int SECOND_TO_MILLIS = 1000;

    /**
     * 时间转换cron表达式（仅执行一次）
     * @param localDateTime    任务执行时间
     * <AUTHOR>
     * @date 2023/4/8 15:28
     * @return java.lang.String
     **/
    public static String generateCron(LocalDateTime localDateTime) {
        LocalDateTime currentDateTime = LocalDateTime.now();
        long second;
        if (currentDateTime.isAfter(localDateTime) && (second = Duration.between(currentDateTime, localDateTime).toMillis() / SECOND_TO_MILLIS) < XXL_JOB_DURATION_SECOND) {
            localDateTime = localDateTime.plusSeconds(XXL_JOB_DURATION_SECOND - second);
        }
        return localDateTime.format(DateTimeFormatter.ofPattern(DateTimeFormatUtil.XXL_JOB_CRON_FORMAT.getFormat()));
    }
}
