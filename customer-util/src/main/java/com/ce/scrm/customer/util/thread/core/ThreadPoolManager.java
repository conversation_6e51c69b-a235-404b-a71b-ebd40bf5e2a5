package com.ce.scrm.customer.util.thread.core;

import com.alibaba.ttl.TtlRunnable;
import com.ce.scrm.customer.util.thread.enums.ThreadRunnerTypeEnum;

import java.util.LinkedList;
import java.util.Queue;
import java.util.concurrent.*;

/**
 * 线程池管理类
 * <AUTHOR>
 * @date 2021/05/19 下午4:50
 * @version 1.0.0
 **/
public class ThreadPoolManager {
    /** 核心线程数 */
    private final static int CORE_THREAD_NUM = 1;

    /** 空闲线程存活时间 */
    private final static int TIME_KEEP_ALIVE = 5;

    /** 空闲线程存活时间单位*/
    private final static TimeUnit UNIT_KEEP_ALIVE = TimeUnit.SECONDS;

    /** 阻塞队列大小 */
    private final static int BLOCKING_QUEUE_SIZE = 400;

    /** 线程池阻塞队列 */
    private final static BlockingQueue<Runnable> BLOCKING_QUEUE = new ArrayBlockingQueue<>(BLOCKING_QUEUE_SIZE);

    /** 线程池 */
    private final ThreadPoolExecutor threadPoolExecutor;

    /** 线程任务类型 */
    private final ThreadRunnerTypeEnum threadRunnerTypeEnum;

    /** 任务的阻塞系数 */
    private final Double blockRate;

    /** 定时任务调度器 */
    private final ScheduledExecutorService scheduledExecutorService;

    /** 最大线程数量 */
    private final int maxThreadNum;

    /** 任务缓冲队列 */
    private final Queue<Runnable> cacheRunnerQueue = new LinkedList<>();

    /** 拒绝任务获取周期 */
    private final static int REJECTED_RUNNER_PULL_TIME = 100;

    /**
     * 创建任务线程池
     * @param threadRunnerTypeEnum  任务密集型
     * <AUTHOR>
     * @date 2021/05/21 上午9:15
     * @version 1.0.0
     **/
    public ThreadPoolManager(ThreadRunnerTypeEnum threadRunnerTypeEnum, ThreadFactoryManager threadFactoryManager) {
        this(threadRunnerTypeEnum, threadFactoryManager, threadRunnerTypeEnum.getBlockRate());
    }

    /**
     * 创建任务线程池
     * @param threadRunnerTypeEnum  任务密集型
     * @param blockRate 阻塞系数
     * <AUTHOR>
     * @date 2021/05/21 上午9:28
     * @version 1.0.0
     **/
    public ThreadPoolManager(ThreadRunnerTypeEnum threadRunnerTypeEnum, ThreadFactoryManager threadFactoryManager, Double blockRate) {
        this.threadRunnerTypeEnum = threadRunnerTypeEnum;
        this.blockRate = blockRate;
        int maxThreadNum = ThreadPoolParamCalculator.getMaxThreadNum(blockRate);
        //防止执行线程因为缺页中断或者某种原因导致的线程等待，多一个额外的线程可以继续使用时间片
        this.maxThreadNum = threadRunnerTypeEnum == ThreadRunnerTypeEnum.CPU ? maxThreadNum + 1 : maxThreadNum;
        //线程池超出界线时将任务加入缓冲队列(拒绝策略)
        RejectedExecutionHandler rejectedExecutionHandler = (task, executor) -> cacheRunnerQueue.offer(task);
        this.threadPoolExecutor = new ThreadPoolExecutor(CORE_THREAD_NUM, this.maxThreadNum, TIME_KEEP_ALIVE, UNIT_KEEP_ALIVE, BLOCKING_QUEUE, threadFactoryManager, rejectedExecutionHandler);
        //创建一个调度线程池
        scheduledExecutorService = Executors.newScheduledThreadPool(1);
        //将缓冲队列中的任务重新加载到线程池
        Runnable mAccessBufferThread = () -> {
            for (int i = 0; i < this.maxThreadNum; i++) {
                if (cacheRunnerQueue.isEmpty()) {
                    break;
                }
                threadPoolExecutor.execute(cacheRunnerQueue.poll());
            }
        };
        //通过调度线程周期性的执行缓冲队列中任务
        scheduledExecutorService.scheduleAtFixedRate(mAccessBufferThread, 0, REJECTED_RUNNER_PULL_TIME, TimeUnit.MILLISECONDS);
    }

    /**
     * 添加任务到线程池
     * @param runnable 任务
     * <AUTHOR>
     * @date 2021/05/21 上午11:06
     * @version 1.0.0
     **/
    public void addExecuteTask(Runnable runnable) {
        if (runnable != null && (runnable = TtlRunnable.get(runnable)) != null) {
            threadPoolExecutor.execute(runnable);
        }
    }

    /**
     *  获取线程池活着的数量
     * <AUTHOR>
     * @date 2021/05/21 上午11:07
     * @version 1.0.0
     * @return boolean
     **/
    public int getActiveCount() {
        return threadPoolExecutor.getActiveCount();
    }

    /**
     * 获取线程池中当前线程的数量
     * <AUTHOR>
     * @date 2021/05/21 下午2:19
     * @version 1.0.0
     * @return int
     **/
    public int getPoolSize() {
        return threadPoolExecutor.getPoolSize();
    }

    /**
     * 获取当前线程池已完成的任务数量
     * <AUTHOR>
     * @date 2021/05/21 下午2:24
     * @version 1.0.0
     * @return long
     **/
    public long getCompletedTaskCount() {
        return threadPoolExecutor.getCompletedTaskCount();
    }

    /**
     * 获取线程池出现的最大的线程数量
     * <AUTHOR>
     * @date 2021/05/21 下午2:25
     * @version 1.0.0
     * @return int
     **/
    public int getLargestPoolSize() {
        return threadPoolExecutor.getLargestPoolSize();
    }

    /**
     *  获取任务缓冲池的任务数量
     * <AUTHOR>
     * @date 2021/05/21 下午2:28
     * @version 1.0.0
     * @return int
     **/
    public int getCacheRunnerCount() {
        return cacheRunnerQueue.size();
    }

    /**
     * 关闭线程池
     * <AUTHOR>
     * @date 2021/05/21 上午11:18
     * @version 1.0.0
     **/
    public void shutdown() {
        cacheRunnerQueue.clear();
        scheduledExecutorService.shutdown();
        threadPoolExecutor.shutdown();
    }

    /**
     * 获取当前线程池的线程任务类型
     * <AUTHOR>
     * @date 2021/05/21 下午2:23
     * @version 1.0.0
     * @return com.ce.scrm.customer.util.thread.enums.ThreadRunnerTypeEnum
     **/
    public ThreadRunnerTypeEnum getThreadRunnerTypeEnum() {
        return threadRunnerTypeEnum;
    }

    /**
     * 获取当前线程的阻塞系数
     * <AUTHOR>
     * @date 2021/05/21 下午2:23
     * @version 1.0.0
     * @return java.lang.Double
     **/
    public Double getBlockRate() {
        return blockRate;
    }
}
