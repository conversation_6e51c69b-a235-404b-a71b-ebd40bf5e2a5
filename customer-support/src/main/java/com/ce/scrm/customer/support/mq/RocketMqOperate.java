package com.ce.scrm.customer.support.mq;

import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * rocketMq封装操作类
 * 关于延时的等级，默认为0，0为不开启延时，从1开始对应的默认级别为：
 * messageDelayLevel=1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
 * 不建议使用同步发送之外的消息保证顺序，异步消息和ONE_WAY消息可能都会导致消息多线程投递，而使消费的时候没有将所有消息都投递成功而出现部分有序，部分无序的情况
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 **/
@Component
public class RocketMqOperate {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(RocketMqOperate.class);

    @Resource
    private RocketMQTemplate rocketMqTemplate;


    /**
     * 同步发送消息
     * 超时时间取自配置
     * @param topic 主题
     * @param message   消息
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return org.apache.rocketmq.client.producer.SendResult
     **/
    public SendResult syncSend(String topic, Object message) {
        return syncSend(topic, message, (long) rocketMqTemplate.getProducer().getSendMsgTimeout());
    }

    /**
     * 同步发送消息
     * 自定义超时时间
     * @param topic 主题
     * @param message   消息
     * @param timeout   超时时间
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return org.apache.rocketmq.client.producer.SendResult
     **/
    public SendResult syncSend(String topic, Object message, long timeout) {
        return syncSend(topic, message, timeout, 0);
    }

    /**
     * 同步发送消息
     * 超时时间取自配置
     * 自定义延时等级
     * @param topic 主题
     * @param message   消息
     * @param level 延时等级
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return org.apache.rocketmq.client.producer.SendResult
     **/
    public SendResult syncSend(String topic, Object message, int level) {
        return syncSend(topic, message, rocketMqTemplate.getProducer().getSendMsgTimeout(), level);
    }

    /**
     * 同步发送消息
     * 自定义超时时间、延时等级
     * @param topic 主题
     * @param message   消息
     * @param timeout   超时时间
     * @param level 延时等级
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return org.apache.rocketmq.client.producer.SendResult
     **/
    public SendResult syncSend(String topic, Object message, long timeout, int level) {
        return rocketMqTemplate.syncSend(topic, MessageBuilder.withPayload(message).build(), timeout, level);
    }


    /**
     * 同步发送顺序消息
     * @param topic 主题
     * @param message   消息
     * @param orderlyHash   保证顺序的hashKey
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return org.apache.rocketmq.client.producer.SendResult
     **/
    public SendResult syncSendOrderly(String topic, Object message, String orderlyHash) {
        return syncSendOrderly(topic, message, orderlyHash, rocketMqTemplate.getProducer().getSendMsgTimeout());
    }

    /**
     * 同步发送顺序消息
     * 自定义超时时间
     * @param topic 主题
     * @param message   消息
     * @param orderlyHash   保证顺序的hashKey
     * @param timeout   超时时间
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return org.apache.rocketmq.client.producer.SendResult
     **/
    public SendResult syncSendOrderly(String topic, Object message, String orderlyHash, long timeout) {
        return rocketMqTemplate.syncSendOrderly(topic, message, orderlyHash, timeout);
    }

    /**
     * 发送异步消息
     * 默认回调
     * @param topic 主题
     * @param message   消息
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public void asyncSend(String topic, String message) {
        asyncSend(topic, message, getDefaultSendCallBack());
    }

    /**
     * 发送异步消息
     * 自定义回调处理
     * @param topic 主题
     * @param message   消息
     * @param sendCallback  回调处理类
     * <AUTHOR>
     * @date 2023/4/6 17:18
     */
    public void asyncSend(String topic, String message, SendCallback sendCallback) {
        asyncSend(topic, message, sendCallback, rocketMqTemplate.getProducer().getSendMsgTimeout());
    }

    /**
     * 发送异步消息
     * @param topic 主题
     * @param message   消息
     * @param sendCallback  回调处理类
     * @param timeout   超时时间
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public void asyncSend(String topic, String message, SendCallback sendCallback, long timeout) {
        asyncSend(topic, message, sendCallback, timeout, 0);
    }

    /**
     * 发送异步消息
     * @param topic 主题
     * @param message   消息
     * @param timeout   超时时间
     * @param delayLevel   延迟消息的级别
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public void asyncSend(String topic, String message, long timeout, int delayLevel) {
        asyncSend(topic, message, getDefaultSendCallBack(), timeout, delayLevel);
    }

    /**
     * 发送异步消息
     *
     * @param topic        消息Topic
     * @param message      消息实体
     * @param sendCallback 回调函数
     * @param timeout      超时时间
     * @param delayLevel   延迟消息的级别
     * <AUTHOR>
     * @date 2023/4/6 17:18
     */
    public void asyncSend(String topic, Object message, SendCallback sendCallback, long timeout, int delayLevel) {
        rocketMqTemplate.asyncSend(topic, MessageBuilder.withPayload(message).build(), sendCallback, timeout, delayLevel);
    }


    /**
     * 异步发送顺序消息
     * @param topic 主题
     * @param message   消息
     * @param orderlyHash   保证顺序的hashKey
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public void asyncSendOrderly(String topic, Object message, String orderlyHash) {
        asyncSendOrderly(topic, message, orderlyHash, getDefaultSendCallBack());
    }

    /**
     * 异步发送顺序消息
     * 自定义超时时间
     * @param topic 主题
     * @param message   消息
     * @param orderlyHash   保证顺序的hashKey
     * @param timeout   超时时间
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public void asyncSendOrderly(String topic, Object message, String orderlyHash, long timeout) {
        asyncSendOrderly(topic, message, orderlyHash, getDefaultSendCallBack(), timeout);
    }

    /**
     * 异步发送顺序消息
     * 自定义回调函数
     * @param topic 主题
     * @param message   消息
     * @param orderlyHash   保证顺序的hashKey
     * @param sendCallback  异步回调处理类
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public void asyncSendOrderly(String topic, Object message, String orderlyHash, SendCallback sendCallback) {
        asyncSendOrderly(topic, message, orderlyHash, sendCallback, rocketMqTemplate.getProducer().getSendMsgTimeout());
    }

    /**
     * 异步发送顺序消息
     * 自定义超时时间
     * 慎用此方法，因为异步本身发送消息的时候就没有顺序，所以消费的时候的顺序，取决于发送消息时候对消息顺序的确定
     * @param topic 主题
     * @param message   消息
     * @param orderlyHash   保证顺序的hashKey
     * @param sendCallback  异步回调处理类
     * @param timeout   超时时间
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public void asyncSendOrderly(String topic, Object message, String orderlyHash, SendCallback sendCallback, long timeout) {
        rocketMqTemplate.asyncSendOrderly(topic, message, orderlyHash, sendCallback, timeout);
    }

    /**
     * 默认异步发送消息CallBack函数
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return org.apache.rocketmq.client.producer.SendCallback
     **/
    private SendCallback getDefaultSendCallBack() {
        return new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                LOGGER.info("---发送MQ成功---");
            }

            @Override
            public void onException(Throwable throwable) {
                LOGGER.error("---发送MQ失败---" + throwable.getMessage(), throwable.getMessage());
            }
        };
    }

    /**
     * 发送单向消息（不管发送是否成功）
     * @param topic 主题
     * @param message   消息
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public void sendOneWay(String topic, String message) {
        rocketMqTemplate.sendOneWay(topic, message);
    }

    /**
     * 发送单向消息（不管发送是否成功）
     * @param topic 主题
     * @param message   消息
     * @param orderlyHash   保证顺序的hashKey
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public void sendOneWayOrderly(String topic, String message, String orderlyHash) {
        rocketMqTemplate.sendOneWayOrderly(topic, message, orderlyHash);
    }
}

