package com.ce.scrm.customer.support.redis;

import com.ce.scrm.customer.cache.constant.CacheConstant;
import com.ce.scrm.customer.cache.lock.Lock;
import com.ce.scrm.customer.cache.lock.LockTypeEnum;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 分布式redis锁
 *
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
@Component
public class RedissonLock implements Lock {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(RedissonLock.class);

    @Resource
    private RedissonClient redissonClient;

    /**
     * redis锁前缀
     */
    private final static String REDIS_LOCK_PREFIX = CacheConstant.CACHE_PREFIX + CacheConstant.CACHE_KEY_SEPARATOR + "REDIS_LOCK_";

    /**
     * 获取当前操作锁方式的名称
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.lang.String
     **/
    @Override
    public String getName() {
        return LockTypeEnum.REDIS.getName();
    }

    /**
     * 获取分布式锁
     * @param lockName 锁名称
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    @Override
    public boolean lock(String lockName) {
        if (redissonClient == null) {
            return false;
        }
        RLock lock = redissonClient.getLock(REDIS_LOCK_PREFIX + lockName);
        lock.lock();
        return true;
    }

    /**
     * 获取锁
     * @param lockName 锁的key
     * @param timeoutTime 锁超时时间
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    @Override
    public boolean tryLock(String lockName, long waitTime, long timeoutTime) {
        if (redissonClient == null) {
            return false;
        }
        RLock lock = redissonClient.getLock(REDIS_LOCK_PREFIX + lockName);
        boolean lockStatus;
        try {
            lockStatus = lock.tryLock(waitTime, timeoutTime, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOGGER.error("[{}]加锁失败", REDIS_LOCK_PREFIX + lockName, e);
            return false;
        }
        return lockStatus;
    }

    /**
     * 释放分布式锁
     * @param lockName 锁名称
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    @Override
    public boolean unLock(String lockName) {
        RLock lock = redissonClient.getLock(REDIS_LOCK_PREFIX + lockName);
        if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
        return true;
    }
}