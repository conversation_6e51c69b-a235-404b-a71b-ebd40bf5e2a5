package com.ce.scrm.customer.support.xxl;

import cn.ce.xxljob.config.XxlAdminAddressConfig;
import cn.ce.xxljob.model.XxlJobInfo;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.support.constant.JobConstant;
import com.ce.scrm.customer.support.log.ExceptionMethod;
import com.ce.scrm.customer.util.constant.UtilConstant;
import com.xxl.job.core.glue.GlueTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * 任务相关操作
 * <AUTHOR>
 * @date 2023/4/6 20:20
 * @version 1.0.0
 */
@Component
public class JobTemplate {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(JobTemplate.class);

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ExecutorTemplate executorTemplate;

    @Resource
    private XxlAdminAddressConfig xxlAdminAddressConfig;

    @Value("${xxl.job.executor.author:admin}")
    private String author;

    @Value("${xxl.job.executor.alarmEmail:<EMAIL>}")
    private String alarmEmail;

    @Value("${xxl.job.executor.oneTimesJob.routeStrategy:FAILOVER}")
    private String executorRouteStrategy;

    @Value("${xxl.job.executor.oneTimesJob.blockStrategy:SERIAL_EXECUTION}")
    private String executorBlockStrategy;

    @Value("${xxl.job.executor.oneTimesJob.failRetryCount:1}")
    private int executorFailRetryCount;

    /**
     * 新增任务默认状态
     */
    private final static int JOB_STATUS = 1;

    /**
     * 添加任务（给一定的默认值，在文件中配置）
     * @param jobDesc   任务描述
     * @param cron  定时表达式
     * @param handler   任务处理名称
     * @param jobParam  任务参数
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return int
     **/
    @Retryable(value = Exception.class, maxAttempts = UtilConstant.MethodRetry.MAX_ATTEMPTS, backoff = @Backoff(delay = UtilConstant.MethodRetry.DELAY, multiplier = UtilConstant.MethodRetry.MULTIPLIER, maxDelay = UtilConstant.MethodRetry.MAX_DELAY))
    public int addJob(String jobDesc, String cron, String handler, String jobParam) {
        return addJob(null, jobDesc, executorRouteStrategy, cron, handler, executorBlockStrategy, null, 0, executorFailRetryCount, author, alarmEmail, jobParam, JOB_STATUS);
    }

    /**
     * 添加任务处理方法异常
     * @param exception 异常信息
     * <AUTHOR>
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return int
     **/
    @Recover
    public int addRecover(Exception exception, String jobDesc, String cron, String handler, String jobParam) {
        ExceptionMethod exceptionMethod = new ExceptionMethod();
        exceptionMethod.setMethodName(this.getClass().getName() + UtilConstant.DATA_SEPARATOR + "addJob");
        exceptionMethod.setMethodParam(jobDesc + UtilConstant.DATA_SEPARATOR + cron + UtilConstant.DATA_SEPARATOR + handler + UtilConstant.DATA_SEPARATOR + jobParam);
        exceptionMethod.setExceptionInfo(exception.getMessage());
        LOGGER.error("添加任务请求xxl-job异常，异常信息为:{}", JSON.toJSONString(exceptionMethod), exception);
        return -1;
    }

    /**
     * 更新任务（给一定的默认值，在文件中配置）
     * @param jobDesc   任务描述
     * @param cron  定时表达式
     * @param handler   任务处理名称
     * @param jobParam  任务参数
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return int
     **/
    @Retryable(value = Exception.class, maxAttempts = UtilConstant.MethodRetry.MAX_ATTEMPTS, backoff = @Backoff(delay = UtilConstant.MethodRetry.DELAY, multiplier = UtilConstant.MethodRetry.MULTIPLIER, maxDelay = UtilConstant.MethodRetry.MAX_DELAY))
    public boolean updateJob(int jobId, String jobDesc, String cron, String handler, String jobParam) {
        return updateJob(jobId, null, jobDesc, executorRouteStrategy, cron, handler, executorBlockStrategy, null, 0, executorFailRetryCount, author, alarmEmail, jobParam, JOB_STATUS);
    }

    /**
     * 开启任务
     * @param id    任务ID
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return int
     **/
    @Retryable(value = Exception.class, maxAttempts = UtilConstant.MethodRetry.MAX_ATTEMPTS, backoff = @Backoff(delay = UtilConstant.MethodRetry.DELAY, multiplier = UtilConstant.MethodRetry.MULTIPLIER, maxDelay = UtilConstant.MethodRetry.MAX_DELAY))
    public boolean startJob(int id) {
        String url = this.getAdminAddress() + JobConstant.JOB + JobConstant.JobOperateConstant.START + id;
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("开启定时任务的Id为:{}", id);
        }
        return restTemplate.getForObject(url, Integer.class) != null;
    }

    /**
     * 停止任务
     * @param id    任务ID
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return int
     **/
    @Retryable(value = Exception.class, maxAttempts = UtilConstant.MethodRetry.MAX_ATTEMPTS, backoff = @Backoff(delay = UtilConstant.MethodRetry.DELAY, multiplier = UtilConstant.MethodRetry.MULTIPLIER, maxDelay = UtilConstant.MethodRetry.MAX_DELAY))
    public boolean stopJob(int id) {
        String url = this.getAdminAddress() + JobConstant.JOB + JobConstant.JobOperateConstant.STOP + id;
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("停止定时任务的Id为:{}", id);
        }
        return restTemplate.getForObject(url, Integer.class) != null;
    }

    /**
     * 删除任务
     * @param id    任务ID
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return int
     **/
    @Retryable(value = Exception.class, maxAttempts = UtilConstant.MethodRetry.MAX_ATTEMPTS, backoff = @Backoff(delay = UtilConstant.MethodRetry.DELAY, multiplier = UtilConstant.MethodRetry.MULTIPLIER, maxDelay = UtilConstant.MethodRetry.MAX_DELAY))
    public boolean removeJob(int id) {
        String url = this.getAdminAddress() + JobConstant.JOB + JobConstant.JobOperateConstant.REMOVE + id;
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("删除定时任务的Id为:{}", id);
        }
        return restTemplate.getForObject(url, Integer.class) != null;
    }

    /**
     * 删除任务处理方法异常
     * @param exception 异常信息
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return int
     **/
    @Recover
    public boolean removeRecover(Exception exception, int id) {
        ExceptionMethod exceptionMethod = new ExceptionMethod();
        exceptionMethod.setMethodName(this.getClass().getName() + UtilConstant.DATA_SEPARATOR + "removeJob");
        exceptionMethod.setMethodParam(String.valueOf(id));
        exceptionMethod.setExceptionInfo(exception.getMessage());
        LOGGER.error("删除任务请求xxl-job异常，异常信息为:{}", JSON.toJSONString(exceptionMethod), exception);
        return false;
    }

    /**
     * 添加任务(全量参数)
     * @param executor  执行器
     * @param jobDesc   任务描述
     * @param routeStrategy 路由策略
     * @param cron  定时表达式
     * @param handler   任务处理名称
     * @param blockStrategy 阻塞处理策略
     * @param childJobId    子任务ID
     * @param timeout   任务超时时间
     * @param failRetryCount    失败重试次数
     * @param author    负责人
     * @param alarmEmail    报警邮件
     * @param jobParam  任务参数
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return int
     **/
    private int addJob(String executor, String jobDesc, String routeStrategy, String cron, String handler, String blockStrategy, String childJobId, int timeout, int failRetryCount, String author, String alarmEmail, String jobParam, int triggerStatus) {
        XxlJobInfo xxlJobInfo = new XxlJobInfo();
        xxlJobInfo.setJobGroup(executorTemplate.getJobGroup(executor).getId());
        xxlJobInfo.setJobDesc(jobDesc);
        xxlJobInfo.setExecutorRouteStrategy(routeStrategy);
        xxlJobInfo.setJobCron(cron);
        xxlJobInfo.setGlueType(GlueTypeEnum.BEAN.getDesc());
        xxlJobInfo.setExecutorHandler(handler);
        xxlJobInfo.setExecutorBlockStrategy(blockStrategy);
        xxlJobInfo.setChildJobId(childJobId);
        xxlJobInfo.setExecutorTimeout(timeout);
        xxlJobInfo.setExecutorFailRetryCount(failRetryCount);
        xxlJobInfo.setAuthor(author);
        xxlJobInfo.setAlarmEmail(alarmEmail);
        xxlJobInfo.setExecutorParam(jobParam);
        xxlJobInfo.setTriggerStatus(triggerStatus);
        String url = this.getAdminAddress() + JobConstant.JOB + JobConstant.JobOperateConstant.ADD;
        Integer jobId = restTemplate.postForObject(url, xxlJobInfo, Integer.class);
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("添加的定时任务ID为:{}", jobId);
        }
        return jobId == null ? -1 : jobId;
    }

    /**
     * 更新任务(全量参数)
     * @param jobId 任务ID
     * @param executor  执行器
     * @param jobDesc   任务描述
     * @param routeStrategy 路由策略
     * @param cron  定时表达式
     * @param handler   任务处理名称
     * @param blockStrategy 阻塞处理策略
     * @param childJobId    子任务ID
     * @param timeout   任务超时时间
     * @param failRetryCount    失败重试次数
     * @param author    负责人
     * @param alarmEmail    报警邮件
     * @param jobParam  任务参数
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return int
     **/
    private boolean updateJob(int jobId, String executor, String jobDesc, String routeStrategy, String cron, String handler, String blockStrategy, String childJobId, int timeout, int failRetryCount, String author, String alarmEmail, String jobParam, int triggerStatus) {
        XxlJobInfo xxlJobInfo = new XxlJobInfo();
        xxlJobInfo.setId(jobId);
        xxlJobInfo.setJobGroup(executorTemplate.getJobGroup(executor).getId());
        xxlJobInfo.setJobDesc(jobDesc);
        xxlJobInfo.setExecutorRouteStrategy(routeStrategy);
        xxlJobInfo.setJobCron(cron);
        xxlJobInfo.setGlueType(GlueTypeEnum.BEAN.getDesc());
        xxlJobInfo.setExecutorHandler(handler);
        xxlJobInfo.setExecutorBlockStrategy(blockStrategy);
        xxlJobInfo.setChildJobId(childJobId);
        xxlJobInfo.setExecutorTimeout(timeout);
        xxlJobInfo.setExecutorFailRetryCount(failRetryCount);
        xxlJobInfo.setAuthor(author);
        xxlJobInfo.setAlarmEmail(alarmEmail);
        xxlJobInfo.setExecutorParam(jobParam);
        xxlJobInfo.setTriggerStatus(triggerStatus);
        String url = this.getAdminAddress() + JobConstant.JOB + JobConstant.JobOperateConstant.UPDATE;
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("更新的定时任务ID为:{}", jobId);
        }
        return restTemplate.postForObject(url, xxlJobInfo, Integer.class) != null;
    }

    /**
     * 获取后台api地址
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return java.lang.String
     **/
    private String getAdminAddress() {
        return xxlAdminAddressConfig.getMasterAddress();
    }
}
