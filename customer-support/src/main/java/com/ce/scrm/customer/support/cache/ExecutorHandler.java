package com.ce.scrm.customer.support.cache;

import cn.ce.xxljob.config.XxlAdminAddressConfig;
import cn.ce.xxljob.model.XxlJobGroup;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ce.scrm.customer.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.customer.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.customer.support.constant.JobConstant;
import com.ce.scrm.customer.util.constant.UtilConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * 任务处理器
 * <AUTHOR>
 * @date 2023/4/6 20:20
 * @version 1.0.0
 */
@Service
public class ExecutorHandler extends AbstractStringCacheHandler<XxlJobGroup> {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(ExecutorHandler.class);

    @Resource
    private XxlAdminAddressConfig xxlAdminAddressConfig;

    @Resource
    private RestTemplate restTemplate;

    /**
     * 从xxl-job-admin获取执行器
     *
     * @param appName 执行器名称
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return CV
     **/
    @Override
    @Retryable(value = Exception.class, maxAttempts = UtilConstant.MethodRetry.MAX_ATTEMPTS, backoff = @Backoff(delay = UtilConstant.MethodRetry.DELAY, multiplier = UtilConstant.MethodRetry.MULTIPLIER, maxDelay = UtilConstant.MethodRetry.MAX_DELAY))
    public XxlJobGroup queryDataBySource(String appName) {
        String url = xxlAdminAddressConfig.getMasterAddress() + JobConstant.JOB + JobConstant.ExecutorOperateConstant.GET_APP_NAME + appName;
        String xxlJobGroupJson = restTemplate.getForObject(url, String.class);
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("xxlJobGroup json string >>>>>>>> {} ", xxlJobGroupJson);
        }
        return JSON.parseObject(xxlJobGroupJson, XxlJobGroup.class);
    }

    /**
     * 异常回调
     * @param appName   执行器名称
     * @param exception 异常信息
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return cn.ce.xxljob.model.XxlJobGroup
     **/
    @Recover
    public XxlJobGroup recover(Exception exception, String appName) {
        LOGGER.error("获取执行器http请求异常,执行器名称为:{},异常信息为:", appName, exception);
        return new XxlJobGroup();
    }


    /**
     * 获取业务缓存的key
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return com.ce.scrm.customer.cache.enumeration.CacheKeyEnum
     **/
    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.JOB_HANDLER_CACHE;
    }

    /**
     * 封装缓存value
     *
     * @param customerValue 自定义传入的value
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return java.lang.String
     **/
    @Override
    public String packageCacheValue(XxlJobGroup customerValue) {
        return JSON.toJSONString(customerValue, SerializerFeature.WriteClassName);
    }

    /**
     * 解析缓存数据
     *
     * @param cacheValue 缓存数据
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return CV
     **/
    @Override
    public XxlJobGroup parseCacheValue(String cacheValue) {
        return JSON.parseObject(cacheValue, XxlJobGroup.class);
    }

    /**
     * 指定对象类型
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return java.lang.Class<T>
     **/
    @Override
    protected Class<XxlJobGroup> getClazz() {
        return XxlJobGroup.class;
    }
}
