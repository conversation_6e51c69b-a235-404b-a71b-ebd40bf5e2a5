package com.ce.scrm.customer.support.xxl;

import cn.ce.xxljob.model.XxlJobGroup;
import com.ce.scrm.customer.support.cache.ExecutorHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 执行器相关操作
 * 待定是否需要动态创建执行器
 * <AUTHOR>
 * @date 2023/4/6 20:20
 * @version 1.0.0
 */
@Component
public class ExecutorTemplate {

    @Resource
    private ExecutorHandler executorHandler;

    @Value("${xxl.job.executor.appname}")
    private String appName;

    /**
     * 根据执行器名称获取相应的执行器
     * @param executor  执行器名称
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return cn.ce.xxljob.model.XxlJobGroup
     **/
    public XxlJobGroup getJobGroup(String executor) {
        if (StringUtils.isBlank(executor)) {
            executor = appName;
        }
        return executorHandler.get(executor);
    }
}
