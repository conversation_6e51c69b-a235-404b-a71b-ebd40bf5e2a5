package com.xxl.job.core.util;

import com.xxl.job.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

/**
 * 重写xxl-job远程调用工具类
 * <AUTHOR>
 * @date 2021/10/27 下午1:06
 * @version 1.0.0
 **/
@SuppressWarnings({"all"})
public class XxlJobRemotingUtil {
    private final static Logger LOGGER = LoggerFactory.getLogger(XxlJobRemotingUtil.class);
    public static final String XXL_JOB_ACCESS_TOKEN = "XXL-JOB-ACCESS-TOKEN";
    private static final TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }

        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }
    }};

    public XxlJobRemotingUtil() {
    }

    private static void trustAllHosts(HttpsURLConnection connection) {
        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init((KeyManager[]) null, trustAllCerts, new SecureRandom());
            SSLSocketFactory newFactory = sc.getSocketFactory();
            connection.setSSLSocketFactory(newFactory);
        } catch (Exception var3) {
            LOGGER.error(var3.getMessage(), var3);
        }

        connection.setHostnameVerifier(new HostnameVerifier() {
            @Override
            public boolean verify(String hostname, SSLSession session) {
                return true;
            }
        });
    }

    @SuppressWarnings("uncheck")
    public static ReturnT postBody(String url, String accessToken, int timeout, Object requestObj, Class returnTargClassOfT) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("执行重写后的 XxlJobRemotingUtil");
        }
        HttpURLConnection connection = null;
        BufferedReader bufferedReader = null;

        ReturnT var33;
        try {
            URL realUrl = new URL(url);
            connection = (HttpURLConnection) realUrl.openConnection();
            boolean useHttps = url.startsWith("https");
            if (useHttps) {
                HttpsURLConnection https = (HttpsURLConnection) connection;
                trustAllHosts(https);
            }

            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setReadTimeout(timeout * 5000);
            connection.setConnectTimeout(10000);
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            connection.setRequestProperty("Accept-Charset", "application/json;charset=UTF-8");
            if (accessToken != null && accessToken.trim().length() > 0) {
                connection.setRequestProperty("XXL-JOB-ACCESS-TOKEN", accessToken);
            }

            connection.connect();
            if (requestObj != null) {
                String requestBody = GsonTool.toJson(requestObj);
                DataOutputStream dataOutputStream = new DataOutputStream(connection.getOutputStream());
                dataOutputStream.write(requestBody.getBytes("UTF-8"));
                dataOutputStream.flush();
                dataOutputStream.close();
            }

            int statusCode = connection.getResponseCode();
            if (statusCode == 200) {
                bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder result = new StringBuilder();

                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    result.append(line);
                }

                String resultJson = result.toString();

                ReturnT var14;
                try {
                    ReturnT returnT = (ReturnT) GsonTool.fromJson(resultJson, ReturnT.class, returnTargClassOfT);
                    var14 = returnT;
                    return var14;
                } catch (Exception var27) {
                    LOGGER.error("xxl-rpc remoting (url=" + url + ") response content invalid(" + resultJson + ").", var27);
                    var14 = new ReturnT(500, "xxl-rpc remoting (url=" + url + ") response content invalid(" + resultJson + ").");
                    return var14;
                }
            }

            var33 = new ReturnT(500, "xxl-rpc remoting fail, StatusCode(" + statusCode + ") invalid. for url : " + url);
        } catch (Exception var28) {
            LOGGER.error(var28.getMessage(), var28);
            ReturnT var8 = new ReturnT(500, "xxl-rpc remoting error(" + var28.getMessage() + "), for url : " + url);
            return var8;
        } finally {
            try {
                if (bufferedReader != null) {
                    bufferedReader.close();
                }

                if (connection != null) {
                    connection.disconnect();
                }
            } catch (Exception var26) {
                LOGGER.error(var26.getMessage(), var26);
            }

        }

        return var33;
    }
}