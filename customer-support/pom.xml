<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>scrm-customer</artifactId>
        <groupId>com.ce.scrm.customer</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>customer-support</artifactId>
    <name>customer-support</name>
    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm.customer</groupId>
            <artifactId>customer-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm.customer</groupId>
            <artifactId>customer-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.omo.common</groupId>
            <artifactId>druid-jasypt-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.omo.common</groupId>
            <artifactId>spring-redis-nacos-switcher</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.omo.common</groupId>
            <artifactId>rocketmq-jasypt-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.sgroschupf</groupId>
            <artifactId>zkclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.omo</groupId>
            <artifactId>sequence-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.omo</groupId>
            <artifactId>sequence-api-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.omo.common</groupId>
            <artifactId>nacos-jasypt-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce</groupId>
            <artifactId>spring-boot-starter-xxljob</artifactId>
        </dependency>
    </dependencies>
</project>
