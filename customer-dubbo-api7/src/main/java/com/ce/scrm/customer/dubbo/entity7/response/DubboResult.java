package com.ce.scrm.customer.dubbo.entity7.response;

import java.io.Serializable;

/**
 * 返回包装体
 * <AUTHOR>
 * @date 2023/4/6 13:55
 * @version 1.0.0
 **/
public class DubboResult<T> implements Serializable {
    private static final long serialVersionUID = 6485342072648694248L;

    private String msg;
    private String code;
    private T data;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Boolean checkSuccess() {
        return DubboCodeMessageEnum.REQUEST_SUCCESS.getCode().equals(this.code);
    }
}
