package com.ce.scrm.customer.dubbo.api7;

import com.ce.scrm.customer.dubbo.entity7.dto.RocketTopicSwitchDubboDto;
import com.ce.scrm.customer.dubbo.entity7.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity7.view.RocketTopicSwitchView;

import java.util.List;


/**
 * rocket mq 切换开关
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/7/10 16:54
 */
public interface IRocketTopicSwitchDubbo {


    /**
     * 根据topicName 获取 开关值
     * @author:
     * @date: 2023/7/10 18:29
     * @param topicName
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Integer>
     */
    DubboResult<Integer> getTopicSwitchByTopicName(String topicName);

    /**
     * topics 列表查询
     * @author:wangshoufang
     * @date: 2023/7/10 16:59
     * @param rocketTopicSwitchDubboDto
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.customer.dubbo.entity.view.IndustryDubboView>>
     */
    DubboResult<List<RocketTopicSwitchView>> getTopicSwitchByConditation(RocketTopicSwitchDubboDto rocketTopicSwitchDubboDto);


    /**
     * 更新 rocketmq 开关
     * @author:
     * @date: 2023/7/10 17:04
     * @param rocketTopicSwitchDubboDto
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     */
    DubboResult<Boolean> update(RocketTopicSwitchDubboDto rocketTopicSwitchDubboDto);

}
