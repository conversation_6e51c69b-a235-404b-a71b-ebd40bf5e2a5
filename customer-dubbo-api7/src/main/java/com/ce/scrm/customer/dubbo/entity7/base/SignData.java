package com.ce.scrm.customer.dubbo.entity7.base;

import java.io.Serializable;

/**
 * 签名数据
 * <AUTHOR>
 * @date 2023/4/10 13:26
 * @version 1.0.0
 */
public class SignData implements Serializable {
    /**
     * 来源key
     */
    private String sourceKey;

    /**
     * 来源密钥
     */
    private String sourceSecret;

    /**
     * 随机串
     */
    private String randomStr;

    /**
     * 请求时间戳
     */
    private Long timestamp;

    /**
     * 签名
     */
    private String sign;

    public String getSourceKey() {
        return sourceKey;
    }

    public void setSourceKey(String sourceKey) {
        this.sourceKey = sourceKey;
    }

    public String getSourceSecret() {
        return sourceSecret;
    }

    public void setSourceSecret(String sourceSecret) {
        this.sourceSecret = sourceSecret;
    }

    public String getRandomStr() {
        return randomStr;
    }

    public void setRandomStr(String randomStr) {
        this.randomStr = randomStr;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}