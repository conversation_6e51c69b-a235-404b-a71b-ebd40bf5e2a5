package com.ce.scrm.customer.dubbo.entity7.response;

/**
 * dubbo返回码枚举
 * <AUTHOR>
 * @date 2023/4/6 13:55
 * @version 1.0.0
 **/
public enum DubboCodeMessageEnum {
    /**
     * 成功返回
     */
    REQUEST_SUCCESS("200", "请求成功"),

    /**
     * 服务器内部异常
     */
    SERVER_INTERNAL_EXCEPTION("10001", "网络超时，请稍后重试哦～"),

    /**
     * 来源渠道不存在
     */
    SOURCE_CHANNEL_NOT_EXIST("10002", "来源渠道异常，调用无效"),

    /**
     * 签名验证失败
     */
    SIGN_CHECK_FAIL("10003", "签名验证失败"),

    /**
     * 请求验证超时
     */
    SIGN_CHECK_TIMEOUT("10004", "当前请求验证超时"),

    /**
     * 返回结果为空
     */
    RETURN_NULL("10005", "返回结果为空"),

    /**
     * 操作失败
     */
    OPER_FAIL("10006", "操作失败"),

    /**
     * 无效参数
     */
    INVALID_PARAM ("10007", "无效参数"),

    /**
     * 出现异常
     */
    EXCEPTION("10008", "出现异常"),
    ;

    private final String code;
    private final String message;

    DubboCodeMessageEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
