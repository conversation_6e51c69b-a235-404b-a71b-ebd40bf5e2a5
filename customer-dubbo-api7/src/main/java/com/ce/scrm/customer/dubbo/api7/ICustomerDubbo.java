package com.ce.scrm.customer.dubbo.api7;


import com.ce.scrm.customer.dubbo.entity7.dto.CustomerAddDubboDto;
import com.ce.scrm.customer.dubbo.entity7.dto.CustomerDetailDubboDto;
import com.ce.scrm.customer.dubbo.entity7.dto.CustomerPageDubboDto;
import com.ce.scrm.customer.dubbo.entity7.dto.CustomerUpdateDubboDto;
import com.ce.scrm.customer.dubbo.entity7.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity7.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity7.view.CustomerAddDubboView;
import com.ce.scrm.customer.dubbo.entity7.view.CustomerDubboView;

/**
 * 客户dubbo接口
 * <AUTHOR>
 * @date 2023/4/12 16:54
 * @version 1.0.0
 */
public interface ICustomerDubbo {

    /**
     * 获取客户
     * @param customerDetailDubboDto  客户查询参数
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
     **/
    DubboResult<CustomerDubboView> detail(CustomerDetailDubboDto customerDetailDubboDto);

    /**
     * 分页查询客户信息
     * @param customerPageDubboDto 客户分页参数
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
     **/
    DubboResult<DubboPageInfo<CustomerDubboView>> pageList(CustomerPageDubboDto customerPageDubboDto);

    /**
     * 添加客户，给其他系统提供
     * @param customerAddDubboDto  客户数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.ContactPersonAddDubboView>
     **/
    DubboResult<CustomerAddDubboView> add(CustomerAddDubboDto customerAddDubboDto);

    /**
     * 更新联系人，给其他系统提供
     * @param customerUpdateDubboDto  联系人数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> update(CustomerUpdateDubboDto customerUpdateDubboDto);
}