package com.ce.scrm.customer.dubbo.entity7.view;

import java.io.Serializable;
import java.util.Date;

/**
 * rocket topic switch 返回对象
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/7/10 17:17
 */
public class RocketTopicSwitchView implements Serializable {

    /**
     * topic 名称
     */
    private String topic;

    /**
     * topic 状态
     * 0:rabbitmq
     * 1:rocketmq
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
