package com.ce.scrm.customer.dubbo.api7;

import com.ce.scrm.customer.dubbo.entity7.base.SignData;
import com.ce.scrm.customer.dubbo.entity7.dto.*;
import com.ce.scrm.customer.dubbo.entity7.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity7.view.*;

import java.util.List;

/**
 * 联系人dubbo接口
 * <AUTHOR>
 * @date 2023/4/12 16:54
 * @version 1.0.0
 */
public interface IContactPersonDubbo {

    /**
     * 获取客户联系人
     * @param customerDetailDubboDto  客户查询dto
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboView>>
     **/
    DubboResult<CustomerDubboView> customerContactDetail(CustomerDetailDubboDto customerDetailDubboDto);

    /**
     * 获取联系人数据，给其他系统提供
     * @param contactPersonDubboDto 查询参数
     * <AUTHOR>
     * @date 2023/5/15 10:14
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView>>
     **/
    DubboResult<List<ContactPersonDataDubboView>> getData(ContactPersonDubboDto contactPersonDubboDto);

    /**
     * 获取客户联系人数据，给其他系统提供
     * @param customerDetailDubboDto 查询参数
     * <AUTHOR>
     * @date 2023/5/15 10:14
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView>>
     **/
    DubboResult<List<ContactPersonDataDubboView>> getCustomerData(CustomerDetailDubboDto customerDetailDubboDto);

    /**
     * 添加联系人，给其他系统提供
     * @param contactPersonAddDubboDto  联系人数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.ContactPersonAddDubboView>
     **/
    DubboResult<ContactPersonAddDubboView> add(ContactPersonAddDubboDto contactPersonAddDubboDto);

    /**
     * 更新联系人，给其他系统提供
     * @param contactPersonUpdateDubboDto  联系人数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> update(ContactPersonUpdateDubboDto contactPersonUpdateDubboDto);

    /**
     * 更新联系人手机号是否已验证
     * <AUTHOR>
     * @date 2023/11/22 10:29
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity7.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> updatePhoneVerifiedFlag(ContactPersonUpdateDubboDto contactPersonUpdateDubboDto);

    /**
     * 根据所有区域数据
     * @param signData 签名数据
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.AreaDubboView>
     **/
    DubboResult<List<AreaDubboView>> getAllAreaData(SignData signData);

    /**
     * 添加默认标记
     * @param orderDefaultFlagDubboDto  打标参数
     * <AUTHOR>
     * @date 2023/5/23 09:43
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> addDefaultFlag(OrderDefaultFlagDubboDto orderDefaultFlagDubboDto);

    /**
     * 联系人绑定微信unionId
     * @param contactPersonBindUnionIdDubboDto  绑定参数
     * <AUTHOR>
     * @date 2023/6/16 09:51
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> bindUnionId(ContactPersonBindUnionIdDubboDto contactPersonBindUnionIdDubboDto);

    /**
     * 联系人解绑微信unionId
     * @param contactPersonUnbindUnionIdDubboDto    解绑参数
     * <AUTHOR>
     * @date 2023/6/16 09:54
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> unbindUnionId(ContactPersonUnbindUnionIdDubboDto contactPersonUnbindUnionIdDubboDto);

    /**
     * 根据绑定unionId获取联系人客户数据
     * @param bindUnionIdQueryDubboDto  查询参数
     * <AUTHOR>
     * @date 2023/6/16 10:28
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonCustomerDataDubboView>>
     **/
    DubboResult<List<ContactPersonCustomerDataDubboView>> getContactPersonDataByUnionId(BindUnionIdQueryDubboDto bindUnionIdQueryDubboDto);
}