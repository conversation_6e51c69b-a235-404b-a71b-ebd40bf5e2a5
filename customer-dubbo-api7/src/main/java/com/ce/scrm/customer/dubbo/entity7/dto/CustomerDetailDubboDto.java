package com.ce.scrm.customer.dubbo.entity7.dto;

import com.ce.scrm.customer.dubbo.entity7.base.SignData;

import java.io.Serializable;
import java.util.List;

/**
 * 客户详情参数
 * <AUTHOR>
 * @date 2023/4/12 16:48
 * @version 1.0.0
 */
public class CustomerDetailDubboDto extends SignData implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    private List<String> memberCodeList;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public List<String> getMemberCodeList() {
        return memberCodeList;
    }

    public void setMemberCodeList(List<String> memberCodeList) {
        this.memberCodeList = memberCodeList;
    }
}