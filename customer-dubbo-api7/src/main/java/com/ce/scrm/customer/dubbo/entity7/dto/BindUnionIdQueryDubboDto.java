package com.ce.scrm.customer.dubbo.entity7.dto;

import com.ce.scrm.customer.dubbo.entity7.base.SignData;

import java.io.Serializable;
import java.util.List;

/**
 * 绑定unionId查询参数
 * <AUTHOR>
 * @date 2023/5/23 09:41
 * @version 1.0.0
 **/
public class BindUnionIdQueryDubboDto extends SignData implements Serializable {

    /**
     * 微信unionId
     */
    private List<String> unionIdList;

    public List<String> getUnionIdList() {
        return unionIdList;
    }

    public void setUnionIdList(List<String> unionIdList) {
        this.unionIdList = unionIdList;
    }
}
