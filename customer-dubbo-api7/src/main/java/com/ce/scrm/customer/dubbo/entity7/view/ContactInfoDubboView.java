package com.ce.scrm.customer.dubbo.entity7.view;

import java.io.Serializable;
import java.util.Date;

/**
 * 联系方式数据
 * <AUTHOR>
 * @date 2023/4/12 17:37
 * @version 1.0.0
 */
public class ContactInfoDubboView implements Serializable {


    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 联系方式id
     */
    private String contactInfoId;

    /**
     * 联系类型：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
     */
    private Integer contactType;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 手机号标识：1、正常，2、黑名单，3、空号
     */
    private Integer phoneFlag;

    /**
     * 联系方式来源key
     */
    private String sourceKey;

    /**
     * 是否是签单人手机号：1、是，0、否
     */
    private Integer signatoryFlag;

    /**
     * 是否是300会员手机号：1、是，0、否
     */
    private Integer memberFlag;

    /**
     * 是否删除
     */
    private Integer deleteFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人
     */
    private Date updateTime;

    public Integer getContactType() {
        return contactType;
    }

    public void setContactType(Integer contactType) {
        this.contactType = contactType;
    }

    public String getContactWay() {
        return contactWay;
    }

    public void setContactWay(String contactWay) {
        this.contactWay = contactWay;
    }

    public Integer getPhoneFlag() {
        return phoneFlag;
    }

    public void setPhoneFlag(Integer phoneFlag) {
        this.phoneFlag = phoneFlag;
    }

    public String getSourceKey() {
        return sourceKey;
    }

    public void setSourceKey(String sourceKey) {
        this.sourceKey = sourceKey;
    }

    public Integer getSignatoryFlag() {
        return signatoryFlag;
    }

    public void setSignatoryFlag(Integer signatoryFlag) {
        this.signatoryFlag = signatoryFlag;
    }

    public Integer getMemberFlag() {
        return memberFlag;
    }

    public void setMemberFlag(Integer memberFlag) {
        this.memberFlag = memberFlag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getContactInfoId() {
        return contactInfoId;
    }

    public void setContactInfoId(String contactInfoId) {
        this.contactInfoId = contactInfoId;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getContactPersonId() {
        return contactPersonId;
    }

    public void setContactPersonId(String contactPersonId) {
        this.contactPersonId = contactPersonId;
    }
}