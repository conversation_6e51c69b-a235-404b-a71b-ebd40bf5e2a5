package com.ce.scrm.customer.dubbo.entity7.dto;

import com.ce.scrm.customer.dubbo.entity7.base.SignData;

import java.io.Serializable;

/**
 * 添加联系人参数
 * <AUTHOR>
 * @date 2023/5/15 11:23
 * @version 1.0.0
 **/
public class ContactPersonAddDubboDto extends SignData implements Serializable {

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 联系人名称
     */
    private String contactPersonName;

    /**
     * 性别：0、未知，1、男，2、女
     */
    private Number gender;

    /**
     * 部门
     */
    private String department;

    /**
     * 职位
     */
    private String position;

    /**
     * 证件类型：0、未知，1、身份证
     */
    private Number certificatesType;

    /**
     * 证件号码（证件类型非0，此字段不能为空）
     */
    private String certificatesNumber;

    /**
     * 省
     */
    private String provinceCode;

    /**
     * 市
     */
    private String cityCode;

    /**
     * 区
     */
    private String districtCode;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 手机号(多个手机号，逗号分隔)
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 微信
     */
    private String wechat;

    /**
     * 企业微信
     */
    private String wecome;

    /**
     * QQ
     */
    private String qq;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 手机号是否已验证 1是 0否
     */
    private Number phoneVerifiedFlag;

    /**
     * 是否是法人 1是 0否
     */
    private Number legalPersonFlag;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getContactPersonName() {
        return contactPersonName;
    }

    public void setContactPersonName(String contactPersonName) {
        this.contactPersonName = contactPersonName;
    }

    public Number getGender() {
        return gender;
    }

    public void setGender(Number gender) {
        this.gender = gender;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public Number getCertificatesType() {
        return certificatesType;
    }

    public void setCertificatesType(Number certificatesType) {
        this.certificatesType = certificatesType;
    }

    public String getCertificatesNumber() {
        return certificatesNumber;
    }

    public void setCertificatesNumber(String certificatesNumber) {
        this.certificatesNumber = certificatesNumber;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getWecome() {
        return wecome;
    }

    public void setWecome(String wecome) {
        this.wecome = wecome;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Number getPhoneVerifiedFlag() {
        return phoneVerifiedFlag;
    }

    public void setPhoneVerifiedFlag(Number phoneVerifiedFlag) {
        this.phoneVerifiedFlag = phoneVerifiedFlag;
    }

    public Number getLegalPersonFlag() {
        return legalPersonFlag;
    }

    public void setLegalPersonFlag(Number legalPersonFlag) {
        this.legalPersonFlag = legalPersonFlag;
    }
}
