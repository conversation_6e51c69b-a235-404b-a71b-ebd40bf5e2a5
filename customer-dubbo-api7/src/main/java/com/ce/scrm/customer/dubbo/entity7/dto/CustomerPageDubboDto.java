package com.ce.scrm.customer.dubbo.entity7.dto;

import com.ce.scrm.customer.dubbo.entity7.base.SignData;

import java.io.Serializable;
import java.util.List;

/**
 * 客户数据
 * <AUTHOR>
 * @date 2023/4/12 16:48
 * @version 1.0.0
 */
public class CustomerPageDubboDto extends SignData implements Serializable {

    /**
     * 客户id：C、企业客户标识，P、个人客户标识，Q、其他客户标识（历史客户ID保持不变）
     */
    private List<String> customerIdList;

    /**
     * 客户类型
     */
    private Integer customerType;

    /**
     * 元数据
     */
    private String sourceDataId;
    /**
     * 证件号码
     */
    private String certificateCode;
    /**
     * 证件类型
     */
    private String certificateType;

    /**
     * 客户/企业名称
     */
    private String customerName;

    /**
     * 创建开始时间（yyyy-MM-dd）
     */
    private String createTimeStart;

    /**
     * 创建结束时间（yyyy-MM-dd）
     */
    private String createTimeEnd;

    /**
     * 页号
     */
    private Integer pageNum = 1;

    /**
     * 页码
     */
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 是否正序
     */
    private boolean ascFlag;

    /**
     * 是否删除标记
     */
    private Integer deleteFlag;

    public List<String> getCustomerIdList() {
        return customerIdList;
    }

    public void setCustomerIdList(List<String> customerIdList) {
        this.customerIdList = customerIdList;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(String createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public String getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(String createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getSourceDataId() {
        return sourceDataId;
    }

    public void setSourceDataId(String sourceDataId) {
        this.sourceDataId = sourceDataId;
    }

    public String getCertificateCode() {
        return certificateCode;
    }

    public void setCertificateCode(String certificateCode) {
        this.certificateCode = certificateCode;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public boolean isAscFlag() {
        return ascFlag;
    }

    public void setAscFlag(boolean ascFlag) {
        this.ascFlag = ascFlag;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }
}