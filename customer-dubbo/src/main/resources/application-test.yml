spring:
  datasource:
    url: ********************************************************************************************************************************************************************************************************
    username: MYS<PERSON>(hs930YRVa7tBBmikUYezNw==)
    password: MYSQL(06NOLLrj3J7RK3LoWUJ5nczQkaXzG0MZ)
  redis:
    password: etRedis@019
    cluster:
      nodes: redis.et:7000,redis.et:7001,redis.et:7002

dubbo:
  registry:
    address: ************:2181,************:2181,************:2181

xxl:
  job:
    accessToken:
    admin:
      #调度中心地址
      addresses: http://test-omo.aiyouyi.cn/xxl-job-admin/
    executor:
      appname: scrm-customer-test
      address:
      ip:
      logpath: logs/xxl-job/jobhandler
      logretentiondays: 3
      oneTimesJob:
        timeout: 10000

rocketmq:
  name-server: ***********:9876;***********:9876
  producer:
    group: test
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)
  consumer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)

nacos:
  config:
    server-addr: common.et:8848
    username: NACOS(cIJ2K2dWtlO7IGs8Ui0KJA==)
    password: NACOS(hUNsVRXSd3byOedVM1Q9Aqs0scX3BBGQwp+gyzItewg=)
    namespace: test1

logging:
  config: classpath:logback-test1.xml

robot:
  force-alarm-address: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f0e9bc76-e26e-451a-baf5-0b601b80433d

sequence:
  zkAddress: ************
  zkPort: 2181

bigdata:
  enterpriseInfo: https://gatetest.datauns.cn/ce22highlevelsearch/highSearch/baseinfo/company_detail?company_name=

elasticsearch:
  hosts: ***********:9200