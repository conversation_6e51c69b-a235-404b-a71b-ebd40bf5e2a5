<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="true">
    <springProperty scope="context" name="providerName" source="spring.application.name"/>
    <springProperty scope="context" name="providerPort" source="server.port"/>
    <property name="console_log_pattern"
              value="${providerName}:${providerPort}- %d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%t] %C.%M:%L - [traceId:%X{traceId}] [sourceEmpId:%X{sourceEmpId}] %m%n"/>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${console_log_pattern}</pattern>
        </layout>
    </appender>
    <appender name="sendErrorMsgAppender" class="com.ce.scrm.customer.support.message.SendErrorMsgAppender"/>
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="sendErrorMsgAppender"/>
    </root>
    <logger name="cn.apache.dubbo" level="error" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="org.apache" level="error" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="cn.ce.common.redis.switcher" level="warn" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>
</configuration>