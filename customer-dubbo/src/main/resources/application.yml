server:
  port: 7921

spring:
  application:
    name: scrm-customer-dubbo
  profiles:
    active: dev
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      initial-size: 3
      min-idle: 3
      max-active: 50
      max-wait: 5000
      keep-alive: false
      remove-abandoned: true
      async-init: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 61000
      max-evictable-idle-time-millis: 25200000
      phy-timeout-millis: 25200000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 100
      max-open-prepared-statements: 100
      filters: config,wall,stat
      connection-properties: druid.stat.slowSqlMillis=500;druid.stat.logSlowSql=true;config.decrypt=false;
      use-global-data-source-stat: true
      use-unfair-lock: true
      stat-view-servlet:
        enabled: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  redis:
    nacos:
      config:
        dataId: scrm-customer-redis
        group: SCRM_GROUP
    database: 0
    timeout: 5000
    cluster:
      max-redirects: 6
    lettuce:
      pool:
        max-idle: 8
        min-idle: 0
        max-active: 1000
        max-wait: 15000
        time-between-eviction-runs: 1000

dubbo:
  config-center:
    timeout: 10000
  application:
    id: scrm-customer-dubbo
    name: scrm-customer-dubbo
  scan:
    base-packages: com.ce.scrm.customer.dubbo.service
  registry:
    protocol: zookeeper
    timeout: 10000
    group: dubbo-cesupport
  protocol:
    name: dubbo
    port: 29501
  consumer:
    check: false
    retries: 0
    timeout: 10000
    loadbalance: roundrobin
    filter: customDubboFilter
  provider:
    version: 1.0.0
    group: scrm-customer-api
    timeout: 10000
    loadbalance: roundrobin
    filter: customDubboFilter

xxl:
  job:
    executor:
      author: admin
      alarmEmail: <EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>
      port: 9471
      oneTimesJob:
        routeStrategy: FAILOVER
        blockStrategy: SERIAL_EXECUTION
        failRetryCount: 1

rocketmq:
  # 生产者配置
  producer:
    # 发送同一类消息的设置为同一个group，保证唯一
    group: CESUPPORT_SCRM_PRODUCER_GROUP
    # 消息最大长度 默认1024*64(4M)
    maxMessageSize: 65536
    # 发送消息超时时间,默认3000
    send-message-timeout: 3000
    # 发送消息失败重试次数
    retryTimesWhenSendFailed: 3

nacos:
  config:
    dataId: scrm-customer-dubbo
    group: SCRM_GROUP
    type: yaml
    auto-refresh: true
    remoteFirst: true
    bootstrap:
      enable: true
      logEnable: false

mybatis-plus:
  mapper-locations: classpath:mapper/*Mapper.xml
  type-aliases-package: com.ce.scrm.customer.dao.entity