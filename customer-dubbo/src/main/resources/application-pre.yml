spring:
  datasource:
    url: ********************************************************************************************************************************************************************************************************
    username: MYSQL(hs930YRVa7tBBmikUYezNw==)
    password: MYSQL(06NOLLrj3J7RK3LoWUJ5nczQkaXzG0MZ)
  redis:
    password: epRedis@019
    cluster:
      nodes: redis.ep:7000,redis.ep:7001,redis.ep:7002

dubbo:
  registry:
    address: ************:2181,************:2181,************:2181

xxl:
  job:
    accessToken:
    admin:
      #调度中心地址
      addresses: http://pre-omo.aiyouyi.cn/xxl-job-admin/
    executor:
      appname: scrm-customer-test
      address:
      ip:
      logpath: logs/xxl-job/jobhandler
      logretentiondays: 3
      oneTimesJob:
        timeout: 10000

# 业务平台
#rocketmq:
#  name-server: ************:9876
#  producer:
#    group: test
#    accessKey: b96c0783-1040-497e-93f7-c183768add07rfBd56
#    secretKey: c07bca4a-8a21-480b-a4a9-3ea49095cf4b
#  consumer:
#    accessKey: b96c0783-1040-497e-93f7-c183768add07rfBd56
#    secretKey: c07bca4a-8a21-480b-a4a9-3ea49095cf4b

# 商城
#商城
rocketmq:
  name-server: mq.ep:9876
  producer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)
  consumer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)

nacos:
  config:
    server-addr: common.et:8848
    username: NACOS(cIJ2K2dWtlO7IGs8Ui0KJA==)
    password: NACOS(hUNsVRXSd3byOedVM1Q9Aqs0scX3BBGQwp+gyzItewg=)
    namespace: test1

logging:
  config: classpath:logback-pre.xml

robot:
  force-alarm-address: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f0e9bc76-e26e-451a-baf5-0b601b80433d

sequence:
  zkAddress: ************
  zkPort: 2181

bigdata:
  enterpriseInfo: http://local-gateway.datauns.cn/ce22highlevelsearch/highSearch/baseinfo/company_detail?company_name=

elasticsearch:
  hosts: ***********:9200