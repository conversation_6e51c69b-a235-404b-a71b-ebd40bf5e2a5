package com.ce.scrm.customer.dubbo.aop.sign;

import cn.hutool.core.util.StrUtil;
import com.ce.scrm.customer.dubbo.entity.base.SignData;
import com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.service.cache.entity.ChannelSourceCacheData;
import com.ce.scrm.customer.service.cache.handler.ChannelSourceCacheHandler;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.util.constant.UtilConstant;
import com.ce.scrm.customer.util.sign.SignUtils;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;

/**
 * 签名认证切面
 * <AUTHOR>
 * @date 2023/4/10 11:37
 * @version 1.0.0
 */
@Order(3)
@Slf4j
@Aspect
@Component
public class SignAspect {
    /**
     * 请求超时时间
     */
    public final static int REQUEST_TIMEOUT_TIME = 30 * 60;

    @Resource
    private ChannelSourceCacheHandler channelSourceCacheHandler;

    /**
     * 定义签名认证切点
     * <AUTHOR>
     * @date 2023/4/6 20:48
     **/
    @Pointcut("@annotation(com.ce.scrm.customer.dubbo.aop.sign.Sign)")
    public void signPointCut() {
    }

    @Around("signPointCut()")
    public Object before(ProceedingJoinPoint joinPoint) throws Throwable {
        //获取签名参数数据
        SignData signData = getSignParam(joinPoint);
        if (StrUtil.isBlank(signData.getSourceKey())) {
            log.error("请求验证:Dubbo请求验证异常, 来源key不能为空");
            return DubboResult.error(MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME), DubboCodeMessageEnum.SIGN_CHECK_FAIL);
        }
        //添加来源cat监控
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        Transaction t = Cat.newTransaction("SOURCE-SERVICE", className + UtilConstant.CAT_SEPARATOR + methodName + UtilConstant.CAT_SEPARATOR + signData.getSourceKey());
        t.setStatus(Transaction.SUCCESS);
        Object result;
        try {
            DubboResult<?> dubboResult = keyCheck(signData);
            if (!dubboResult.checkSuccess()) {
                return dubboResult;
            }
            if ("EXACT_QUERY".equals(signData.getRandomStr())) {
                ServiceConstant.EXACT_QUERY_FLAG.set(true);
            } else {
                ServiceConstant.EXACT_QUERY_FLAG.set(false);
            }
            result = joinPoint.proceed();
        } catch (Throwable e) {
            t.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            ServiceConstant.EXACT_QUERY_FLAG.remove();
            t.complete();
        }
        return result;
    }

    /**
     * 仅校验密钥
     * @param signData 签名数据
     * <AUTHOR>
     * @date 2023/4/15 12:43
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<?>
     **/
    private DubboResult<?> keyCheck(SignData signData) {
        //获取签名的来源数据
        ChannelSourceCacheData channelSourceCacheData = channelSourceCacheHandler.get(signData.getSourceKey());
        if (channelSourceCacheData == null) {
            log.error("请求验证:Dubbo请求验证异常, 来源key不存在，传入key为:{}", signData.getSourceKey());
            return DubboResult.error(MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME), DubboCodeMessageEnum.SIGN_CHECK_FAIL);
        }
        String sourceSecret = "";
        String targetSecret = "";
        if (StrUtil.isBlank(sourceSecret = signData.getSourceSecret()) || !sourceSecret.equals(targetSecret = channelSourceCacheData.getSourceSecret())) {
            log.error("请求验证:Dubbo请求验证异常, 传入密钥为:{}, 实际密钥为:{}", sourceSecret, targetSecret);
            return DubboResult.error(MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME), DubboCodeMessageEnum.SIGN_CHECK_FAIL);
        }
        return DubboResult.success();
    }

    /**
     * 完整签名校验
     * @param signData 签名数据
     * @param joinPoint 切点数据
     * <AUTHOR>
     * @date 2023/4/15 12:39
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<?>
     **/
    private DubboResult<?> signCheck(SignData signData, ProceedingJoinPoint joinPoint) {
        //获取参数中的签名
        String requestSign = signData.getSign();
        //获取签名的来源数据
        ChannelSourceCacheData channelSourceCacheData = channelSourceCacheHandler.get(signData.getSourceKey());
        //来源渠道不存在
        if (channelSourceCacheData == null) {
            return DubboResult.error(MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME), DubboCodeMessageEnum.SOURCE_CHANNEL_NOT_EXIST);
        }
        //获取请求参数
        Map<String, String> paramMap = getParamMap(joinPoint, channelSourceCacheData);
        //获取请求路径
        String methodName = RpcContext.getContext().getMethodName();
        String interfaceName = RpcContext.getContext().getUrl().getPath();
        String requestPath = interfaceName + UtilConstant.CAT_SEPARATOR + methodName;
        //生成签名
        String sign = SignUtils.generateSign(requestPath, paramMap);
        //验证签名
        if (StrUtil.isBlank(sign) || !sign.equals(requestSign)) {
            log.error("请求验证:Dubbo请求验签异常, 生成签名为:{}, 请求签名为:{}", sign, requestSign);
            return DubboResult.error(MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME), DubboCodeMessageEnum.SIGN_CHECK_FAIL);
        }
        Long requestTimeStamp = signData.getTimestamp();
        //验证时间戳
        if (LocalDateTime.now().toEpochSecond(ZoneOffset.ofHours(UtilConstant.DEFAULT_ZONE_NUM)) - requestTimeStamp > REQUEST_TIMEOUT_TIME) {
            log.error("请求验证:Dubbo请求调用超时");
            return DubboResult.error(MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME), DubboCodeMessageEnum.SIGN_CHECK_TIMEOUT);
        }
        return DubboResult.success();
    }

    /**
     * 获取签名参数
     * @param joinPoint 方法切点
     * <AUTHOR>
     * @date 2023/4/10 13:36
     * @return com.ce.scrm.customer.service.config.sign.SignData
     **/
    public static SignData getSignParam(ProceedingJoinPoint joinPoint) {
        Object[] paramArray = joinPoint.getArgs();
        for (Object obj : paramArray) {
            if (obj instanceof SignData) {
                return (SignData) obj;
            }
        }
        throw new RuntimeException("请求参数需要包含签名类或者继承签名类");
    }

    /**
     * 获取请求的所有参数
     * @param joinPoint 切点
     * @param channelSourceCacheData 渠道来源数据
     * <AUTHOR>
     * @date 2023/4/10 14:27
     * @return java.util.Map<java.lang.String, java.lang.Object>
     **/
    private Map<String, String> getParamMap(ProceedingJoinPoint joinPoint, ChannelSourceCacheData channelSourceCacheData) {
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg != null) {
                //将密钥放入请求参数,并且去掉签名串，防止后续加入生成签名
                if (arg instanceof SignData) {
                    SignData signData = (SignData) arg;
                    signData.setSourceSecret(channelSourceCacheData.getSourceSecret());
                    signData.setSign(null);
                }
            }
        }
        return SignUtils.getParamMap(args);
    }
}