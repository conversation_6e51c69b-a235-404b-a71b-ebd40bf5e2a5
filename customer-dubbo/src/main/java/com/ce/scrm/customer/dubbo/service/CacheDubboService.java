package com.ce.scrm.customer.dubbo.service;

import cn.hutool.core.collection.CollectionUtil;
import com.ce.scrm.customer.cache.access.BaseManager;
import com.ce.scrm.customer.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.customer.cache.handler.manager.CacheHandlerManager;
import com.ce.scrm.customer.dubbo.api.ICacheDubbo;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 缓存dubbo接口
 * <AUTHOR>
 * @date 2023/6/25 09:56
 * @version 1.0.0
 */
@DubboService(interfaceClass = ICacheDubbo.class)
public class CacheDubboService implements ICacheDubbo {

    @Resource
    private CacheHandlerManager cacheHandlerManager;

    @Resource
    private BaseManager baseManager;

    /**
     * 获取单条缓存
     *
     * @param key 缓存key
     * <AUTHOR>
     * @date 2023/6/25 09:50
     * @return java.lang.String
     **/
    @Override
    public String get(String key) {
        return baseManager.getCache().get(key);
    }

    /**
     * 删除单条缓存
     *
     * @param key 缓存key
     * <AUTHOR>
     * @date 2023/6/25 09:54
     * @return java.lang.Boolean
     **/
    @Override
    public Boolean del(String key) {
        baseManager.getCache().del(key);
        return true;
    }

    /**
     * 模糊获取所有的key
     *
     * @param prefix 查询条件
     * <AUTHOR>
     * @date 2023/6/25 09:51
     * @return java.util.Set<java.lang.String>
     **/
    @Override
    public Set<String> getKeys(String prefix) {
        return baseManager.getCache().scan(prefix);
    }

    /**
     * 批量删除缓存
     *
     * @param prefix 查询条件
     * <AUTHOR>
     * @date 2023/6/25 09:55
     * @return java.lang.Boolean
     **/
    @Override
    public Boolean delKeys(String prefix) {
        Set<String> keySet = baseManager.getCache().scan(prefix);
        if (CollectionUtil.isEmpty(keySet)) {
            return false;
        }
        List<String> keys = new ArrayList<>(keySet);
        List<List<String>> subLists = new ArrayList<>();
        for (int i = 0; i < keys.size(); i += 10) {
            List<String> subList = keys.subList(i, Math.min(i + 10, keys.size()));
            subLists.add(subList);
        }
        subLists.forEach(keyList -> baseManager.getCache().batchDel(keyList));
        return !keySet.isEmpty();
    }

    /**
     * 获取缓存枚举类型
     *
     * <AUTHOR>
     * @date 2023/8/21 10:30
     * @return java.util.List<java.lang.String>
     **/
    @Override
    public List<String> getCacheEnumType() {
        return Arrays.stream(CacheKeyEnum.values()).map(CacheKeyEnum::name).collect(Collectors.toList());
    }

    /**
     * 获取缓存key
     *
     * @param keyType   缓存指定类型
     * @param serviceId 业务ID
     * <AUTHOR>
     * @date 2023/8/21 10:37
     * @return java.lang.String
     **/
    @Override
    public String getKey(String keyType, String serviceId) {
        return cacheHandlerManager.getCacheHandler(keyType).getKey(serviceId);
    }
}