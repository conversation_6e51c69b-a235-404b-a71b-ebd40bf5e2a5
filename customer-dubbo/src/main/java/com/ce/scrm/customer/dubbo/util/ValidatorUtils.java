package com.ce.scrm.customer.dubbo.util;

import com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Objects;
import java.util.Set;

@Slf4j
public class ValidatorUtils {
	private static final ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
	private static final Validator validator = factory.getValidator();

	/**
	 * 通用对象参数校验方法
	 *
	 * @param obj 需要校验的对象
	 * @param <T> 对象类型
	 * @return 校验结果 DubboResult
	 */
	public static <T> DubboResult<?> validate(T obj) {
		if (Objects.isNull(obj)) {
			return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "参数不能为空");
		}
		Set<ConstraintViolation<T>> violations = validator.validate(obj);

		if (!violations.isEmpty()) {
			StringBuilder sb = new StringBuilder();
			for (ConstraintViolation<T> violation : violations) {
				sb.append(violation.getMessage()).append("；");
			}
			return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, sb.toString());
		}

		return DubboResult.success();
	}
}