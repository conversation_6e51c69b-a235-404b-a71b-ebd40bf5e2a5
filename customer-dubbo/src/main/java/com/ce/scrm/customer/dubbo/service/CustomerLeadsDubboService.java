package com.ce.scrm.customer.dubbo.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.dao.entity.CustomerLeads;
import com.ce.scrm.customer.dao.service.abm.CustomerLeadsService;
import com.ce.scrm.customer.dubbo.api.ICustomerLeadsDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerLeadsAddDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerLeadsPageDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerLeadsDubboView;
import com.ce.scrm.customer.dubbo.util.PageUtil;
import com.ce.scrm.customer.dubbo.util.ValidatorUtils;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 客户leads dubbo业务
 * <AUTHOR>
 * @date 2023/4/13 14:50
 * @version 1.0.0
 */
@Slf4j
@DubboService(interfaceClass = ICustomerLeadsDubbo.class)
public class CustomerLeadsDubboService implements ICustomerLeadsDubbo {

    @Resource
    CustomerLeadsService customerLeadsService;


    /***
     * 添加单条leads
     * @param customerLeadsAddDubboDto
     * <AUTHOR>
     * @date 2025/7/17 10:44
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Long>
    **/
    @Override
    public DubboResult<Long> addCustomerLead(CustomerLeadsAddDubboDto customerLeadsAddDubboDto) {
        log.info("dubbo-添加 {}", JSON.toJSONString(customerLeadsAddDubboDto));
        DubboResult<?> validateRs = ValidatorUtils.validate(customerLeadsAddDubboDto);
        if (!validateRs.checkSuccess()) {
            return DubboResult.error(validateRs);
        }

        CustomerLeads entity = new CustomerLeads();
        BeanUtil.copyProperties(customerLeadsAddDubboDto, entity);
        boolean save = customerLeadsService.save(entity);
        if (!save) {
            log.error("添加leads失败 entity={}", JSON.toJSONString(entity));
            return DubboResult.error(DubboCodeMessageEnum.EXCEPTION,"添加leads失败");
        }else {
            Long id = entity.getId();
            log.info("生成 leads id={}", id);
            return DubboResult.success(id);
        }
    }

    /***
     * 获取集合
     * @param customerId
     * <AUTHOR>
     * @date 2025/7/17 10:54
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.customer.dubbo.entity.view.abm.CustomerLeadsDubboView>>
    **/
    @Override
    public DubboResult<List<CustomerLeadsDubboView>> getListByCustomerId(String customerId) {
        log.info("dubbo-获取集合 customerId={}", customerId);
        List<CustomerLeads> list = customerLeadsService.list(new LambdaQueryWrapper<CustomerLeads>()
                .eq(CustomerLeads::getCustomerId, customerId));
        if (CollectionUtils.isEmpty(list)) {
            return DubboResult.success();
        }else {
            List<CustomerLeadsDubboView> customerLeadsDubboViews = BeanUtil.copyToList(list, CustomerLeadsDubboView.class);
            return DubboResult.success(customerLeadsDubboViews);
        }
    }

	/**
	 * 获取leads分页
	 * @param customerLeadsPageDubboDto 客户id及分页信息
	 * @return leads pages
	 */
	@Override
	public DubboResult<DubboPageInfo<CustomerLeadsDubboView>> getPageByCustomerId(CustomerLeadsPageDubboDto customerLeadsPageDubboDto) {
		log.info("dubbo-getPageByCustomerId-获取分页 customerId={}", customerLeadsPageDubboDto);
		if (StringUtils.isBlank(customerLeadsPageDubboDto.getCustomerId())) {
			return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户id不能为空");
		}
		LambdaQueryWrapper<CustomerLeads> wrapper = new LambdaQueryWrapper<CustomerLeads>().eq(CustomerLeads::getCustomerId, customerLeadsPageDubboDto.getCustomerId());
		Page<CustomerLeads> page = Page.of(customerLeadsPageDubboDto.getPageNum(), customerLeadsPageDubboDto.getPageSize());

		Page<CustomerLeads> leadsPage = customerLeadsService.page(page, wrapper);
		if (CollectionUtils.isEmpty(leadsPage.getRecords())) {
			DubboPageInfo<CustomerLeadsDubboView> dubboPageInfo = new DubboPageInfo<>();
			dubboPageInfo.setPageNum(customerLeadsPageDubboDto.getPageNum());
			dubboPageInfo.setPageSize(customerLeadsPageDubboDto.getPageSize());
			return DubboResult.success(dubboPageInfo);
		}
		DubboPageInfo<CustomerLeadsDubboView> dubboPageInfoDubboPageInfo = PageUtil.pageConversion(leadsPage, CustomerLeadsDubboView.class);
		return DubboResult.success(dubboPageInfoDubboPageInfo);
	}
}
