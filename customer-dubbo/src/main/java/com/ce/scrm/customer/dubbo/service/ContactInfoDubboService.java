package com.ce.scrm.customer.dubbo.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.dubbo.aop.sign.Sign;
import com.ce.scrm.customer.dubbo.api.IContactInfoDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.ContactInfoDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.ContactInfoDubboView;
import com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboView;
import com.ce.scrm.customer.service.business.IContactInfoBusiness;
import com.ce.scrm.customer.service.business.IContactPersonBusiness;
import com.ce.scrm.customer.service.business.ICustomerContactBusiness;
import com.ce.scrm.customer.service.business.IOrderDefaultFlagBusiness;
import com.ce.scrm.customer.service.business.entity.dto.ContactInfoBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.ContactPersonBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.CustomerContactBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.ContactInfoBusinessView;
import com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView;
import com.ce.scrm.customer.service.business.entity.view.CustomerContactBusinessView;
import com.ce.scrm.customer.service.enums.ContactInfoEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 联系方式dubbo业务
 * <AUTHOR>
 * @date 2023/4/13 14:50
 * @version 1.0.0
 */
@Slf4j
@DubboService(interfaceClass = IContactInfoDubbo.class)
public class ContactInfoDubboService implements IContactInfoDubbo {

    @Resource
    private IContactInfoBusiness contactInfoBusiness;

    @Resource
    private IContactPersonBusiness contactPersonBusiness;

    @Resource
    private ICustomerContactBusiness customerContactBusiness;

    @Resource
    private SendMqService sendMqService;

    @Resource
    private IOrderDefaultFlagBusiness orderDefaultFlagBusiness;

    @Sign
    @Override
    public DubboResult<ContactPersonDubboView> detailByWay(ContactInfoDubboDto contactInfoDubboDto) {
        ContactInfoBusinessDto contactInfoBusinessDto = BeanUtil.copyProperties(contactInfoDubboDto, ContactInfoBusinessDto.class);
        contactInfoBusinessDto.setContactTypeList(contactInfoDubboDto.getContactTypeList());
        ContactInfoBusinessView contactInfoBusinessView = contactInfoBusiness.getCustomerContactInfoData(contactInfoBusinessDto);

        if (contactInfoBusinessView == null) {
            return DubboResult.success(null);
        }
        List<ContactInfoDubboView> contactInfoList = new ArrayList<>();

        ContactInfoDubboView contactInfoDubboView = BeanUtil.copyProperties(contactInfoBusinessView, ContactInfoDubboView.class);
        contactInfoList.add(contactInfoDubboView);
        String contactPersonId = contactInfoBusinessView.getContactPersonId();
        ContactPersonBusinessDto contactPersonBusinessDto = new ContactPersonBusinessDto();
        contactPersonBusinessDto.setContactPersonId(contactPersonId);
        ContactPersonBusinessView contactPersonBusinessView = contactPersonBusiness.detail(contactPersonBusinessDto);
        ContactPersonDubboView contactPersonDubboView = BeanUtil.copyProperties(contactPersonBusinessView, ContactPersonDubboView.class);

        if (contactPersonDubboView == null) {
            return DubboResult.success(null);
        }
        CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
        customerContactBusinessDto.setContactPersonId(contactPersonDubboView.getContactPersonId());
        CustomerContactBusinessView customerContactBusinessView = customerContactBusiness.customerContactByContactId(customerContactBusinessDto);
        if (customerContactBusinessView != null) {
            customerContactBusinessView.setCustomerId(contactPersonDubboView.getCustomerId());
            sendMqService.sendCheckValidCustomerMq("ContactInfoDubboService.detailByWay", Collections.singletonList(contactPersonDubboView.getCustomerId()));
        }

        contactPersonDubboView.setContactInfoList(contactInfoList);

        return DubboResult.success(contactPersonDubboView);
    }

    /**
     * 根据获取联系方式列表
     *
     * @param contactInfoDubboDto 参数
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboDto>>
     * <AUTHOR>
     * @date 2023/4/7 20:59
     **/
    @Sign
    @Override
    public DubboResult<List<ContactInfoDubboView>> detailList(ContactInfoDubboDto contactInfoDubboDto) {

        ContactInfoBusinessDto contactInfoBusinessDto = BeanUtil.copyProperties(contactInfoDubboDto, ContactInfoBusinessDto.class);
        contactInfoBusinessDto.setContactTypeList(contactInfoDubboDto.getContactTypeList());
        List<ContactInfoBusinessView> contactInfoBusinessViews = contactInfoBusiness.detailList(contactInfoBusinessDto);

        List<ContactInfoDubboView> contactInfoDubboViewList = Optional.of(contactInfoBusinessViews).orElse(Lists.newArrayList()).stream().map(record -> {
            ContactInfoDubboView contactInfoDubboView = new ContactInfoDubboView();
            BeanUtil.copyProperties(record, contactInfoDubboView);
            return contactInfoDubboView;
        }).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(contactInfoDubboViewList)) {
            return null;
        }

        return DubboResult.success(contactInfoDubboViewList);
    }


    @Sign
    @Override
    public DubboResult<List<ContactInfoDubboView>> contactPersonByContactWayList(ContactInfoDubboDto contactInfoDubboDto) {

        if(contactInfoDubboDto == null || CollectionUtils.isEmpty(contactInfoDubboDto.getContactWayList()) || contactInfoDubboDto.getContactType() == null){
            log.warn("根据联系方式列表查询联系人，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必要参数不能为空");
        }
        ContactInfoBusinessDto contactInfoBusinessDto = BeanUtil.copyProperties(contactInfoDubboDto, ContactInfoBusinessDto.class);
        contactInfoBusinessDto.setContactTypeList(contactInfoDubboDto.getContactTypeList());
        List<ContactInfoBusinessView> contactInfoBusinessViews = contactInfoBusiness.detailList(contactInfoBusinessDto);
        //log.warn("查询到的联系方式结果集：{}", JSON.toJSONString(contactInfoBusinessViews));
        List<ContactInfoDubboView> contactInfoDubboViewList = Optional.of(contactInfoBusinessViews).orElse(Lists.newArrayList()).stream().map(record -> {
            ContactInfoDubboView contactInfoDubboView = new ContactInfoDubboView();
            BeanUtil.copyProperties(record, contactInfoDubboView);
            // 联系人
            ContactPersonBusinessView contactPersonBusinessView = contactPersonBusiness.detail(contactInfoDubboView.getContactPersonId());
            if (orderDefaultFlagBusiness.check(contactInfoDubboView.getContactPersonId(), ContactInfoEnum.get(contactInfoBusinessDto.getContactType()), contactInfoDubboView.getContactWay())) {
                contactInfoDubboView.setSignatoryFlag(1);
            } else {
                contactInfoDubboView.setSignatoryFlag(0);
            }

            if(contactPersonBusinessView != null){
                if(!StringUtils.isEmpty(contactPersonBusinessView.getSourceKey()) && contactPersonBusinessView.getSourceKey().equals("member")){
                    contactInfoDubboView.setMemberFlag(1);
                } else {
                    contactInfoDubboView.setMemberFlag(0);
                }
            }
            return contactInfoDubboView;
        }).collect(Collectors.toList());
        //log.warn("过滤后的的联系方式结果集：{}", JSON.toJSONString(contactInfoDubboViewList));
        if (CollectionUtil.isEmpty(contactInfoDubboViewList)) {
            return DubboResult.success(null);
        }

        return DubboResult.success(contactInfoDubboViewList);
    }
}