package com.ce.scrm.customer.dubbo.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.customer.cache.access.BaseManager;
import com.ce.scrm.customer.cache.constant.CacheConstant;
import com.ce.scrm.customer.cache.store.Cache;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.service.mq.entity.ValidCustomerCheckDto;
import com.ce.scrm.customer.support.mq.RocketMqOperate;
import com.ce.scrm.customer.util.constant.UtilConstant;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 发送mq
 * <AUTHOR>
 * @date 2023/7/12 09:40
 * @version 1.0.0
 */
@Slf4j
@Service
public class SendMqService {

    @Resource
    private RocketMqOperate rocketMqOperate;

    @Resource
    private BaseManager baseManager;

    /**
     * 发送校验有效客户mq
     * @param method    调用方法
     * @param customerIdList    客户ID
     * <AUTHOR>
     * @date 2023/7/11 17:11
     **/
    public void sendCheckValidCustomerMq(String method, List<String> customerIdList) {
        Cache cache = baseManager.getCache();
        String checkValidCustomerSwitcher = cache.get(CacheConstant.CACHE_PREFIX + CacheConstant.CACHE_KEY_SEPARATOR + "CHECK_VALID_CUSTOMER_SWITCHER");
        if(StrUtil.isBlank(checkValidCustomerSwitcher) || "0".equals(checkValidCustomerSwitcher)){
            return;
        }
        ValidCustomerCheckDto validCustomerCheckDto = new ValidCustomerCheckDto();
        validCustomerCheckDto.setCustomerIdList(customerIdList);
        validCustomerCheckDto.setExactQueryFlag(ServiceConstant.EXACT_QUERY_FLAG.get());
        validCustomerCheckDto.setInvokeIp(RpcContext.getContext().getRemoteAddressString());
        validCustomerCheckDto.setInvokeMethod(method);
        validCustomerCheckDto.setInvokeTraceId(MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME));
        SendResult sendResult = rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CUSTOMER_TOPIC + ServiceConstant.MqConstant.TOPIC_TAG_SEPARATOR + ServiceConstant.MqConstant.Tag.CHECK_VALID_CUSTOMER_TAG, JSON.toJSONString(validCustomerCheckDto));
        if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
            log.error("发送校验有效客户消息失败,失败状态为:{},发送的消息为:{}", sendResult.getSendStatus(), JSON.toJSONString(validCustomerCheckDto));
        }
    }

    
    /**
     * 同步搜客宝状态
     * <AUTHOR>
     * @date 2024/2/26 11:23
     * @param customerIdList
     * @return void
     */
    public void sendSkbSyncFlagMq(List<String> customerIdList) {
        try {
            Optional.of(customerIdList).orElse(Lists.newArrayList()).stream().forEach(customerId -> {
                //发送更新flag7，flag8的mq
                SendResult sendSkbResult = rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CDP_CUS_INFO_FOR_FLAG_TOPIC, customerId);
                if (!SendStatus.SEND_OK.equals(sendSkbResult.getSendStatus())) {
                    log.error("发送更新flag7，flag8的mq消息失败,失败状态为:{},发送的消息为:{}", sendSkbResult.getSendStatus(), customerId);
                }
            });
        }catch (Exception e){
            log.error("发送更新flag7，flag8的mq消息失败,参数={}", JSONObject.toJSONString(customerIdList));
        }
    }


    public void sendWechatBindMq(String message) {
        try {
            SendResult sendSkbResult = rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CDP_CONTACTPERSON_BIND_UNIONID_TOPIC, message);
            if (!SendStatus.SEND_OK.equals(sendSkbResult.getSendStatus())) {
                log.error("联系人绑定微信事件发送消息失败，发送的消息为:{}", sendSkbResult.getSendStatus(), message);
            }
        }catch (Exception e){
            log.error("联系人绑定微信事件发送消息失败,发送的消息为={}", message);
        }
    }

}