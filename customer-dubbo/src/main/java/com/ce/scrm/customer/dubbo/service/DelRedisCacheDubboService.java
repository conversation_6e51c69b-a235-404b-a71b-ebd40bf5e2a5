package com.ce.scrm.customer.dubbo.service;

import cn.hutool.core.util.StrUtil;
import com.ce.scrm.customer.dubbo.api.IDelRedisCacheDubbo;
import com.ce.scrm.customer.service.cache.handler.ContactPersonCacheHandler;
import com.ce.scrm.customer.service.cache.handler.CustomerCacheHandler;
import com.ce.scrm.customer.service.cache.handler.CustomerContactCacheHandler;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@DubboService(interfaceClass = IDelRedisCacheDubbo.class)
public class DelRedisCacheDubboService implements IDelRedisCacheDubbo {

    @Resource
    private CustomerCacheHandler customerCacheHandler;

    @Resource
    private CustomerContactCacheHandler customerContactCacheHandler;

    @Resource
    private ContactPersonCacheHandler contactPersonCacheHandler;

    @Override
    public void delCustomerCacheById(String customerId) {
        if (StringUtils.isNotEmpty(customerId)) {
            customerCacheHandler.del(customerId);
        }
    }

    /**
     * 删除客户联系人缓存
     * @param customerId    客户ID
     * <AUTHOR>
     * @date 2024/4/2 16:20
     **/
    @Override
    public void delCustomerContactCacheById(String customerId) {
        if (StrUtil.isBlank(customerId)) {
            return;
        }
        customerContactCacheHandler.del(customerId);
    }

    @Override
    public void delContactCacheById(String contactPersonId) {
        if (StringUtils.isNotEmpty(contactPersonId)) {
            contactPersonCacheHandler.del(contactPersonId);
        }
    }
}
