package com.ce.scrm.customer.dubbo.service7;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ce.scrm.customer.dubbo.api7.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.entity7.dto.CustomerAddDubboDto;
import com.ce.scrm.customer.dubbo.entity7.dto.CustomerDetailDubboDto;
import com.ce.scrm.customer.dubbo.entity7.dto.CustomerPageDubboDto;
import com.ce.scrm.customer.dubbo.entity7.dto.CustomerUpdateDubboDto;
import com.ce.scrm.customer.dubbo.entity7.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity7.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity7.view.ContactPersonDubboView;
import com.ce.scrm.customer.dubbo.entity7.view.CustomerAddDubboView;
import com.ce.scrm.customer.dubbo.entity7.view.CustomerDubboView;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 客户dubbo业务
 * <AUTHOR>
 * @date 2023/4/13 14:50
 * @version 1.0.0
 */
@DubboService(interfaceClass = ICustomerDubbo.class)
public class Customer7DubboService implements ICustomerDubbo {

    @Resource
    private com.ce.scrm.customer.dubbo.api.ICustomerDubbo customerDubbo;

    /**
     * 获取客户数据
     *
     * @param customerDetailDubboDto 查询参数
     * <AUTHOR>
     * @date 2023/4/13 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.DemoDubboView>
     **/
    @Override
    public DubboResult<CustomerDubboView> detail(CustomerDetailDubboDto customerDetailDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView> dubboViewDubboResult = customerDubbo.detail(BeanUtil.copyProperties(customerDetailDubboDto, com.ce.scrm.customer.dubbo.entity.dto.CustomerDetailDubboDto.class));
        if (dubboViewDubboResult.checkSuccess()) {
            com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView customerDubboView;
            DubboResult<CustomerDubboView> dubboResult = BeanUtil.copyProperties(dubboViewDubboResult, DubboResult.class);
            if ((customerDubboView = dubboViewDubboResult.getData()) != null) {
                CustomerDubboView customerDubboViewNew = BeanUtil.copyProperties(customerDubboView, CustomerDubboView.class);
                List<com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboView> contactPersonDubboViewList;
                if (CollectionUtil.isNotEmpty(contactPersonDubboViewList = customerDubboView.getContactPersonDubboViewList())) {
                    List<ContactPersonDubboView> contactPersonDubboViewListNew = new ArrayList<>();
                    contactPersonDubboViewList.forEach(contactPersonDubboView -> {
                        ContactPersonDubboView contactPersonDubboViewNew = BeanUtil.copyProperties(contactPersonDubboView, ContactPersonDubboView.class);
                        contactPersonDubboViewNew.setCreateTime(Date.from(contactPersonDubboView.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
                        contactPersonDubboViewNew.setUpdateTime(Date.from(contactPersonDubboView.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
                        contactPersonDubboViewListNew.add(contactPersonDubboViewNew);
                    });
                    customerDubboViewNew.setContactPersonDubboViewList(contactPersonDubboViewListNew);
                }
                customerDubboViewNew.setCreateTime(Date.from(customerDubboView.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
                dubboResult.setData(customerDubboViewNew);
                return dubboResult;
            }
        }
        DubboResult<CustomerDubboView> dubboResult = new DubboResult<>();
        dubboResult.setCode(dubboViewDubboResult.getCode());
        dubboResult.setMsg(dubboViewDubboResult.getMsg());
        return dubboResult;
    }

    @Override
    public DubboResult<DubboPageInfo<CustomerDubboView>> pageList(CustomerPageDubboDto customerPageDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>> dubboViewDubboResult = customerDubbo.pageList(BeanUtil.copyProperties(customerPageDubboDto, com.ce.scrm.customer.dubbo.entity.dto.CustomerPageDubboDto.class));
        if (dubboViewDubboResult.checkSuccess()) {
            DubboResult<DubboPageInfo<CustomerDubboView>> dubboResult = BeanUtil.copyProperties(dubboViewDubboResult, DubboResult.class);
            com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView> dubboPageInfo;
            if ((dubboPageInfo = dubboViewDubboResult.getData()) != null) {
                DubboPageInfo<CustomerDubboView> dubboPageInfoNew = BeanUtil.copyProperties(dubboPageInfo, DubboPageInfo.class);
                List<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView> customerDubboViewList;
                if (CollectionUtil.isNotEmpty(customerDubboViewList = dubboPageInfo.getList())) {
                    List<CustomerDubboView> customerDubboViewListNew = new ArrayList<>();
                    customerDubboViewList.forEach(customerDubboView -> {
                        CustomerDubboView customerDubboViewNew = BeanUtil.copyProperties(customerDubboView, CustomerDubboView.class);
                        List<com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboView> contactPersonDubboViewList;
                        if (CollectionUtil.isNotEmpty(contactPersonDubboViewList = customerDubboView.getContactPersonDubboViewList())) {
                            List<ContactPersonDubboView> contactPersonDubboViewListNew = new ArrayList<>();
                            contactPersonDubboViewList.forEach(contactPersonDubboView -> {
                                ContactPersonDubboView contactPersonDubboViewNew = BeanUtil.copyProperties(contactPersonDubboView, ContactPersonDubboView.class);
                                contactPersonDubboViewNew.setCreateTime(Date.from(contactPersonDubboView.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
                                contactPersonDubboViewNew.setUpdateTime(Date.from(contactPersonDubboView.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
                                contactPersonDubboViewListNew.add(contactPersonDubboViewNew);
                            });
                            customerDubboViewNew.setContactPersonDubboViewList(contactPersonDubboViewListNew);
                        }
                        customerDubboViewNew.setCreateTime(Date.from(customerDubboView.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
                        customerDubboViewListNew.add(customerDubboViewNew);
                    });
                    dubboPageInfoNew.setList(customerDubboViewListNew);
                }
                dubboResult.setData(dubboPageInfoNew);
            }
            return dubboResult;
        }
        DubboResult<DubboPageInfo<CustomerDubboView>> dubboResult = new DubboResult<>();
        dubboResult.setCode(dubboViewDubboResult.getCode());
        dubboResult.setMsg(dubboViewDubboResult.getMsg());
        return dubboResult;
    }

    /**
     * 添加客户，给其他系统提供
     *
     * @param customerAddDubboDto 客户数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.ContactPersonAddDubboView>
     **/
    @Override
    public DubboResult<CustomerAddDubboView> add(CustomerAddDubboDto customerAddDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerAddDubboView> customerAddDubboViewDubboResult = customerDubbo.add(BeanUtil.copyProperties(customerAddDubboDto, com.ce.scrm.customer.dubbo.entity.dto.CustomerAddDubboDto.class));
        if (customerAddDubboViewDubboResult.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(customerAddDubboViewDubboResult), new TypeReference<DubboResult<CustomerAddDubboView>>() {
            });
        }
        DubboResult<CustomerAddDubboView> dubboResult = new DubboResult<>();
        dubboResult.setCode(customerAddDubboViewDubboResult.getCode());
        dubboResult.setMsg(customerAddDubboViewDubboResult.getMsg());
        return dubboResult;
    }

    /**
     * 更新客户，给其他系统提供
     *
     * @param customerUpdateDubboDto 客户数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Override
    public DubboResult<Boolean> update(CustomerUpdateDubboDto customerUpdateDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<Boolean> customerUpdateDubboResult = customerDubbo.update(BeanUtil.copyProperties(customerUpdateDubboDto, com.ce.scrm.customer.dubbo.entity.dto.CustomerUpdateDubboDto.class));
        if (customerUpdateDubboResult.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(customerUpdateDubboResult), new TypeReference<DubboResult<Boolean>>() {
            });
        }
        DubboResult<Boolean> dubboResult = new DubboResult<>();
        dubboResult.setCode(customerUpdateDubboResult.getCode());
        dubboResult.setMsg(customerUpdateDubboResult.getMsg());
        return dubboResult;
    }
}