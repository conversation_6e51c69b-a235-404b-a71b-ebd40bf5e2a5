package com.ce.scrm.customer.dubbo.service.sync.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信绑定
 * <AUTHOR>
 * @description
 * @date 2024/12/3
 */
@Data
public class WechatBindData implements Serializable {

    /**
     * 1:新增
     * 2:删除
     */
    private Integer type;

    /**
     * skb pid
     */
    private String pid;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 联系人ID
     */
    private String contactPersonId;

    /**
     * 联系人名称
     */
    private String contactPersonName;

    /**
     * 绑定类型：0、无，1、微信，2、企业微信
     */
    private Integer bindType;

    /**
     * 微信unionId
     */
    private String unionId;

    /**
     * 微信昵称
     */
    private String wechatNickName;

    /**
     * 操作人
     */
    private String operator;


    /**
     * 操作时间
     */
    private Date operatorTime;
}
