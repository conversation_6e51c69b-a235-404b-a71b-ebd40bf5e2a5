package com.ce.scrm.customer.dubbo.service.sync.entity;

import lombok.Data;

@Data
public class ContactPersonData {
    private String customerId;
    private String contactPersonId;
    private String contactPersonName;
    private String gender;
    private String department;
    private String position;
    private String certificatesType;
    private String certificatesNumber;
    private String provinceCode;
    private String cityCode;
    private String districtCode;
    private String address;
    private String sourceKey;
    private String sourceTag;
    private String deleteFlag;
    private String createTime;
    private String creatorKey;
    private String creator;
    private String updateTime;
    private String operatorKey;
    private String operator;
    private String remarks;
    private String memberCode;
}
