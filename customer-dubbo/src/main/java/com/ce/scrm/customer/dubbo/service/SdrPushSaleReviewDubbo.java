package com.ce.scrm.customer.dubbo.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.dubbo.api.ISdrPushSaleReviewDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.abm.SdrPushSaleReviewDubboAddDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.SdrPushSaleReviewDubboUpdateDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.abm.SdrPushSaleReviewDubboView;
import com.ce.scrm.customer.dubbo.util.ValidatorUtils;
import com.ce.scrm.customer.service.business.ISdrPushSaleReviewBusiness;
import com.ce.scrm.customer.service.business.entity.dto.abm.SdrPushSaleReviewBusinessAddDto;
import com.ce.scrm.customer.service.business.entity.dto.abm.SdrPushSaleReviewBusinessUpdateDto;
import com.ce.scrm.customer.service.business.entity.view.abm.SdrPushSaleReviewBusinessViewDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 跨境ABM sdr 推送销售 审核接口实现
 */
@Slf4j
@DubboService(interfaceClass = ISdrPushSaleReviewDubbo.class)
public class SdrPushSaleReviewDubbo implements ISdrPushSaleReviewDubbo {

    @Resource
    ISdrPushSaleReviewBusiness sdrPushSaleReviewBusiness;

    /***
     * 添加审核记录
     * @param reviewDubboAddDto
     * <AUTHOR>
     * @date 2025/7/16 19:26
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.String>
     **/
    @Override
    public DubboResult<Long> add(SdrPushSaleReviewDubboAddDto reviewDubboAddDto) {

        log.info("添加审核记录 dubbo={}", JSON.toJSONString(reviewDubboAddDto));
        DubboResult<?> validateRs = ValidatorUtils.validate(reviewDubboAddDto);
        if (!validateRs.checkSuccess()) {
            return DubboResult.error(validateRs);
        }
        Long countToBeReview = sdrPushSaleReviewBusiness.countToBeReview(reviewDubboAddDto.getCustomerId());
        if (countToBeReview > 0) {
            return DubboResult.error(DubboCodeMessageEnum.EXCEPTION, "添加审核记录失败,当前该客户下已存在待审核的记录");
        }

        SdrPushSaleReviewBusinessAddDto reviewBusinessAddDto = new SdrPushSaleReviewBusinessAddDto();
        BeanUtils.copyProperties(reviewDubboAddDto, reviewBusinessAddDto);
        boolean add = sdrPushSaleReviewBusiness.add(reviewBusinessAddDto);
        if (!add) {
            return DubboResult.error(DubboCodeMessageEnum.EXCEPTION, "添加审核记录失败");
        } else {
            Long id = reviewBusinessAddDto.getId();
            log.info("产生审核id={}", id);
            return DubboResult.success(id);
        }
    }

    /***
     * 获取审核记录
     * @param customerId
     * <AUTHOR>
     * @date 2025/7/16 19:35
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.abm.SdrPushSaleReviewDubboView>
     **/
    @Override
    public DubboResult<SdrPushSaleReviewDubboView> getToBeReview(String customerId) {
        log.info("获取审核记录 customerId={}", customerId);
        Optional<SdrPushSaleReviewBusinessViewDto> toBeReview = sdrPushSaleReviewBusiness.getToBeReview(customerId);
        if (toBeReview.isPresent()) {
            SdrPushSaleReviewBusinessViewDto sdrPushSaleReviewBusinessViewDto = toBeReview.get();
            SdrPushSaleReviewDubboView sdrPushSaleReviewDubboView = BeanUtil.copyProperties(sdrPushSaleReviewBusinessViewDto, SdrPushSaleReviewDubboView.class);
            return DubboResult.success(sdrPushSaleReviewDubboView);
        } else {
            return DubboResult.success();
        }

    }

    /***
     * 审核
     * @param reviewDubboUpdateDto
     * <AUTHOR>
     * @date 2025/7/16 19:37
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.String>
     **/
    @Override
    public DubboResult<String> review(SdrPushSaleReviewDubboUpdateDto reviewDubboUpdateDto) {
        log.info("审核 : {}", JSON.toJSONString(reviewDubboUpdateDto));
        DubboResult<?> validateRs = ValidatorUtils.validate(reviewDubboUpdateDto);
        if (!validateRs.checkSuccess()) {
            return DubboResult.error(validateRs);
        }

        SdrPushSaleReviewBusinessUpdateDto reviewBusinessUpdateDto = new SdrPushSaleReviewBusinessUpdateDto();
        BeanUtils.copyProperties(reviewDubboUpdateDto, reviewBusinessUpdateDto);
        Optional<String> review = sdrPushSaleReviewBusiness.review(reviewBusinessUpdateDto);
        return review.<DubboResult<String>>map(s -> DubboResult.error(DubboCodeMessageEnum.EXCEPTION, s)).orElseGet(DubboResult::success);
    }
}
