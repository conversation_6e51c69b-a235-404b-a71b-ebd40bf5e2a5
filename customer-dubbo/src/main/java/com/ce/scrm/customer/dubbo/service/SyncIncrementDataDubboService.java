package com.ce.scrm.customer.dubbo.service;

import cn.ce.sequence.api.request.BusinessTypeEnum;
import cn.ce.sequence.api.request.SequenceIdRequest;
import cn.ce.sequence.api.service.SequenceService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.ce.scrm.customer.dao.entity.*;
import com.ce.scrm.customer.dao.service.*;
import com.ce.scrm.customer.dubbo.api.ISyncIncrementDataDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.IncrementSyncDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.service.sync.entity.BigDataConfig;
import com.ce.scrm.customer.dubbo.service.sync.entity.ContactInfoData;
import com.ce.scrm.customer.dubbo.service.sync.entity.ContactPersonData;
import com.ce.scrm.customer.dubbo.service.sync.entity.CustomerData;
import com.ce.scrm.customer.service.business.ICustomerBusiness;
import com.ce.scrm.customer.service.business.IIndustryBusiness;
import com.ce.scrm.customer.service.business.entity.dto.CustomerBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView;
import com.ce.scrm.customer.service.cache.handler.ContactPersonCacheHandler;
import com.ce.scrm.customer.service.cache.handler.CustomerCacheHandler;
import com.ce.scrm.customer.service.cache.handler.CustomerContactCacheHandler;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import com.ce.scrm.customer.service.third.entity.view.BigDataCompanyDetail;
import com.ce.scrm.customer.service.third.invoke.BigDataThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 增量同步数据
 * <AUTHOR>
 * @date 2023/4/23 16:11
 **/
@Slf4j
@DubboService(interfaceClass = ISyncIncrementDataDubbo.class)
public class SyncIncrementDataDubboService implements ISyncIncrementDataDubbo {

    @Resource
    private AreaService areaService;

    @Resource
    private IndustryService industryService;

    @Resource
    private BigDataConfig bigDataConfig;

    @Resource
    private CustomerService customerService;

    @Resource
    private ContactPersonService contactPersonService;

    @Resource
    private CustomerContactService customerContactService;

    @Resource
    private ContactInfoService contactInfoService;

    @Resource
    private SequenceService sequenceService;

    @Resource
    private CustomerCacheHandler customerCacheHandler;

    @Resource
    private ContactPersonCacheHandler contactPersonCacheHandler;

    @Resource
    private CustomerContactCacheHandler customerContactCacheHandler;

    @Resource
    private SendMqService sendMqService;

    @Resource
    private IIndustryBusiness industryBusiness;

    @Resource
    private ICustomerBusiness customerBusiness;

    @Resource
    private BigDataThirdService bigDataThirdService;

    /**
     * 删除数据
     * @param dataType  数据类型：1、根据联系人ID删除联系方式，2、根据联系方式ID删除联系方式
     * @param data  同步数据
     * <AUTHOR>
     * @date 2023/4/26 14:16
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Override
    public DubboResult<Boolean> delete(Integer dataType, String data) {
        if (dataType == null || StrUtil.isBlank(data)) {
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM);
        }
        LambdaUpdateChainWrapper<ContactInfo> contactInfoLambdaUpdateChainWrapper = contactInfoService.lambdaUpdate();
        String customerId = null;
        String contactPersonId = null;
        if (dataType == 1) {
            contactPersonId = data;
            contactInfoLambdaUpdateChainWrapper.eq(ContactInfo::getContactPersonId, data);
        } else if (dataType == 2) {
            ContactInfo contactInfo = contactInfoService.lambdaQuery().eq(ContactInfo::getContactInfoId, data).one();
            if (contactInfo != null) {
                contactPersonId = contactInfo.getContactPersonId();
            }
            contactInfoLambdaUpdateChainWrapper.eq(ContactInfo::getContactPersonId, data);
        } else {
            return DubboResult.error(DubboCodeMessageEnum.OPER_FAIL, "删除类型有误");
        }
        boolean update = contactInfoLambdaUpdateChainWrapper.set(ContactInfo::getDeleteFlag, 1).update();
        if (StrUtil.isNotBlank(contactPersonId)) {
            CustomerContact customerContact = customerContactService.lambdaQuery().eq(CustomerContact::getContactPersonId, contactPersonId).one();
            if (customerContact != null) {
                customerId = customerContact.getCustomerId();
            }
            contactPersonCacheHandler.del(contactPersonId);
        }
        if (StrUtil.isNotBlank(customerId)) {
            customerContactCacheHandler.del(customerId);
            sendMqService.sendCheckValidCustomerMq("SyncIncrementDataDubboService.delete", Collections.singletonList(customerId));
        }
        return DubboResult.success(update);
    }

    /**
     * 同步数据
     * @param dataType  数据类型
     * @param data  被同步的数据
     * <AUTHOR>
     * @date 2023/4/23 16:13
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult
     **/
    @Override
    public DubboResult<Boolean> sync(Integer dataType, String data) {
        // dataType 1=客户 2=联系人  3=联系方式
        if (dataType == null) {
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "同步数据，数据类型不能为空");
        }
        if (StrUtil.isBlankIfStr(data)) {
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "同步数据，被同步的数据不能为空");
        }
        DubboResult<Boolean> result;
        switch (dataType) {
            case 1:
                result = syncCustomer(data);
                break;
            case 2:
                result = syncLinkman(data);
                break;
            case 3:
                result = syncLinkmanInfo(data);
                break;
            default:
                log.error("非法的数据同步类型，同步类型为:{}", data);
                result = DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "同步数据，非法的数据同步类型");
        }
        return result;
    }

    /**
     * 处理实时增量同步参数
     *
     * @param incrementSyncDto 参数
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     * <AUTHOR>
     * @date 2023/5/4 10:10
     **/
    @Override
    public DubboResult<Boolean> handleSyncParam(IncrementSyncDto incrementSyncDto) {

        try {
            if (incrementSyncDto == null) {
                log.error("handleSyncParam接收到参数为空");
                return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "同步数据，incrementSyncDto不能为空");
            }

            Integer dataType = incrementSyncDto.getDataType();
            Integer syncDeleteFlag = incrementSyncDto.getSyncDeleteFlag();
            Map<String, Object> data = incrementSyncDto.getData();

            if (data == null) {
                return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "同步数据，数据类型不能为空");
            }
            if (dataType == 1) {
                CustomerData customerDataDto = customerFieldDataTransfer(data);
                DubboResult<Boolean> sync = this.sync(dataType, JSON.toJSONString(customerDataDto));
                log.info("客户同步完成");
                return sync;
            }

            if (dataType == 2) {
                ContactPersonData contactPersonDataDto = contactPersonFieldDataTransfer(data);
                DubboResult<Boolean> sync = this.sync(dataType, JSON.toJSONString(contactPersonDataDto));
                log.info("联系人同步完成");
                return sync;
            }

            if (dataType == 3) {
                // 删除 联系方式
                ContactInfoData contactInfoDataDto = contactInfoFieldDataTransfer(data);
                if (syncDeleteFlag == 1) {
                    String contactInfoId = contactInfoDataDto.getContactInfoId();
                    String contactPersonId = contactInfoDataDto.getContactPersonId();
                    if (contactPersonId != null) {
                        DubboResult<Boolean> delete = this.delete(1, contactPersonId);
                        log.info("联系方式同步完成");
                        return delete;
                    } else if (contactInfoId != null) {
                        DubboResult<Boolean> delete = this.delete(2, contactInfoId);
                        log.info("联系方式同步完成");
                        return delete;
                    }
                } else {
                    DubboResult<Boolean> sync = this.sync(dataType, JSON.toJSONString(contactInfoDataDto));
                    log.info("联系方式同步完成");
                    return sync;
                }
            }
        } catch (DuplicateKeyException e) {
            log.error("客户已存在,客户表有唯一约束,incrementSyncDto={}", JSON.toJSONString(incrementSyncDto), e);
            return DubboResult.error(DubboCodeMessageEnum.CUSTOMER_NOT_REPEAT_ADD);
        } catch (Exception e) {
            log.error("同步异常", e);
            return DubboResult.error(DubboCodeMessageEnum.EXCEPTION, "同步异常");
        }

        return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "dataType不合法，未做任何处理");
    }

    /**
     * 同步客户数据
     * @param data  客户数据
     * <AUTHOR>
     * @date 2023/4/23 16:20
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    private DubboResult<Boolean> syncCustomer(String data) {
        //客户数据转换
        Customer customerTarget = customerDataTransfer(data);
        if (StringUtils.isBlank(customerTarget.getCustomerName()) || StringUtils.isBlank(customerTarget.getCustomerName().trim())){
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户名称为空，创建客户失败");
        }
        //获取当前客户是否存在
        Customer customerOrigin = customerService.lambdaQuery().eq(Customer::getCustomerId, customerTarget.getCustomerId()).orderByAsc(Customer::getId).last("limit 1").one();
        boolean flag = true;
        if (customerOrigin == null) {
            flag = customerService.save(customerTarget);
        } else {
            if (customerTarget.getUpdateTime().isAfter(customerOrigin.getUpdateTime())) {
                customerTarget.setId(customerOrigin.getId());
                flag = customerService.updateById(customerTarget);
            }
            if (customerOrigin.getInvalidFlag() == 1) {
                sendMqService.sendCheckValidCustomerMq("SyncIncrementDataDubboService.syncCustomer", Collections.singletonList(customerOrigin.getCustomerId()));
            }
        }
        if (flag && customerOrigin == null) {
            //异步打标(目前只有skb),目前只有新增达标
            sendMqService.sendSkbSyncFlagMq(Collections.singletonList(customerTarget.getCustomerId()));
        }
        customerCacheHandler.del(customerTarget.getCustomerId());
        customerContactCacheHandler.del(customerTarget.getCustomerId());
        return DubboResult.success(flag);
    }

    /**
     * 客户数据转换
     * @param data  原始数据
     * <AUTHOR>
     * @date 2023/4/23 16:31
     * @return com.ce.scrm.customer.dubbo.service.sync.entity.CustomerData
     **/
    private Customer customerDataTransfer(String data) {
        CustomerData customerData = JSON.parseObject(data, CustomerData.class);
        Customer customer = new Customer();
        BeanUtils.copyProperties(customerData, customer);
        //获取并处理大数据ID
        String pid;
        customer.setSourceDataId(pid = emptyHandler(customerData.getSourceDataId()));
        //获取并处理客户类型
        int customerType;
        customer.setCustomerType(customerType = customerType(customerData.getCustomerType()));
        //客户名称
        String customerNameOrigin = customerData.getCustomerName();
        String customerNameTarget;
        if (StringUtils.isNotBlank(customerNameOrigin)) {
            customerNameTarget = customerNameOrigin.replace("\"", "").replace("'", "");
        } else {
            customerNameTarget = "";
        }
        customer.setCustomerName(customerNameTarget);
        //证件类型处理
        int certificateType;
        if (customerType == 1) {
            certificateType = 1;
        } else {
            certificateType = certificateType(customerData.getCertificateType());
        }
        customer.setCertificateType(certificateType);
        //证件号码
        customer.setCertificateCode(certificateCode(customerData.getCertificateCode()));
        //员工规模
        customer.setStaffScale(staffScale(customerData.getStaffScale()));
        //省code
        String provinceCode;
        customer.setProvinceCode((provinceCode = lengthHandler(customerData.getProvinceCode())));
        //市code
        String cityCode;
        customer.setCityCode(cityCode = lengthHandler(customerData.getCityCode()));
        //区code
        String districtCode;
        customer.setDistrictCode(districtCode = lengthHandler(customerData.getDistrictCode()));
        //一级行业code
        String firstIndustryCode;
        customer.setFirstIndustryCode(firstIndustryCode = lengthHandler(customerData.getFirstIndustryCode()));
        //二级行业code
        String secondIndustryCode;
        customer.setSecondIndustryCode(secondIndustryCode = lengthHandler(customerData.getSecondIndustryCode()));
        //创建方式
        customer.setCreateWay(createWay(customerData.getCreateWay()));
        // 老来源
        customer.setLabelFrom(customerData.getCreateWay());
        //删除标记
        customer.setDeleteFlag((customerData.getDeleteFlag() != null && (Long.parseLong(customerData.getDeleteFlag())) == 1) ? 0 : 1);
        //创建时间
        String originCreateTime = customerData.getCreateTime();
        LocalDateTime targetCreateTime;
        if (StringUtils.isBlank(originCreateTime)) {
            targetCreateTime = LocalDateTime.now();
        } else {
            if (StrUtil.isNumeric(originCreateTime)) {
                targetCreateTime = Instant.ofEpochMilli(Long.parseLong(originCreateTime)).atZone(ZoneId.systemDefault()).toLocalDateTime();
            } else {
                targetCreateTime = LocalDateTime.parse(originCreateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }
        customer.setCreateTime(targetCreateTime);
        //创建渠道
        customer.setCreatorKey("scrm");
        //创建人
        customer.setCreator(emptyHandler(customerData.getCreator()));
        //更新时间
        String originUpdateTime = customerData.getUpdateTime();
        LocalDateTime targetUpdateTime;
        if (StringUtils.isBlank(originUpdateTime)) {
            targetUpdateTime = targetCreateTime;
        } else {
            if (StrUtil.isNumeric(originUpdateTime)) {
                targetUpdateTime = Instant.ofEpochMilli(Long.parseLong(originUpdateTime)).atZone(ZoneId.systemDefault()).toLocalDateTime();
            } else {
                targetUpdateTime = LocalDateTime.parse(originUpdateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }
        customer.setUpdateTime(targetUpdateTime);
        //更新渠道
        customer.setOperatorKey("scrm");
        //更新人
        customer.setOperator(emptyHandler(customerData.getOperator()));
        //省名称
        if (StringUtils.isNotBlank(provinceCode)) {
            customer.setProvinceName(getAreaName(provinceCode));
        }
        //市名称
        if (StringUtils.isNotBlank(cityCode)) {
            customer.setCityName(getAreaName(cityCode));
        }
        //区名称
        if (StringUtils.isNotBlank(districtCode)) {
            customer.setDistrictName(getAreaName(districtCode));
        }
        //一级行业名称
        if (StringUtils.isNotBlank(firstIndustryCode)) {
            customer.setFirstIndustryName(getIndustryName(firstIndustryCode));
        }
        //二级行业名称
        if (StringUtils.isNotBlank(secondIndustryCode)) {
            customer.setSecondIndustryName(getIndustryName(secondIndustryCode));
        }
        if (StringUtils.isNotBlank(customerData.getDistributeChannel())) {
            customer.setDistributeChannel(Integer.valueOf(secondIndustryCode));
        }
        //获取大数据企业信息
        if (customerType == 1 && StringUtils.isNotBlank(customerNameTarget)) {
            BigDataCompanyDetail bigDataCompanyDetail = bigDataThirdService.getBigDataCustomerInfo(customerNameTarget);
            if (Objects.nonNull(bigDataCompanyDetail)) {
                //更新大数据ID
                customer.setSourceDataId(bigDataCompanyDetail.getPid());
                //登记状态
                customer.setCheckInState(bigDataCompanyDetail.getEnt_status());
                //成立日期
                customer.setEstablishDate(Instant.ofEpochMilli(Long.parseLong(bigDataCompanyDetail.getEstablish_date())).atZone(ZoneOffset.ofHours(8)).toLocalDate());
                //注册资本
                customer.setRegisterCapital(registerCapital(bigDataCompanyDetail.getReg_cap(), bigDataCompanyDetail.getReg_cap_cur()));
                //注册号
                customer.setRegisterNo(bigDataCompanyDetail.getReg_no());
                //纳税人识别号
                customer.setTaxpayerNo(bigDataCompanyDetail.getUncid());
                //企业类型
                customer.setEnterpriseType(bigDataCompanyDetail.getEnt_type());
                //运营开始时间
                customer.setOpenStartTime(Instant.ofEpochMilli(Long.parseLong(bigDataCompanyDetail.getOp_from())).atZone(ZoneOffset.ofHours(8)).toLocalDate());
                //运营结束时间
                customer.setOpenEndTime(Instant.ofEpochMilli(Long.parseLong(bigDataCompanyDetail.getOp_end())).atZone(ZoneOffset.ofHours(8)).toLocalDate());
                //纳税人资质默认都是一般纳税人
                customer.setTaxpayerQualification("一般纳税人");
                //核准日期
                customer.setApproveDate(Instant.ofEpochMilli(Long.parseLong(bigDataCompanyDetail.getAppr_date())).atZone(ZoneOffset.ofHours(8)).toLocalDate());
                //登记机关
                customer.setRegistrationAuthority(bigDataCompanyDetail.getReg_org());
                //维护一级行业数据
                if (StrUtil.isNotBlank(bigDataCompanyDetail.getIndustryL1_desc())) {
                    customer.setFirstIndustryName(bigDataCompanyDetail.getIndustryL1_desc());
                    customer.setFirstIndustryCode(bigDataCompanyDetail.getIndustryL1_code());
                }
                //维护二级行业数据
                if (CollectionUtil.isNotEmpty(bigDataCompanyDetail.getIndustryL2_desc())) {
                    String secondIndustryName = bigDataCompanyDetail.getIndustryL2_desc().get(0);
                    customer.setSecondIndustryName(secondIndustryName);
                    customer.setSecondIndustryCode(bigDataCompanyDetail.getIndustryL2_code());
                }
                //维护三级行业数据
                if (StrUtil.isNotBlank(bigDataCompanyDetail.getIndustryL3_desc())) {
                    customer.setThirdIndustryName(bigDataCompanyDetail.getIndustryL3_desc());
                    customer.setThirdIndustryCode(bigDataCompanyDetail.getIndustryL3_code());
                }
                //维护四级行业数据
                if (StrUtil.isNotBlank(bigDataCompanyDetail.getIndustryL4_desc())) {
                    customer.setFourthIndustryName(bigDataCompanyDetail.getIndustryL4_desc());
                    customer.setFourthIndustryCode(bigDataCompanyDetail.getIndustryL4_code());
                }
                customer.setLegalPerson(bigDataCompanyDetail.getLegal_person());
                //经营范围
                customer.setBusinessScope(bigDataCompanyDetail.getOp_scope());
            }
        }
        return customer;
    }


    private String lengthHandler(String origin) {
        origin = emptyHandler(origin);
        if (origin.length() > 20) {
            return "";
        }
        return origin;
    }

    private String emptyHandler(String origin) {
        return StringUtils.isBlank(origin) ? "" : origin;
    }

    private int customerType(String data) {
        if (StrUtil.isBlankIfStr(data)) {
            return 0;
        }
        long origin = Long.parseLong(data);
        int target;
        if (origin == 1) {
            target = 1;
        } else if (origin == 2) {
            target = 2;
        } else if (origin == 3) {
            target = 3;
        } else {
            target = 0;
        }
        return target;
    }

    private int certificateType(String origin) {
        if (StringUtils.isBlank(origin)) {
            return 0;
        }
        int target;
        switch (origin) {
            case "SF":
                target = 2;
                break;
            case "JR":
                target = 3;
                break;
            case "HZ":
                target = 4;
                break;
            case "GA":
                target = 5;
                break;
            case "TW":
                target = 6;
                break;
            case "WG":
                target = 7;
                break;
            default:
                target = 0;
        }
        return target;
    }

    private String certificateCode(String origin) {
        return StringUtils.isBlank(origin) ? "" : origin;
    }

    private String staffScale(String origin) {
        if (StringUtils.isBlank(origin)) {
            return origin;
        }
        String target;
        switch (origin) {
            case "D_REGISTER_NUM_01":
                target = "20人以下";
                break;
            case "D_REGISTER_NUM_02":
                target = "100人以下";
                break;
            case "D_REGISTER_NUM_03":
                target = "300人以下";
                break;
            case "D_REGISTER_NUM_04":
                target = "500人以下";
                break;
            case "D_REGISTER_NUM_05":
                target = "1000人以下";
                break;
            case "D_REGISTER_NUM_06":
                target = "1000人以上";
                break;
            default:
                target = origin;
        }
        return target;
    }

    private int createWay(String origin) {
        if (StringUtils.isBlank(origin)) {
            return 0;
        }
        int target;
        switch (origin) {
            case "online":
            case "ce10":
            case "ce1":
            case "ce2":
            case "ce3":
            case "ce4":
            case "ce6":
            case "FBR201603":
            case "FBR":
            case "10":
            case "11":
            case "13":
            case "14":
                target = 1;
                break;
            case "1":
            case "ce0":
            case "TP_CUST":
            case "SALER_IMPO":
            case "HAND_ADDED":
                target = 2;
                break;
            case "INTENTION":
                target = 3;
                break;
            default:
                target = 0;
        }
        return target;
    }

    private String getAreaName(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        Area area = areaService.lambdaQuery().eq(Area::getCode, code).one();
        if (area != null) {
            return area.getName();
        }
        return null;
    }

    private String getIndustryName(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        Industry industry = industryService.lambdaQuery().eq(Industry::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).eq(Industry::getCode, code).one();
        if (industry != null) {
            return industry.getName();
        }
        return null;
    }

    private String registerCapital(String reg_cap, String reg_cap_cur) {
        String target = "";
        if (StringUtils.isNotBlank(reg_cap)) {
            target += reg_cap;
        }
        if (StringUtils.isNotBlank(reg_cap_cur)) {
            target += reg_cap_cur;
        }
        return target;
    }

    /**
     * 同步联系人数据
     * @param data  联系人原始数据
     * <AUTHOR>
     * @date 2023/4/23 16:21
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    private DubboResult<Boolean> syncLinkman(String data) {
        //数据转换
        ContactPersonData contactPersonData = JSON.parseObject(data, ContactPersonData.class);
        ContactPerson contactPerson = new ContactPerson();
        BeanUtils.copyProperties(contactPersonData, contactPerson);
        //联系人数据转换
        ContactPerson contactPersonTarget = contactPersonDataTransfer(contactPersonData, contactPerson);
        log.info("联系人数据转换完成，转换后的数据为:{}", JSONObject.toJSONString(contactPersonData));
        if (StringUtils.isNotBlank(contactPersonData.getCustomerId())){
            CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
            customerBusinessDto.setCustomerId(contactPersonData.getCustomerId());
            CustomerBusinessView customerBusinessView = customerBusiness.detail(customerBusinessDto);
            if (customerBusinessView != null) {
                //企业法人打标
                boolean legalFlag = getLegalFlag(customerBusinessView.getCustomerName(), contactPersonData.getContactPersonName());
                log.info("联系人数据转换完成，是否法人:{}", legalFlag);
                contactPersonTarget.setLegalPersonFlag(legalFlag ? 1 : 0);
            }
        }
        //获取当前联系人是否存在
        ContactPerson contactPersonOrigin = contactPersonService.lambdaQuery().eq(ContactPerson::getContactPersonId, contactPersonTarget.getContactPersonId()).orderByAsc(ContactPerson::getId).last("limit 1").one();
        boolean flag = true;
        if (contactPersonOrigin == null) {
            flag = contactPersonService.save(contactPersonTarget);
        } else {
            //只更新最新的数据，mongo数据有多条ID相同的数据
            if (contactPersonTarget.getUpdateTime().isAfter(contactPersonOrigin.getUpdateTime())) {
                contactPersonTarget.setId(contactPersonOrigin.getId());
                flag = contactPersonService.updateById(contactPersonTarget);
            }
        }
        contactPersonCacheHandler.del(contactPersonTarget.getContactPersonId());
        boolean state = true;
        if (StrUtil.isNotBlank(contactPersonData.getCustomerId()) && contactPersonData.getCustomerId().length() <= 45) {
            //客户联系人关系数据转换
            CustomerContact customerContactTarget = new CustomerContact();
            BeanUtils.copyProperties(contactPersonTarget, customerContactTarget);
            customerContactTarget.setCustomerId(contactPersonData.getCustomerId());
            if (StrUtil.isNotBlank(contactPersonData.getMemberCode())) {
                customerContactTarget.setMemberCode(contactPersonData.getMemberCode());
            }
            //获取当前客户联系人关系是否存在
            CustomerContact customerContactOrigin = customerContactService.lambdaQuery()
                    .eq(CustomerContact::getContactPersonId, customerContactTarget.getContactPersonId())
                    .eq(CustomerContact::getCustomerId, customerContactTarget.getCustomerId()).orderByAsc(CustomerContact::getId).last("limit 1").one();
            if (customerContactOrigin == null) {
                customerContactTarget.setId(null);
                state = customerContactService.save(customerContactTarget);
            } else {
                customerContactTarget.setId(customerContactOrigin.getId());
                state = customerContactService.updateById(customerContactTarget);
            }
            customerContactCacheHandler.del(contactPersonData.getCustomerId());
        }
        customerCacheHandler.del(contactPersonData.getCustomerId());
        return DubboResult.success(flag && state);
    }

    /**
     * 联系人数据转换
     * @param contactPerson   返回联系人数据
     * @param contactPersonData 联系人原始数据
     * <AUTHOR>
     * @date 2023/4/23 19:59
     * @return com.ce.scrm.customer.dao.entity.ContactPerson
     **/
    private ContactPerson contactPersonDataTransfer(ContactPersonData contactPersonData, ContactPerson contactPerson) {
        //名称处理
        contactPerson.setContactPersonName(nameHandler(contactPersonData.getContactPersonName()));
        //性别处理
        contactPerson.setGender(genderType(contactPersonData.getGender()));
        //处理证件类型
        contactPerson.setCertificatesType(linkmanCertificateType(contactPersonData.getCertificatesType()));
        //省code
        String provinceCode;
        contactPerson.setProvinceCode(provinceCode = contactPersonData.getProvinceCode());
        //市code
        String cityCode;
        contactPerson.setCityCode(cityCode = contactPersonData.getCityCode());
        //区code
        String districtCode;
        contactPerson.setDistrictCode(districtCode = contactPersonData.getDistrictCode());
        contactPerson.setSourceKey("scrm");
        contactPerson.setSourceTag(labelFromToTag(contactPersonData.getSourceTag()));
        contactPerson.setDeleteFlag(0);
        //创建时间
        String originCreateTime = contactPersonData.getCreateTime();
        LocalDateTime targetCreateTime;
        if (StringUtils.isBlank(originCreateTime)) {
            targetCreateTime = LocalDateTime.now();
        } else {
            if (StrUtil.isNumeric(originCreateTime)) {
                targetCreateTime = Instant.ofEpochMilli(Long.parseLong(originCreateTime)).atZone(ZoneId.systemDefault()).toLocalDateTime();
            } else {
                targetCreateTime = LocalDateTime.parse(originCreateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }
        contactPerson.setCreateTime(targetCreateTime);
        //创建渠道
        contactPerson.setCreatorKey("scrm");
        //创建人
        contactPerson.setCreator(emptyHandler(contactPersonData.getCreator()));
        //更新时间
        String originUpdateTime = contactPersonData.getUpdateTime();
        LocalDateTime targetUpdateTime;
        if (StringUtils.isBlank(originUpdateTime)) {
            targetUpdateTime = targetCreateTime;
        } else {
            if (StrUtil.isNumeric(originCreateTime)) {
                targetUpdateTime = Instant.ofEpochMilli(Long.parseLong(originUpdateTime)).atZone(ZoneId.systemDefault()).toLocalDateTime();
            } else {
                targetUpdateTime = LocalDateTime.parse(originUpdateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }
        contactPerson.setUpdateTime(targetUpdateTime);
        //更新渠道
        contactPerson.setOperatorKey("scrm");
        //更新人
        contactPerson.setOperator(emptyHandler(contactPersonData.getOperator()));
        //省名称
        if (StringUtils.isNotBlank(provinceCode)) {
            contactPerson.setProvinceName(getAreaName(provinceCode));
        }
        //市名称
        if (StringUtils.isNotBlank(cityCode)) {
            contactPerson.setCityName(getAreaName(cityCode));
        }
        //区名称
        if (StringUtils.isNotBlank(districtCode)) {
            contactPerson.setDistrictName(getAreaName(districtCode));
        }
        return contactPerson;
    }

    private String nameHandler(String origin) {
        if (StringUtils.isBlank(origin) || origin.length() < 45) {
            return origin;
        }
        return origin.substring(0, 45);
    }

    private int genderType(String originData) {
        long origin;
        if (StrUtil.isBlank(originData)) {
            return 0;
        }
        origin = Long.parseLong(originData);
        int target;
        if (origin == 1) {
            target = 1;
        } else if (origin == 2) {
            target = 2;
        } else {
            target = 0;
        }
        return target;
    }

    private int linkmanCertificateType(String origin) {
        if (StringUtils.isBlank(origin)) {
            return 0;
        }
        return 1;
    }

    private String labelFromToTag(String origin) {
        if (StringUtils.isBlank(origin)) {
            return "scrm";
        }
        String target;
        switch (origin) {
            case "ce0":
            case "ce7":
            case "ce9":
                target = "scrm";
                break;
            case "ce5":
                target = "member";
                break;
            case "ce6":
            case "ce8":
                target = "customer_service";
                break;
            case "cloudMarket":
                target = "order";
                break;
            default:
                target = "scrm";
        }
        return target;
    }

    /**
     * 同步联系方式数据
     * @param data  联系方式数据
     * <AUTHOR>
     * @date 2023/4/23 16:21
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    private DubboResult<Boolean> syncLinkmanInfo(String data) {
        //联系方式数据转换
        ContactInfo contactInfoTarget = contactInfoDataTransfer(data);
        //获取当前联系方式是否存在
        ContactInfo contactInfoOrigin = contactInfoService.lambdaQuery()
                .eq(ContactInfo::getContactInfoId, contactInfoTarget.getContactInfoId())
                .orderByAsc(ContactInfo::getId).last("limit 1").one();
        if (contactInfoOrigin == null) {
            contactInfoOrigin = contactInfoService.lambdaQuery()
                    .eq(ContactInfo::getContactPersonId, contactInfoTarget.getContactPersonId())
                    .eq(ContactInfo::getContactType, contactInfoTarget.getContactType())
                    .eq(ContactInfo::getContactWay, contactInfoTarget.getContactWay())
                    .orderByAsc(ContactInfo::getId).last("limit 1").one();
        }
        boolean flag;
        if (contactInfoOrigin == null) {
            flag = contactInfoService.save(contactInfoTarget);
        } else {
            contactInfoTarget.setId(contactInfoOrigin.getId());
            flag = contactInfoService.updateById(contactInfoTarget);
        }
        contactPersonCacheHandler.del(contactInfoTarget.getContactPersonId());
        return DubboResult.success(flag);
    }

    private ContactInfo contactInfoDataTransfer(String data) {
        ContactInfoData contactInfoData = JSON.parseObject(data, ContactInfoData.class);
        ContactInfo contactInfo = new ContactInfo();
        BeanUtils.copyProperties(contactInfoData, contactInfo);
        //联系方式ID为空处理
        String contactWayId;
        if (StringUtils.isBlank(contactWayId = contactInfoData.getContactInfoId())) {
            SequenceIdRequest sequenceIdRequest = new SequenceIdRequest();
            sequenceIdRequest.setBusinessType(BusinessTypeEnum.promotion.getCode());
            contactWayId = String.valueOf(sequenceService.generateId(sequenceIdRequest).getData());
        }
        contactInfo.setContactInfoId(contactWayId);
        //联系方式类型处理
        contactInfo.setContactType(contactWayType(contactInfoData.getContactType()));
        //处理联系方式
        contactInfo.setContactWay(emptyHandler(contactInfoData.getContactWay()));
        //手机号标识，默认是1
        contactInfo.setPhoneFlag(1);
        contactInfo.setSignatoryFlag(1);
        contactInfo.setMemberFlag(isMemberContact(contactInfoData.getMemberFlag()));
        contactInfo.setSourceKey("scrm");
        contactInfo.setDeleteFlag(0);
        //创建时间
        String originCreateTime = contactInfoData.getCreateTime();
        LocalDateTime targetCreateTime;
        if (StringUtils.isBlank(originCreateTime)) {
            targetCreateTime = LocalDateTime.now();
        } else {
            if (StrUtil.isNumeric(originCreateTime)) {
                targetCreateTime = Instant.ofEpochMilli(Long.parseLong(originCreateTime)).atZone(ZoneId.systemDefault()).toLocalDateTime();
            } else {
                targetCreateTime = LocalDateTime.parse(originCreateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }
        contactInfo.setCreateTime(targetCreateTime);
        //创建渠道
        contactInfo.setCreatorKey("scrm");
        //创建人
        contactInfo.setCreator(emptyHandler(contactInfoData.getCreator()));
        //更新时间
        String originUpdateTime = contactInfoData.getUpdateTime();
        LocalDateTime targetUpdateTime;
        if (StringUtils.isBlank(originUpdateTime)) {
            targetUpdateTime = targetCreateTime;
        } else {
            if (StrUtil.isNumeric(originUpdateTime)) {
                targetUpdateTime = Instant.ofEpochMilli(Long.parseLong(originUpdateTime)).atZone(ZoneId.systemDefault()).toLocalDateTime();
            } else {
                targetUpdateTime = LocalDateTime.parse(originUpdateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }
        contactInfo.setUpdateTime(targetUpdateTime);
        //更新渠道
        contactInfo.setOperatorKey("scrm");
        //更新人
        contactInfo.setOperator(emptyHandler(contactInfoData.getOperator()));
        return contactInfo;
    }

    private int isMemberContact(String origin) {
        int target = 0;
        if (StringUtils.isNotBlank(origin) && StringUtils.isNumeric(origin)) {
            target = Integer.parseInt(origin);
        }
        return target;
    }

    private int contactWayType(String originData) {
        long origin;
        if (StrUtil.isBlank(originData)) {
            return 0;
        }
        origin = Long.parseLong(originData);
        int target;
        if (origin == 1) {
            target = 4;
        } else if (origin == 2) {
            target = 1;
        } else if (origin == 3) {
            target = 5;
        } else if (origin == 5) {
            target = 2;
        } else if (origin == 6) {
            target = 3;
        } else if (origin == 10) {
            target = 6;
        } else {
            target = 0;
        }
        return target;
    }


    private CustomerData customerFieldDataTransfer(Map<String, Object> data) {
        if (data == null) {
            return null;
        }
        CustomerData customerDataDto = new CustomerData();
        if (data.get("custId") != null) {
            String custId = (String) data.get("custId");
            customerDataDto.setCustomerId(custId);
        }

        if (data.get("custNameCn") != null) {
            String custNameCn = (String) data.get("custNameCn");
            customerDataDto.setCustomerName(custNameCn);
        }

        if (data.get("custIdNumber") != null) {
            String custIdNumber = (String) data.get("custIdNumber");
            customerDataDto.setCertificateCode(custIdNumber);
        }

        if (data.get("custCertificatesType") != null) {
            String custCertificatesType = (String) data.get("custCertificatesType");
            customerDataDto.setCertificateType(custCertificatesType);
        }

        if (data.get("custAddress") != null) {
            String custAddress = (String) data.get("custAddress");
            customerDataDto.setRegisterAddress(custAddress);
        }

        if (data.get("custAddressProvince") != null) {
            String custAddressProvince = (String) data.get("custAddressProvince");
            customerDataDto.setProvinceCode(custAddressProvince);
        }

        if (data.get("custAddressCity") != null) {
            String custAddressCity = (String) data.get("custAddressCity");
            customerDataDto.setCityCode(custAddressCity);
        }

        if (data.get("custAddressRegion") != null) {
            String custAddressRegion = (String) data.get("custAddressRegion");
            customerDataDto.setDistrictCode(custAddressRegion);
        }

        if (data.get("industryClassBig") != null) {
            String industryClassBig = (String) data.get("industryClassBig");
            customerDataDto.setFirstIndustryCode(industryClassBig);
        }
        if (data.get("industryClassSmall") != null) {
            String industryClassSmall = (String) data.get("industryClassSmall");
            customerDataDto.setSecondIndustryCode(industryClassSmall);
        }

        if (data.get("custType") != null) {
            Object custType = data.get("custType");
            customerDataDto.setCustomerType(custType.toString());
        }

        if (data.get("custRegisterPeopleNumberType") != null) {
            String custRegisterPeopleNumberType = (String) data.get("custRegisterPeopleNumberType");
            customerDataDto.setStaffScale(custRegisterPeopleNumberType);
        }

        if (data.get("lableFrom") != null) {
            String labelFrom = (String) data.get("lableFrom");
            customerDataDto.setCreateWay(labelFrom);
        }

        if (data.get("customerIsActive") != null) {
            Object customerIsActive = data.get("customerIsActive");
            customerDataDto.setDeleteFlag(customerIsActive.toString());
        }

        if (data.get("createDate") != null) {
            customerDataDto.setCreateTime(getDateFromObject(data.get("createDate")));
        }

        if (data.get("updateTime") != null) {
            customerDataDto.setUpdateTime(getDateFromObject(data.get("updateTime")));
        }

        if (data.get("creator") != null) {
            String creator = (String) data.get("creator");
            customerDataDto.setCreator(creator);
        }

        if (data.get("distributeChannel") != null) {
            String distributeChannel = (String) data.get("distributeChannel");
            customerDataDto.setDistributeChannel(distributeChannel);
        }

        return customerDataDto;
    }

    private ContactPersonData contactPersonFieldDataTransfer(Map<String, Object> data) {
        if (data == null) {
            return null;
        }
        ContactPersonData contactPersonDataDto = new ContactPersonData();
        if (data.get("custId") != null) {
            String custId = (String) data.get("custId");
            contactPersonDataDto.setCustomerId(custId);
        }

        if (data.get("manId") != null) {
            String manId = (String) data.get("manId");
            contactPersonDataDto.setContactPersonId(manId);
        }

        if (data.get("manNameCn") != null) {
            String manNameCn = (String) data.get("manNameCn");
            contactPersonDataDto.setContactPersonName(manNameCn);
        }

        if (data.get("manSex") != null) {
            Object manSex = data.get("manSex");
            contactPersonDataDto.setGender(manSex.toString());
        }
/*
        if(data.get("certificatesType") != null){
            String certificatesType = (String)data.get("certificatesType");
            contactPersonDataDto.setCertificatesType(certificatesType);
        }*/

       /* if(data.get("provinceCode") != null){
            String provinceCode = (String)data.get("provinceCode");
            contactPersonDataDto.setProvinceCode(provinceCode);
        }

        if(data.get("custAddressCity") != null){
            String custAddressCity = (String)data.get("custAddressCity");
            customerDataDto.setCityCode(custAddressCity);
        }

        if(data.get("custAddressRegion") != null){
            String custAddressRegion = (String)data.get("custAddressRegion");
            customerDataDto.setDistrictCode(custAddressRegion);
        }*/

        if (data.get("manPost") != null) {
            Object manPost = data.get("manPost");
            contactPersonDataDto.setPosition(manPost.toString());
        }

        if (data.get("lableFrom") != null) {
            Object lableFrom = data.get("lableFrom");
            contactPersonDataDto.setSourceTag(lableFrom.toString());
        }

        if (data.get("createDate") != null) {
            contactPersonDataDto.setCreateTime(getDateFromObject(data.get("createDate")));
        }
        if (data.get("creator") != null) {
            String creator = (String) data.get("creator");
            contactPersonDataDto.setCreator(creator);
        }

        if (data.get("updateTime") != null) {
            contactPersonDataDto.setUpdateTime(getDateFromObject(data.get("updateTime")));
        }
        if (data.get("remarks") != null) {
            String remarks = (String) data.get("remarks");
            contactPersonDataDto.setRemarks(remarks);
        }
        return contactPersonDataDto;
    }

    private ContactInfoData contactInfoFieldDataTransfer(Map<String, Object> data) {
        if (data == null) {
            return null;
        }
        ContactInfoData contactInfoDataDto = new ContactInfoData();
        if (data.get("contactId") != null) {
            String contactId = (String) data.get("contactId");
            contactInfoDataDto.setContactInfoId(contactId);
        }

        if (data.get("manId") != null) {
            String manId = (String) data.get("manId");
            contactInfoDataDto.setContactPersonId(manId);
        }

        if (data.get("contactType") != null) {
            Object contactType = data.get("contactType");
            contactInfoDataDto.setContactType(contactType.toString());
        }

        if (data.get("contactNum") != null) {
            String contactNum = (String) data.get("contactNum");
            contactInfoDataDto.setContactWay(contactNum);
        }

        if (data.get("isMemberContact") != null) {
            Object isMemberContact = data.get("isMemberContact");
            contactInfoDataDto.setMemberFlag(isMemberContact.toString());
        }

        if (data.get("creator") != null) {
            String creator = (String) data.get("creator");
            contactInfoDataDto.setCreator(creator);
        }

        if (data.get("createDate") != null) {
            contactInfoDataDto.setCreateTime(getDateFromObject(data.get("createDate")));
        }

        if (data.get("updateTime") != null) {
            contactInfoDataDto.setUpdateTime(getDateFromObject(data.get("updateTime")));
        }

        if (data.get("signatoryFlag") != null) {
            Object signatoryFlag = data.get("signatoryFlag");
            contactInfoDataDto.setSignatoryFlag(signatoryFlag.toString());
        }
        return contactInfoDataDto;
    }

    private String getDateFromObject(Object createDate) {
        long l = Long.parseLong(createDate.toString());
        Date date = new Date(l);

        return DateUtil.format(date, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 获取法人标记
     * @param customerName      客户名称
     * @param contactPersonName 联系人名称
     * <AUTHOR>
     * @date 2024/1/15 12:19
     * @return boolean
     **/
    private boolean getLegalFlag(String customerName, String contactPersonName) {
        if (StrUtil.isBlank(contactPersonName)) {
            return false;
        }
        BigDataCompanyDetail bigDataCustomerInfo = bigDataThirdService.getBigDataCustomerInfo(customerName);
        return bigDataCustomerInfo != null && contactPersonName.equals(bigDataCustomerInfo.getLegal_person());
    }
}