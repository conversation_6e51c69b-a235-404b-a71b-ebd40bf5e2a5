package com.ce.scrm.customer;

import com.alibaba.nacos.spring.context.annotation.config.EnableNacosConfig;
import com.xxl.job.core.thread.ExecutorRegistryThread;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.PreDestroy;

/**
 * dubbo启动类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/06/01 12:53 下午
 **/
@SpringBootApplication
@EnableTransactionManagement
@EnableNacosConfig
@EnableDubbo
@MapperScan("com.ce.scrm.customer.dao.mapper")
public class ScrmCustomerDubboApplication {

    private final static Logger LOGGER = LoggerFactory.getLogger(ScrmCustomerDubboApplication.class);

    /**
     * 项目启动方法
     * @param args  启动参数
     * <AUTHOR>
     * @date 2021/05/24 下午2:25
     **/
    public static void main(String[] args) {
        SpringApplication.run(ScrmCustomerDubboApplication.class, args);
        //停止xxlJOB注册处理器
        ExecutorRegistryThread.getInstance().toStop();
        LOGGER.error("ScrmCustomerDubboApplication is started（项目启动成功）");
    }

    @PreDestroy
    public void destroy(){
        LOGGER.error("ScrmCustomerDubboApplication is started（项目即将重启）");
    }
}
