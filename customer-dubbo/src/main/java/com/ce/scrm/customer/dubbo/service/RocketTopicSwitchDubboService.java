package com.ce.scrm.customer.dubbo.service;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.customer.dubbo.api.IRocketTopicSwitchDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.RocketTopicSwitchDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.RocketTopicSwitchView;
import com.ce.scrm.customer.service.business.IRocketTopicSwitchBusiness;
import com.ce.scrm.customer.service.business.entity.dto.RocketTopicSwitchBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.RocketTopicSwitchBusinessView;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * rocket mq 开关
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/7/10 17:08
 */
@Slf4j
@DubboService(interfaceClass = IRocketTopicSwitchDubbo.class)
public class RocketTopicSwitchDubboService implements IRocketTopicSwitchDubbo {

    @Resource
    IRocketTopicSwitchBusiness iRocketTopicSwitchBusiness;

    @Override
    public DubboResult<Integer> getTopicSwitchByTopicName(String topicName) {
        if (StringUtils.isEmpty(topicName)) {
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "参数不能为空");
        }
        return DubboResult.success(iRocketTopicSwitchBusiness.getTopicSwitchByTopicName(topicName));
    }

    @Override
    public DubboResult<List<RocketTopicSwitchView>> getTopicSwitchByConditation(RocketTopicSwitchDubboDto rocketTopicSwitchDubboDto) {
        RocketTopicSwitchBusinessDto rocketTopicSwitchBusinessDto = new RocketTopicSwitchBusinessDto();
        rocketTopicSwitchBusinessDto.setTopic(rocketTopicSwitchDubboDto.getTopic());
        rocketTopicSwitchBusinessDto.setStatus(rocketTopicSwitchDubboDto.getStatus());
        List<RocketTopicSwitchBusinessView> lists = iRocketTopicSwitchBusiness.getTopicSwitchByConditation(rocketTopicSwitchBusinessDto);
        List<RocketTopicSwitchView> viewList = Optional.of(lists).orElse(Lists.newArrayList()).stream().map(record->{
            RocketTopicSwitchView view = BeanUtil.copyProperties(record, RocketTopicSwitchView.class);
            return view;
        }).collect(Collectors.toList());
        return DubboResult.success(viewList);
    }

    @Override
    public DubboResult<Boolean> update(RocketTopicSwitchDubboDto rocketTopicSwitchDubboDto) {
        if (StringUtils.isEmpty(rocketTopicSwitchDubboDto.getTopic()) || rocketTopicSwitchDubboDto.getStatus()==null){
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "参数不能为空");
        }
        RocketTopicSwitchBusinessDto rocketTopicSwitchBusinessDto = new RocketTopicSwitchBusinessDto();
        rocketTopicSwitchBusinessDto.setTopic(rocketTopicSwitchDubboDto.getTopic());
        rocketTopicSwitchBusinessDto.setStatus(rocketTopicSwitchDubboDto.getStatus());
        Boolean result = iRocketTopicSwitchBusiness.update(rocketTopicSwitchBusinessDto);
        return DubboResult.success(result);
    }
}
