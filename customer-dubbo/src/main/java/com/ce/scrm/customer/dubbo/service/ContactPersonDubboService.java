package com.ce.scrm.customer.dubbo.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.dao.entity.CustomerBindWx;
import com.ce.scrm.customer.dao.entity.OrderDefaultFlag;
import com.ce.scrm.customer.dao.service.CustomerBindWxService;
import com.ce.scrm.customer.dubbo.aop.sign.Sign;
import com.ce.scrm.customer.dubbo.api.IContactPersonDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.*;
import com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.*;
import com.ce.scrm.customer.dubbo.service.sync.entity.WechatBindData;
import com.ce.scrm.customer.dubbo.util.PageUtil;
import com.ce.scrm.customer.service.business.*;
import com.ce.scrm.customer.service.business.entity.dto.*;
import com.ce.scrm.customer.service.business.entity.view.*;
import com.ce.scrm.customer.service.config.UniqueIdService;
import com.ce.scrm.customer.service.enums.BindTypeEnum;
import com.ce.scrm.customer.service.enums.ContactInfoEnum;
import com.ce.scrm.customer.service.third.entity.view.BigDataCompanyDetail;
import com.ce.scrm.customer.service.third.entity.view.CompleteOrgInfo;
import com.ce.scrm.customer.service.third.invoke.BigDataThirdService;
import com.ce.scrm.customer.service.third.invoke.EmployeeThirdService;
import com.ce.scrm.customer.util.constant.UtilConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 联系人dubbo业务
 * <AUTHOR>
 * @date 2023/4/13 14:50
 * @version 1.0.0
 */
@Slf4j
@DubboService(interfaceClass = IContactPersonDubbo.class)
public class ContactPersonDubboService implements IContactPersonDubbo {

    @Resource
    private ICustomerBusiness customerBusiness;

    @Resource
    private IContactPersonBusiness contactPersonBusiness;

    @Resource
    private ICustomerContactBusiness customerContactBusiness;

    @Resource
    private UniqueIdService uniqueIdService;

    @Resource
    private IAreaBusiness areaBusiness;

    @Resource
    private IContactInfoBusiness contactInfoBusiness;

    @Resource
    private EmployeeThirdService employeeThirdService;

    @Resource
    private IOrderDefaultFlagBusiness orderDefaultFlagBusiness;

    @Resource
    private SendMqService sendMqService;

    @Resource
    private BigDataThirdService bigDataThirdService;

    @Resource
    private CustomerBindWxService customerBindWxService;


    @Sign
    @Override
    public DubboResult<ContactPersonDubboView> detail(ContactPersonDubboDto contactPersonDubboDto) {

        ContactPersonBusinessDto contactPersonBusinessDto = new ContactPersonBusinessDto();
        contactPersonBusinessDto.setContactPersonId(contactPersonDubboDto.getContactPersonId());
        ContactPersonBusinessView contactPersonBusinessView = contactPersonBusiness.detail(contactPersonBusinessDto);
        ContactPersonDubboView contactPersonDubboView = BeanUtil.copyProperties(contactPersonBusinessView, ContactPersonDubboView.class);
        if (contactPersonDubboView == null) {
            return DubboResult.success();
        }
        //修改默认签单标记
        List<ContactInfoDubboView> contactInfoList = contactPersonDubboView.getContactInfoList();
        Optional.ofNullable(contactInfoList).orElse(new ArrayList<>()).forEach(contactInfoDubboView -> {
            if (orderDefaultFlagBusiness.check(contactPersonDubboView.getContactPersonId(), ContactInfoEnum.get(contactInfoDubboView.getContactType()), contactInfoDubboView.getContactWay())) {
                contactInfoDubboView.setSignatoryFlag(1);
            } else {
                contactInfoDubboView.setSignatoryFlag(null);
            }
        });
        contactPersonDubboView.setContactInfoList(contactInfoList);
        return DubboResult.success(contactPersonDubboView);
    }

    @Sign
    @Override
    public DubboResult<CustomerDubboView> customerContactDetail(CustomerDetailDubboDto customerDetailDubboDto) {
        CustomerContactBusinessDto customerContactBusinessDto = BeanUtil.copyProperties(customerDetailDubboDto, CustomerContactBusinessDto.class);
        CustomerBusinessView customerBusinessView = customerContactBusiness.customerContactDetail(customerContactBusinessDto);
        if (customerBusinessView == null) {
            return DubboResult.success(null);
        }
        CustomerDubboView customerDubboView = BeanUtil.copyProperties(customerBusinessView, CustomerDubboView.class);

        List<ContactPersonBusinessView> contactPersonList = customerBusinessView.getContactPersonList();
        List<ContactPersonDubboView> contactPersonDubboViews = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(contactPersonList)) {
            for (ContactPersonBusinessView contactPersonBusinessView : contactPersonList) {
                ContactPersonDubboView contactPersonDubboView = BeanUtil.copyProperties(contactPersonBusinessView, ContactPersonDubboView.class);
                contactPersonDubboViews.add(contactPersonDubboView);
                List<ContactInfoBusinessView> contactInfoList = contactPersonBusinessView.getContactInfoList();
                List<ContactInfoDubboView> contactInfoDubboViews = BeanUtil.copyToList(contactInfoList, ContactInfoDubboView.class);
                Optional.ofNullable(contactInfoDubboViews).orElse(new ArrayList<>()).forEach(contactInfoDubboView -> {
                    if (orderDefaultFlagBusiness.check(contactPersonDubboView.getContactPersonId(), ContactInfoEnum.get(contactInfoDubboView.getContactType()), contactInfoDubboView.getContactWay())) {
                        contactInfoDubboView.setSignatoryFlag(1);
                    } else {
                        contactInfoDubboView.setSignatoryFlag(null);
                    }
                });
                contactPersonDubboView.setContactInfoList(contactInfoDubboViews);
            }
        }
        customerDubboView.setContactPersonDubboViewList(contactPersonDubboViews);
        sendMqService.sendCheckValidCustomerMq("ContactPersonDubboService.customerContactDetail", Collections.singletonList(customerDubboView.getCustomerId()));
        return DubboResult.success(customerDubboView);
    }

    @Sign
    @Override
    public DubboResult<List<ContactPersonDubboView>> detailList(ContactPersonDubboDto contactPersonDubboDto) {

        List<String> contactPersonIdList = contactPersonDubboDto.getContactPersonIdList();
        List<ContactPersonDubboView> contactPersonDubboViewList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(contactPersonIdList)) {
            for (String contactPersonId : contactPersonIdList) {
                ContactPersonBusinessDto contactPersonBusinessDto = new ContactPersonBusinessDto();
                contactPersonBusinessDto.setContactPersonId(contactPersonId);
                ContactPersonBusinessView contactPersonBusinessView = contactPersonBusiness.detail(contactPersonBusinessDto);
                if (contactPersonBusinessView == null) {
                    continue;
                }
                ContactPersonDubboView contactPersonDubboView = BeanUtil.copyProperties(contactPersonBusinessView, ContactPersonDubboView.class);
                contactPersonDubboViewList.add(contactPersonDubboView);
            }
        }
        return DubboResult.success(contactPersonDubboViewList);
    }

    @Sign
    @Override
    public DubboResult<List<CustomerDubboView>> customerContactDetailByMemberCodes(CustomerDetailDubboDto customerDetailDubboDto) {
        CustomerContactBusinessDto customerContactBusinessDto = BeanUtil.copyProperties(customerDetailDubboDto, CustomerContactBusinessDto.class);
        List<CustomerBusinessView> customerBusinessViews = customerContactBusiness.customerContactByMemberCodes(customerContactBusinessDto);
        if (CollectionUtil.isEmpty(customerBusinessViews)) {
            return DubboResult.success(null);
        }

        List<CustomerDubboView> customerDubboViews = new ArrayList<>();
        for (CustomerBusinessView view : customerBusinessViews) {
            CustomerDubboView customerDubboView = BeanUtil.copyProperties(view, CustomerDubboView.class);

            List<ContactPersonBusinessView> contactPersonList = view.getContactPersonList();
            List<ContactPersonDubboView> contactPersonDubboViews = new ArrayList<>();
            for (ContactPersonBusinessView contactPersonBusinessView : contactPersonList) {
                ContactPersonDubboView contactPersonDubboView = BeanUtil.copyProperties(contactPersonBusinessView, ContactPersonDubboView.class);
                contactPersonDubboViews.add(contactPersonDubboView);
                List<ContactInfoBusinessView> contactInfoList = contactPersonBusinessView.getContactInfoList();
                List<ContactInfoDubboView> contactInfoDubboViews = BeanUtil.copyToList(contactInfoList, ContactInfoDubboView.class);
                Optional.ofNullable(contactInfoDubboViews).orElse(new ArrayList<>()).forEach(contactInfoDubboView -> {
                    if (orderDefaultFlagBusiness.check(contactPersonDubboView.getContactPersonId(), ContactInfoEnum.get(contactInfoDubboView.getContactType()), contactInfoDubboView.getContactWay())) {
                        contactInfoDubboView.setSignatoryFlag(1);
                    } else {
                        contactInfoDubboView.setSignatoryFlag(null);
                    }
                });
                contactPersonDubboView.setContactInfoList(contactInfoDubboViews);
            }
            customerDubboView.setContactPersonDubboViewList(contactPersonDubboViews);
            customerDubboViews.add(customerDubboView);
        }
        sendMqService.sendCheckValidCustomerMq("ContactPersonDubboService.customerContactDetailByMemberCodes", customerDubboViews.stream().map(CustomerDubboView::getCustomerId).collect(Collectors.toList()));

        return DubboResult.success(customerDubboViews);
    }

    /**
     * 获取联系人数据，给其他系统提供
     *
     * @param contactPersonDubboDto 查询参数
     * <AUTHOR>
     * @date 2023/5/15 10:14
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView>>
     **/
    @Sign
    @Override
    public DubboResult<List<ContactPersonDataDubboView>> getData(ContactPersonDubboDto contactPersonDubboDto) {
        if (contactPersonDubboDto == null || (StrUtil.isBlank(contactPersonDubboDto.getContactPersonId()) && (StrUtil.isBlank(contactPersonDubboDto.getCustomerId()) || StrUtil.isBlank(contactPersonDubboDto.getPhone())))) {
            log.warn("获取联系人数据，必传参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必传参数不能为空");
        }
        List<ContactPersonBusinessView> contactPersonBusinessViewList;
        if (StrUtil.isNotBlank(contactPersonDubboDto.getContactPersonId())) {
            contactPersonBusinessViewList = Collections.singletonList(contactPersonBusiness.detail(contactPersonDubboDto.getContactPersonId()));
        } else {
            contactPersonBusinessViewList = contactPersonBusiness.detail(contactPersonDubboDto.getCustomerId(), contactPersonDubboDto.getPhone());
        }
        if (CollectionUtil.isEmpty(contactPersonBusinessViewList)) {
            log.warn("获取联系人数据为空，参数为:{}", JSON.toJSONString(contactPersonDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.RETURN_NULL);
        }
        return DubboResult.success(contactPersonBusinessViewList.stream().flatMap(contactPersonBusinessView -> conversion(contactPersonBusinessView).stream()).collect(Collectors.toList()));
    }

    /**
     * 将联系人数据按照联系方式分组转换
     *
     * @param contactPersonBusinessView 联系人数据
     * <AUTHOR>
     * @date 2023/5/15 10:26
     * @return java.util.List<com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView>
     **/
    private List<ContactPersonDataDubboView> conversion(ContactPersonBusinessView contactPersonBusinessView) {
        if (contactPersonBusinessView == null) {
            return new ArrayList<>();
        }
        List<ContactInfoBusinessView> contactInfoList = contactPersonBusinessView.getContactInfoList();
        Optional<String> email = contactInfoList.stream().filter(contactInfoBusinessView -> ContactInfoEnum.email.getType().equals(contactInfoBusinessView.getContactType())).map(ContactInfoBusinessView::getContactWay).findFirst();
        Optional<String> wechat = contactInfoList.stream().filter(contactInfoBusinessView -> ContactInfoEnum.wechat.getType().equals(contactInfoBusinessView.getContactType())).map(ContactInfoBusinessView::getContactWay).findFirst();
        Optional<String> wecome = contactInfoList.stream().filter(contactInfoBusinessView -> ContactInfoEnum.wecome.getType().equals(contactInfoBusinessView.getContactType())).map(ContactInfoBusinessView::getContactWay).findFirst();
        Optional<String> qq = contactInfoList.stream().filter(contactInfoBusinessView -> ContactInfoEnum.qq.getType().equals(contactInfoBusinessView.getContactType())).map(ContactInfoBusinessView::getContactWay).findFirst();
        Optional<String> telephone = contactInfoList.stream().filter(contactInfoBusinessView -> ContactInfoEnum.telephone.getType().equals(contactInfoBusinessView.getContactType())).map(ContactInfoBusinessView::getContactWay).findFirst();
        Optional<OrderDefaultFlag> orderDefaultFlag;
        //订单标记
        if ("order".equals(contactPersonBusinessView.getSourceKey())) {
            List<String> phoneList = contactInfoList.stream().filter(contactInfoBusinessView -> ContactInfoEnum.mobile.getType().equals(contactInfoBusinessView.getContactType())).map(ContactInfoBusinessView::getContactWay).collect(Collectors.toList());
            List<String> emailList = contactInfoList.stream().filter(contactInfoBusinessView -> ContactInfoEnum.email.getType().equals(contactInfoBusinessView.getContactType())).map(ContactInfoBusinessView::getContactWay).collect(Collectors.toList());
            orderDefaultFlag = Optional.ofNullable(orderDefaultFlagBusiness.get(contactPersonBusinessView.getContactPersonId(), phoneList, emailList));
            if (orderDefaultFlag.isPresent()) {
                email = Optional.of(orderDefaultFlag.get().getEmail());
            }
        } else {
            orderDefaultFlag = Optional.empty();
        }
        Optional<String> finalEmail = email;
        return contactInfoList.stream()
                .filter(contactInfoBusinessView -> ContactInfoEnum.mobile.getType().equals(contactInfoBusinessView.getContactType()))
                .map(contactInfoBusinessView -> {
                    ContactPersonDataDubboView contactPersonDataDubboView = BeanUtil.copyProperties(contactPersonBusinessView, ContactPersonDataDubboView.class);
                    contactPersonDataDubboView.setPhone(contactInfoBusinessView.getContactWay());
                    finalEmail.ifPresent(contactPersonDataDubboView::setEmail);
                    wechat.ifPresent(contactPersonDataDubboView::setWechat);
                    wecome.ifPresent(contactPersonDataDubboView::setWecome);
                    qq.ifPresent(contactPersonDataDubboView::setQq);
                    telephone.ifPresent(contactPersonDataDubboView::setTelephone);
                    //订单设置默认标签,需要等待数据清洗之后
                    orderDefaultFlag.ifPresent(orderDefaultFlagValue -> contactPersonDataDubboView.setDefaultFlag(orderDefaultFlagValue.getPhone().equals(contactInfoBusinessView.getContactWay()) ? 1 : 0));
                    return contactPersonDataDubboView;
                }).collect(Collectors.toList());
    }

    /**
     * 获取客户联系人数据，给其他系统提供
     *
     * @param customerDetailDubboDto 查询参数
     * <AUTHOR>
     * @date 2023/5/15 10:14
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView>>
     **/
    @Sign
    @Override
    public DubboResult<List<ContactPersonDataDubboView>> getCustomerData(CustomerDetailDubboDto customerDetailDubboDto) {
        if (customerDetailDubboDto == null || StrUtil.isBlank(customerDetailDubboDto.getCustomerId())) {
            log.warn("获取客户联系人数据，客户ID不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户ID不能为空");
        }
        List<ContactPersonBusinessView> contactPersonList = customerContactBusiness.customerContactPersonDetail(customerDetailDubboDto.getCustomerId());
        if (CollectionUtil.isEmpty(contactPersonList)) {
            log.warn("获取客户联系人数据为空，参数为:{}", JSON.toJSONString(customerDetailDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.RETURN_NULL);
        }
        sendMqService.sendCheckValidCustomerMq("ContactPersonDubboService.getCustomerData", Collections.singletonList(customerDetailDubboDto.getCustomerId()));
        return DubboResult.success(contactPersonList.stream()
                .filter(contactPersonBusinessView -> contactPersonBusinessView != null && CollectionUtil.isNotEmpty(contactPersonBusinessView.getContactInfoList()))
                .flatMap(contactPersonBusinessView -> conversion(contactPersonBusinessView).stream()).collect(Collectors.toList()));
    }

    /**
     * 添加联系人，给其他系统提供
     *
     * @param contactPersonAddDubboDto 联系人数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.ContactPersonAddDubboView>
     **/
    @Sign
    @Override
    public DubboResult<ContactPersonAddDubboView> add(ContactPersonAddDubboDto contactPersonAddDubboDto) {
        if (contactPersonAddDubboDto == null || StrUtil.isBlank(contactPersonAddDubboDto.getCustomerId()) || StrUtil.isBlank(contactPersonAddDubboDto.getContactPersonName())
                || StrUtil.isBlank(contactPersonAddDubboDto.getPhone()) || StrUtil.isBlank(contactPersonAddDubboDto.getOperator())) {
            log.warn("添加联系人，必传参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必传参数不能为空");
        }
        CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
        customerBusinessDto.setCustomerId(contactPersonAddDubboDto.getCustomerId());
        CustomerBusinessView customerBusinessView = customerBusiness.detail(customerBusinessDto);
        if (customerBusinessView == null) {
            log.warn("添加联系人，客户不存在，客户ID为:{}", contactPersonAddDubboDto.getCustomerId());
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户不存在");
        }
        //企业法人打标
        boolean legalFlag = getLegalFlag(customerBusinessView.getCustomerName(), contactPersonAddDubboDto.getContactPersonName());
        contactPersonAddDubboDto.setLegalPersonFlag(legalFlag ? 1 : 0);
        //校验联系人手机号
        DubboResult<?> checkPhoneResult = checkPhone(contactPersonAddDubboDto.getPhone());
        if (!checkPhoneResult.checkSuccess()) {
            return DubboResult.error(checkPhoneResult);
        }
        //根据联系方式和来源获取客户下联系人方式数据
        Optional<ContactInfoBusinessView> customerContactInfoDataOptional = contactInfoBusiness.getCustomerContactInfoData(contactPersonAddDubboDto.getCustomerId(), contactPersonAddDubboDto.getContactPersonName(), contactPersonAddDubboDto.getSourceKey(), Arrays.asList(contactPersonAddDubboDto.getPhone().split(UtilConstant.DATA_SEPARATOR)));
        if (customerContactInfoDataOptional.isPresent()) {
            log.warn("联系人不能重复添加，联系方式数据为：{}", JSON.toJSONString(customerContactInfoDataOptional.get()));
            ContactPersonAddDubboView contactPersonAddDubboView = new ContactPersonAddDubboView();
            contactPersonAddDubboView.setContactPersonId(customerContactInfoDataOptional.get().getContactPersonId());
            return DubboResult.success(contactPersonAddDubboView, DubboCodeMessageEnum.CONTACT_PERSON_NOT_REPEAT_ADD.getMessage());
        }
        //拼装联系人数据
        ContactPersonAddBusinessDto contactPersonAddBusinessDto = packageContactPersonData(contactPersonAddDubboDto);
        //添加联系人
        contactPersonBusiness.add(contactPersonAddBusinessDto);
        ContactPersonAddDubboView contactPersonAddDubboView = new ContactPersonAddDubboView();
        contactPersonAddDubboView.setContactPersonId(contactPersonAddBusinessDto.getContactPersonId());

        sendMqService.sendCheckValidCustomerMq("ContactPersonDubboService.add", Collections.singletonList(contactPersonAddDubboDto.getCustomerId()));
        return DubboResult.success(contactPersonAddDubboView);
    }

    /**
     * 校验联系人手机号不能是员工绑定的手机号
     * @param phoneArrStr   手机号数组字符串，逗号分隔
     * <AUTHOR>
     * @date 2023/5/15 16:12
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<?>
     **/
    private DubboResult<?> checkPhone(String phoneArrStr) {
        List<String> phoneList = Arrays.asList(phoneArrStr.split(UtilConstant.DATA_SEPARATOR));
        if (phoneList.stream().anyMatch(phone -> employeeThirdService.check(phone))) {
            return DubboResult.error(DubboCodeMessageEnum.CONTACT_PERSON_PHONE_CHECK_FAIL);
        }
        return DubboResult.success();
    }

    /**
     * 拼装联系人数据
     * @param contactPersonAddDubboDto  传入联系人数据
     * <AUTHOR>
     * @date 2023/5/15 16:03
     * @return com.ce.scrm.customer.service.business.entity.dto.ContactPersonAddBusinessDto
     **/
    private ContactPersonAddBusinessDto packageContactPersonData(ContactPersonAddDubboDto contactPersonAddDubboDto) {
        //拼装联系人数据
        ContactPersonAddBusinessDto contactPersonAddBusinessDto = BeanUtil.copyProperties(contactPersonAddDubboDto, ContactPersonAddBusinessDto.class);
        //生成联系人ID
        String contactPersonId = uniqueIdService.getId();
        contactPersonAddBusinessDto.setContactPersonId(contactPersonId);
        //省
        String provinceCode;
        AreaBusinessView provinceData;
        if (StrUtil.isNotBlank(provinceCode = contactPersonAddDubboDto.getProvinceCode()) && (provinceData = areaBusiness.get(provinceCode)) != null) {
            contactPersonAddBusinessDto.setProvinceName(provinceData.getName());
        }
        //市
        String cityCode;
        AreaBusinessView cityData;
        if (StrUtil.isNotBlank(cityCode = contactPersonAddDubboDto.getCityCode()) && (cityData = areaBusiness.get(cityCode)) != null) {
            contactPersonAddBusinessDto.setCityName(cityData.getName());
        }
        //区
        String districtCode;
        AreaBusinessView districtData;
        if (StrUtil.isNotBlank(districtCode = contactPersonAddDubboDto.getDistrictCode()) && (districtData = areaBusiness.get(districtCode)) != null) {
            contactPersonAddBusinessDto.setDistrictName(districtData.getName());
        }
        //拼装联系方式数据
        List<ContactPersonAddBusinessDto.ContactInfoAddBusinessDto> contactInfoAddBusinessDtoList = Arrays.stream(contactPersonAddDubboDto.getPhone().split(UtilConstant.DATA_SEPARATOR)).map(phone -> {
            ContactPersonAddBusinessDto.ContactInfoAddBusinessDto contactInfoAddBusinessDto = new ContactPersonAddBusinessDto.ContactInfoAddBusinessDto();
            contactInfoAddBusinessDto.setContactType(ContactInfoEnum.mobile.getType());
            contactInfoAddBusinessDto.setContactWay(phone);
            contactInfoAddBusinessDto.setPhoneFlag(1);
            contactInfoAddBusinessDto.setPhoneVerifiedFlag(contactPersonAddDubboDto.getPhoneVerifiedFlag());
            return contactInfoAddBusinessDto;
        }).collect(Collectors.toList());
        if (StrUtil.isNotBlank(contactPersonAddDubboDto.getWechat())) {
            ContactPersonAddBusinessDto.ContactInfoAddBusinessDto contactInfoAddBusinessDto = new ContactPersonAddBusinessDto.ContactInfoAddBusinessDto();
            contactInfoAddBusinessDto.setContactType(ContactInfoEnum.wechat.getType());
            contactInfoAddBusinessDto.setContactWay(contactPersonAddDubboDto.getWechat());
            contactInfoAddBusinessDto.setPhoneFlag(1);
            contactInfoAddBusinessDtoList.add(contactInfoAddBusinessDto);
        }
        if (StrUtil.isNotBlank(contactPersonAddDubboDto.getEmail())) {
            ContactPersonAddBusinessDto.ContactInfoAddBusinessDto contactInfoAddBusinessDto = new ContactPersonAddBusinessDto.ContactInfoAddBusinessDto();
            contactInfoAddBusinessDto.setContactType(ContactInfoEnum.email.getType());
            contactInfoAddBusinessDto.setContactWay(contactPersonAddDubboDto.getEmail());
            contactInfoAddBusinessDto.setPhoneFlag(1);
            contactInfoAddBusinessDtoList.add(contactInfoAddBusinessDto);
        }
        if (StrUtil.isNotBlank(contactPersonAddDubboDto.getTelephone())) {
            ContactPersonAddBusinessDto.ContactInfoAddBusinessDto contactInfoAddBusinessDto = new ContactPersonAddBusinessDto.ContactInfoAddBusinessDto();
            contactInfoAddBusinessDto.setContactType(ContactInfoEnum.telephone.getType());
            contactInfoAddBusinessDto.setContactWay(contactPersonAddDubboDto.getTelephone());
            contactInfoAddBusinessDto.setPhoneFlag(1);
            contactInfoAddBusinessDtoList.add(contactInfoAddBusinessDto);
        }
        if (StrUtil.isNotBlank(contactPersonAddDubboDto.getQq())) {
            ContactPersonAddBusinessDto.ContactInfoAddBusinessDto contactInfoAddBusinessDto = new ContactPersonAddBusinessDto.ContactInfoAddBusinessDto();
            contactInfoAddBusinessDto.setContactType(ContactInfoEnum.qq.getType());
            contactInfoAddBusinessDto.setContactWay(contactPersonAddDubboDto.getQq());
            contactInfoAddBusinessDto.setPhoneFlag(1);
            contactInfoAddBusinessDtoList.add(contactInfoAddBusinessDto);
        }
        if (StrUtil.isNotBlank(contactPersonAddDubboDto.getWecome())) {
            ContactPersonAddBusinessDto.ContactInfoAddBusinessDto contactInfoAddBusinessDto = new ContactPersonAddBusinessDto.ContactInfoAddBusinessDto();
            contactInfoAddBusinessDto.setContactType(ContactInfoEnum.wecome.getType());
            contactInfoAddBusinessDto.setContactWay(contactPersonAddDubboDto.getWecome());
            contactInfoAddBusinessDto.setPhoneFlag(1);
            contactInfoAddBusinessDtoList.add(contactInfoAddBusinessDto);
        }
        contactPersonAddBusinessDto.setContactInfoList(contactInfoAddBusinessDtoList);
        return contactPersonAddBusinessDto;
    }

    /**
     * 获取法人标记
     * @param customerName      客户名称
     * @param contactPersonName 联系人名称
     * <AUTHOR>
     * @date 2024/1/15 12:19
     * @return boolean
     **/
    private boolean getLegalFlag(String customerName, String contactPersonName) {
        if (StrUtil.isBlank(contactPersonName)) {
            return false;
        }
        BigDataCompanyDetail bigDataCustomerInfo = bigDataThirdService.getBigDataCustomerInfo(customerName);
        return bigDataCustomerInfo != null && contactPersonName.equals(bigDataCustomerInfo.getLegal_person());
    }

    /**
     * 更新联系人，给其他系统提供
     *
     * @param contactPersonUpdateDubboDto 联系人数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Sign
    @Override
    public DubboResult<Boolean> update(ContactPersonUpdateDubboDto contactPersonUpdateDubboDto) {
        DubboResult<?> checkParam = checkParam(contactPersonUpdateDubboDto);
        if (!checkParam.checkSuccess()) {
            return DubboResult.error(checkParam);
        }
        CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
        customerContactBusinessDto.setContactPersonId(contactPersonUpdateDubboDto.getContactPersonId());
        CustomerContactBusinessView customerContactBusinessView = customerContactBusiness.customerContactByContactId(customerContactBusinessDto);
        contactPersonUpdateDubboDto.setLegalPersonFlag(0);
        if (customerContactBusinessView != null) {
            CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
            customerBusinessDto.setCustomerId(customerContactBusinessView.getCustomerId());
            CustomerBusinessView customerBusinessView = customerBusiness.detail(customerBusinessDto);
            if (customerBusinessView != null) {
                //企业法人打标
                boolean legalFlag = getLegalFlag(customerBusinessView.getCustomerName(), contactPersonUpdateDubboDto.getContactPersonName());
                contactPersonUpdateDubboDto.setLegalPersonFlag(legalFlag ? 1 : 0);
            }
        }
        //校验联系人手机号
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getPhone())) {
            String phoneList = Arrays.stream(contactPersonUpdateDubboDto.getPhone().split(UtilConstant.DATA_SEPARATOR)).map(phoneArrStr -> phoneArrStr.split(UtilConstant.KEY_VALUE_SEPARATOR)[1]).collect(Collectors.joining(UtilConstant.DATA_SEPARATOR));
            DubboResult<?> checkPhoneResult = checkPhone(phoneList);
            if (!checkPhoneResult.checkSuccess()) {
                return DubboResult.error(checkPhoneResult);
            }
        }
        return DubboResult.success(contactPersonBusiness.update(packageContactPersonData(contactPersonUpdateDubboDto)));
    }

    @Sign
    @Override
    public DubboResult<Boolean> updatePhoneVerifiedFlag(ContactPersonUpdateDubboDto contactPersonUpdateDubboDto) {
        if (contactPersonUpdateDubboDto == null || StrUtil.isBlank(contactPersonUpdateDubboDto.getContactPersonId()) || StrUtil.isBlank(contactPersonUpdateDubboDto.getOperator())) {
            log.warn("更新联系人数据，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必要参数不能为空");
        }
        //手机号校验
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getPhone()) && !contactPersonUpdateDubboDto.getPhone().contains(UtilConstant.KEY_VALUE_SEPARATOR)) {
            log.warn("更新联系人数据，手机号传入有误，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "手机号传入有误");
        }
        if (contactPersonUpdateDubboDto.getPhoneVerifiedFlag() == null) {
            log.warn("更新联系人数据，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必要参数不能为空");
        }
//        if (contactPersonUpdateDubboDto.getPhoneVerifiedTime() == null) {
//            log.warn("更新联系人时间为空，必要参数不能为空");
//            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必要参数不能为空");
//        }
        //校验联系人手机号
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getPhone())) {
            String phoneList = Arrays.stream(contactPersonUpdateDubboDto.getPhone().split(UtilConstant.DATA_SEPARATOR)).map(phoneArrStr -> phoneArrStr.split(UtilConstant.KEY_VALUE_SEPARATOR)[1]).collect(Collectors.joining(UtilConstant.DATA_SEPARATOR));
            DubboResult<?> checkPhoneResult = checkPhone(phoneList);
            if (!checkPhoneResult.checkSuccess()) {
                return DubboResult.error(checkPhoneResult);
            }
        }
        List<ContactPersonUpdateBusinessDto.ContactInfoUpdateBusinessDto> contactInfoList = new ArrayList<>();
        ContactPersonUpdateBusinessDto.ContactInfoUpdateBusinessDto contactInfoUpdateBusinessDto = new ContactPersonUpdateBusinessDto.ContactInfoUpdateBusinessDto();
        contactInfoUpdateBusinessDto.setPhoneVerifiedFlag(contactPersonUpdateDubboDto.getPhoneVerifiedFlag());
//        contactInfoUpdateBusinessDto.setPhoneVerifiedTime(contactPersonUpdateDubboDto.getPhoneVerifiedTime());
        contactInfoUpdateBusinessDto.setContactType(1);
        contactInfoUpdateBusinessDto.setContactWay(contactPersonUpdateDubboDto.getPhone());
        contactInfoUpdateBusinessDto.setContactInfoId(contactPersonUpdateDubboDto.getContactInfoId());
        contactInfoList.add(contactInfoUpdateBusinessDto);
        ContactPersonUpdateBusinessDto contactPersonUpdateBusinessDto = new ContactPersonUpdateBusinessDto();
        contactPersonUpdateBusinessDto.setContactInfoList(contactInfoList);
        contactPersonUpdateBusinessDto.setOperator(contactPersonUpdateDubboDto.getOperator());
        contactPersonUpdateBusinessDto.setContactPersonId(contactPersonUpdateDubboDto.getContactPersonId());
        //企业法人打标
        CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
        customerContactBusinessDto.setContactPersonId(contactPersonUpdateDubboDto.getContactPersonId());
        CustomerContactBusinessView customerContactBusinessView = customerContactBusiness.customerContactByContactId(customerContactBusinessDto);
        if (customerContactBusinessView != null) {
            CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
            customerBusinessDto.setCustomerId(customerContactBusinessView.getCustomerId());
            CustomerBusinessView customerBusinessView = customerBusiness.detail(customerBusinessDto);
            if (customerBusinessView != null) {
                //获取法人标记
                Optional<BigDataCompanyDetail> bigDataCustomerInfoOptional = Optional.ofNullable(bigDataThirdService.getBigDataCustomerInfo(customerBusinessView.getCustomerName()));
                if (!bigDataCustomerInfoOptional.isPresent()) {
                    log.warn("updatePhoneVerifiedFlag-获取客户信息失败，customerName:{}", customerBusinessView.getCustomerName());
                }else{
                    //获取成功后才能更新法人标
                    BigDataCompanyDetail bigDataCustomerInfo = bigDataCustomerInfoOptional.get();
                    boolean legalFlag = Objects.equals(contactPersonUpdateDubboDto.getContactPersonName(), bigDataCustomerInfo.getLegal_person());
                    contactPersonUpdateBusinessDto.setLegalPersonFlag(legalFlag ? 1 : 0);
                }
            }
        }
        return DubboResult.success(contactPersonBusiness.updatePhoneVerifiedFlag(contactPersonUpdateBusinessDto));
    }

    @Sign
    @Override
    public DubboResult<Boolean> updateSigningPersonFlag(SigningPersonUpdateDubboDto signingPersonUpdateDubboDto) {
        if (signingPersonUpdateDubboDto == null
                || StrUtil.isBlank(signingPersonUpdateDubboDto.getCustomerId())
                || StrUtil.isBlank(signingPersonUpdateDubboDto.getOperator())
                || StrUtil.isBlank(signingPersonUpdateDubboDto.getContactPersonId())
                || StrUtil.isBlank(signingPersonUpdateDubboDto.getPhone())
                || signingPersonUpdateDubboDto.getSigningPersonFlag() == null
        ) {
            log.warn("updateSigningPersonFlag更新签名联系人，参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "参数不能为空");
        }
        //校验联系人手机号 员工绑定
        DubboResult<?> checkPhoneResult = checkPhone(signingPersonUpdateDubboDto.getPhone());
        if (!checkPhoneResult.checkSuccess()) {
            return DubboResult.error(checkPhoneResult);
        }

        //用于校验参数传输的正确性
        List<ContactPersonBusinessView> contactPersonBusinessViewList = contactPersonBusiness.detail(signingPersonUpdateDubboDto.getCustomerId(), signingPersonUpdateDubboDto.getPhone());
        if (contactPersonBusinessViewList == null) {
            log.warn("updateSigningPersonFlag更新签名联系人，根据客户ID和手机号，获取联系人数据为空，参数为:{}", JSON.toJSONString(signingPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.RETURN_NULL);
        }
        Optional<ContactPersonBusinessView> contactPersonBusinessViewOptional = contactPersonBusinessViewList.stream().filter(c -> c.getContactPersonId().equals(signingPersonUpdateDubboDto.getContactPersonId())).findFirst();
        if (!contactPersonBusinessViewOptional.isPresent()) {
            log.warn("updateSigningPersonFlag更新签名联系人，根据联系人ID获取联系人数据为空，获取联系人数据为空，参数为:{}", JSON.toJSONString(signingPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.RETURN_NULL);
        }
        ContactPersonBusinessView contactPersonBusinessView = contactPersonBusinessViewOptional.get();
        if (Objects.nonNull(contactPersonBusinessView.getSigningPersonFlag()) && contactPersonBusinessView.getSigningPersonFlag() == 2) {
            log.warn("updateSigningPersonFlag更新签名联系人，正式签单联系人不可以修改！参数为:{}", JSON.toJSONString(signingPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.CONTACT_PERSON_ALREADY_FORMAL_SIGN_CONTACT);
        }

        ContactPersonUpdateBusinessDto contactPersonUpdateBusinessDto = new ContactPersonUpdateBusinessDto();
        contactPersonUpdateBusinessDto.setContactPersonId(contactPersonBusinessView.getContactPersonId());
        contactPersonUpdateBusinessDto.setOperator(signingPersonUpdateDubboDto.getOperator());
        contactPersonUpdateBusinessDto.setSigningPersonFlag(signingPersonUpdateDubboDto.getSigningPersonFlag());
        contactPersonUpdateBusinessDto.setSigningPersonUpdate(LocalDateTime.now());
        if (Objects.equals(1,contactPersonBusinessView.getLegalPersonFlag())){
            //是法人不需要重新标识
        }else{
            //企业法人打标
            CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
            customerContactBusinessDto.setContactPersonId(contactPersonBusinessView.getContactPersonId());
            CustomerContactBusinessView customerContactBusinessView = customerContactBusiness.customerContactByContactId(customerContactBusinessDto);
            if (customerContactBusinessView != null) {
                CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
                customerBusinessDto.setCustomerId(customerContactBusinessView.getCustomerId());
                CustomerBusinessView customerBusinessView = customerBusiness.detail(customerBusinessDto);
                if (customerBusinessView != null) {
                    //获取法人标记
                    Optional<BigDataCompanyDetail> bigDataCustomerInfoOptional = Optional.ofNullable(bigDataThirdService.getBigDataCustomerInfo(customerBusinessView.getCustomerName()));
                    if (!bigDataCustomerInfoOptional.isPresent()) {
                        log.warn("updatePhoneVerifiedFlag-获取客户信息失败，customerName:{}", customerBusinessView.getCustomerName());
                    }else{
                        //获取成功后才能更新法人标
                        BigDataCompanyDetail bigDataCustomerInfo = bigDataCustomerInfoOptional.get();
                        boolean legalFlag = Objects.equals(contactPersonBusinessView.getContactPersonName(), bigDataCustomerInfo.getLegal_person());
                        contactPersonUpdateBusinessDto.setLegalPersonFlag(legalFlag ? 1 : 0);
                    }
                }
            }
        }
        return DubboResult.success(contactPersonBusiness.update(contactPersonUpdateBusinessDto));
    }

    @Sign
    @Override
    public DubboResult<List<CustomerDubboView>> getSigningPersonDetailData(SigningPersonDetailDubboDto signingPersonDetailDubboDto) {
        if (signingPersonDetailDubboDto == null
                || StrUtil.isBlank(signingPersonDetailDubboDto.getPhone())
                || StrUtil.isBlank(signingPersonDetailDubboDto.getContactPersonName())
        ) {
            log.warn("getSigningPersonDetailData获取签名联系人详情数据，参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "参数不能为空");
        }
        List<CustomerBusinessView> customerBusinessViewList = contactPersonBusiness.getSigningPersonDataDetail(signingPersonDetailDubboDto.getContactPersonName(), signingPersonDetailDubboDto.getPhone());
        if (CollectionUtil.isEmpty(customerBusinessViewList)){
            log.warn("getSigningPersonDetailData获取签名联系人详情数据，根据联系人姓名和手机号，获取客户数据为空，参数为:{}", JSON.toJSONString(signingPersonDetailDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.RETURN_NULL);
        }
        //拷贝dubbo层的属性类中
        List<CustomerDubboView> customerDubboViewList = customerBusinessViewList.stream().map(customerBusinessView -> {
            CustomerDubboView customerDubboView = BeanUtil.copyProperties(customerBusinessView, CustomerDubboView.class);
            if (customerBusinessView.getContactPersonList() != null){
                List<ContactPersonDubboView> contactPersonDubboViewList = customerBusinessView.getContactPersonList().stream()
                        .map(contactPerson -> {
                            ContactPersonDubboView contactPersonDubboView = BeanUtil.copyProperties(contactPerson, ContactPersonDubboView.class);
                            if (contactPerson.getContactInfoList() != null){
                                List<ContactInfoDubboView> contactInfoDubboViewList = contactPerson.getContactInfoList().stream()
                                        .map(contactInfo -> BeanUtil.copyProperties(contactInfo, ContactInfoDubboView.class))
                                        .collect(Collectors.toList());
                                contactPersonDubboView.setContactInfoList(contactInfoDubboViewList);
                            }
                            return contactPersonDubboView;
                        })
                        .collect(Collectors.toList());
                customerDubboView.setContactPersonDubboViewList(contactPersonDubboViewList);
            }
            return customerDubboView;
        }).collect(Collectors.toList());
        return DubboResult.success(customerDubboViewList);
    }

    /**
     * 更新联系人参数校验
     * @param contactPersonUpdateDubboDto   更新参数
     * <AUTHOR>
     * @date 2023/5/18 12:45
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<?>
     **/
    private DubboResult<?> checkParam(ContactPersonUpdateDubboDto contactPersonUpdateDubboDto) {
        if (contactPersonUpdateDubboDto == null || StrUtil.isBlank(contactPersonUpdateDubboDto.getContactPersonId()) || StrUtil.isBlank(contactPersonUpdateDubboDto.getOperator())) {
            log.warn("更新联系人数据，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必要参数不能为空");
        }
        //手机号校验
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getPhone()) && !contactPersonUpdateDubboDto.getPhone().contains(UtilConstant.KEY_VALUE_SEPARATOR)) {
            log.warn("更新联系人数据，手机号传入有误，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "手机号传入有误");
        }
        //微信校验
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getWechat()) && !contactPersonUpdateDubboDto.getWechat().contains(UtilConstant.KEY_VALUE_SEPARATOR)) {
            log.warn("更新联系人数据，微信号传入有误，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "微信号传入有误");
        }
        //邮箱校验
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getEmail()) && !contactPersonUpdateDubboDto.getEmail().contains(UtilConstant.KEY_VALUE_SEPARATOR)) {
            log.warn("更新联系人数据，邮箱传入有误，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "邮箱传入有误");
        }
        //电话校验
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getTelephone()) && !contactPersonUpdateDubboDto.getTelephone().contains(UtilConstant.KEY_VALUE_SEPARATOR)) {
            log.warn("更新联系人数据，电话传入有误，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "电话传入有误");
        }
        //qq校验
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getQq()) && !contactPersonUpdateDubboDto.getQq().contains(UtilConstant.KEY_VALUE_SEPARATOR)) {
            log.warn("更新联系人数据，qq传入有误，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "qq传入有误");
        }
        //企业微信号校验
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getWecome()) && !contactPersonUpdateDubboDto.getWecome().contains(UtilConstant.KEY_VALUE_SEPARATOR)) {
            log.warn("更新联系人数据，企业微信号传入有误，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "企业微信号传入有误");
        }
        ContactPersonBusinessView contactPersonBusinessView = contactPersonBusiness.detail(contactPersonUpdateDubboDto.getContactPersonId());
        if (contactPersonBusinessView == null || !contactPersonBusinessView.getSourceKey().equals(contactPersonUpdateDubboDto.getSourceKey())) {
            log.warn("更新联系人数据，当前来源下没有此联系人，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "当前来源下没有此联系人");
        }
        //联系方式重复校验
        List<ContactInfoBusinessView> contactInfoList = contactPersonBusinessView.getContactInfoList();
        if (CollectionUtil.isEmpty(contactInfoList)) {
            return DubboResult.success();
        }
        List<String> phoneList = contactInfoList.stream().filter(c -> ContactInfoEnum.mobile.getType().equals(c.getContactType())).map(ContactInfoBusinessView::getContactWay).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(phoneList) && StrUtil.isNotBlank(contactPersonUpdateDubboDto.getPhone()) && Arrays.stream(contactPersonUpdateDubboDto.getPhone().split(UtilConstant.DATA_SEPARATOR)).anyMatch(s -> s.split(UtilConstant.KEY_VALUE_SEPARATOR).length > 1 && phoneList.contains(s.split(UtilConstant.KEY_VALUE_SEPARATOR)[1]))) {
            log.warn("更新联系人数据，手机号已存在，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "手机号已存在");
        }
        List<String> wechatList = contactInfoList.stream().filter(c -> ContactInfoEnum.wechat.getType().equals(c.getContactType())).map(ContactInfoBusinessView::getContactWay).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(wechatList) && StrUtil.isNotBlank(contactPersonUpdateDubboDto.getWechat()) && contactPersonUpdateDubboDto.getWechat().split(UtilConstant.KEY_VALUE_SEPARATOR).length > 1 && wechatList.contains(contactPersonUpdateDubboDto.getWechat().split(UtilConstant.KEY_VALUE_SEPARATOR)[1])) {
            log.warn("更新联系人数据，微信号已存在，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "微信号已存在");
        }
        List<String> emailList = contactInfoList.stream().filter(c -> ContactInfoEnum.email.getType().equals(c.getContactType())).map(ContactInfoBusinessView::getContactWay).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(emailList) && StrUtil.isNotBlank(contactPersonUpdateDubboDto.getEmail()) && contactPersonUpdateDubboDto.getEmail().split(UtilConstant.KEY_VALUE_SEPARATOR).length > 1 && emailList.contains(contactPersonUpdateDubboDto.getEmail().split(UtilConstant.KEY_VALUE_SEPARATOR)[1])) {
            log.warn("更新联系人数据，邮箱已存在，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "邮箱已存在");
        }
        List<String> telephoneList = contactInfoList.stream().filter(c -> ContactInfoEnum.telephone.getType().equals(c.getContactType())).map(ContactInfoBusinessView::getContactWay).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(telephoneList) && StrUtil.isNotBlank(contactPersonUpdateDubboDto.getTelephone()) && contactPersonUpdateDubboDto.getTelephone().split(UtilConstant.KEY_VALUE_SEPARATOR).length > 1 && telephoneList.contains(contactPersonUpdateDubboDto.getTelephone().split(UtilConstant.KEY_VALUE_SEPARATOR)[1])) {
            log.warn("更新联系人数据，电话已存在，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "电话已存在");
        }
        List<String> qqList = contactInfoList.stream().filter(c -> ContactInfoEnum.qq.getType().equals(c.getContactType())).map(ContactInfoBusinessView::getContactWay).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(qqList) && StrUtil.isNotBlank(contactPersonUpdateDubboDto.getQq()) && contactPersonUpdateDubboDto.getQq().split(UtilConstant.KEY_VALUE_SEPARATOR).length > 1 && qqList.contains(contactPersonUpdateDubboDto.getQq().split(UtilConstant.KEY_VALUE_SEPARATOR)[1])) {
            log.warn("更新联系人数据，qq已存在，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "qq已存在");
        }
        List<String> wecomeList = contactInfoList.stream().filter(c -> ContactInfoEnum.wecome.getType().equals(c.getContactType())).map(ContactInfoBusinessView::getContactWay).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(wecomeList) && StrUtil.isNotBlank(contactPersonUpdateDubboDto.getWecome()) && contactPersonUpdateDubboDto.getWecome().split(UtilConstant.KEY_VALUE_SEPARATOR).length > 1 && wecomeList.contains(contactPersonUpdateDubboDto.getWecome().split(UtilConstant.KEY_VALUE_SEPARATOR)[1])) {
            log.warn("更新联系人数据，企业微信已存在，参数为:{}", JSON.toJSONString(contactPersonUpdateDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "企业微信已存在");
        }
        return DubboResult.success();
    }

    /**
     * 拼装更新联系人参数数据
     *
     * @param contactPersonUpdateDubboDto   更新参数
     * <AUTHOR>
     * @date 2023/5/16 14:22
     * @return com.ce.scrm.customer.service.business.entity.dto.ContactPersonUpdateBusinessDto
     **/
    private ContactPersonUpdateBusinessDto packageContactPersonData(ContactPersonUpdateDubboDto contactPersonUpdateDubboDto) {
        ContactPersonUpdateBusinessDto contactPersonUpdateBusinessDto = BeanUtil.copyProperties(contactPersonUpdateDubboDto, ContactPersonUpdateBusinessDto.class);
        //省
        String provinceCode;
        AreaBusinessView provinceData;
        if (StrUtil.isNotBlank(provinceCode = contactPersonUpdateDubboDto.getProvinceCode()) && (provinceData = areaBusiness.get(provinceCode)) != null) {
            contactPersonUpdateBusinessDto.setProvinceName(provinceData.getName());
        }
        //市
        String cityCode;
        AreaBusinessView cityData;
        if (StrUtil.isNotBlank(cityCode = contactPersonUpdateDubboDto.getCityCode()) && (cityData = areaBusiness.get(cityCode)) != null) {
            contactPersonUpdateBusinessDto.setCityName(cityData.getName());
        }
        //区
        String districtCode;
        AreaBusinessView districtData;
        if (StrUtil.isNotBlank(districtCode = contactPersonUpdateDubboDto.getDistrictCode()) && (districtData = areaBusiness.get(districtCode)) != null) {
            contactPersonUpdateBusinessDto.setDistrictName(districtData.getName());
        }
        List<ContactPersonUpdateBusinessDto.ContactInfoUpdateBusinessDto> contactInfoUpdateBusinessDtoList = new ArrayList<>();
        List<String> phoneList;
        String sourceKey = contactPersonUpdateDubboDto.getSourceKey();
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getPhone()) && CollectionUtil.isNotEmpty(phoneList = Arrays.asList(contactPersonUpdateDubboDto.getPhone().split(UtilConstant.DATA_SEPARATOR)))) {
            //拼装联系方式数据
            contactInfoUpdateBusinessDtoList.addAll(phoneList.stream().map(phoneArrStr -> getAndPackage(sourceKey, contactPersonUpdateDubboDto.getContactPersonId(), phoneArrStr, ContactInfoEnum.mobile)).collect(Collectors.toList()));
        }
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getWechat())) {
            contactInfoUpdateBusinessDtoList.add(getAndPackage(sourceKey, contactPersonUpdateDubboDto.getContactPersonId(), contactPersonUpdateDubboDto.getWechat(), ContactInfoEnum.wechat));
        }
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getEmail())) {
            contactInfoUpdateBusinessDtoList.add(getAndPackage(sourceKey, contactPersonUpdateDubboDto.getContactPersonId(), contactPersonUpdateDubboDto.getEmail(), ContactInfoEnum.email));
        }
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getTelephone())) {
            contactInfoUpdateBusinessDtoList.add(getAndPackage(sourceKey, contactPersonUpdateDubboDto.getContactPersonId(), contactPersonUpdateDubboDto.getTelephone(), ContactInfoEnum.telephone));
        }
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getQq())) {
            contactInfoUpdateBusinessDtoList.add(getAndPackage(sourceKey, contactPersonUpdateDubboDto.getContactPersonId(), contactPersonUpdateDubboDto.getQq(), ContactInfoEnum.qq));
        }
        if (StrUtil.isNotBlank(contactPersonUpdateDubboDto.getWecome())) {
            contactInfoUpdateBusinessDtoList.add(getAndPackage(sourceKey, contactPersonUpdateDubboDto.getContactPersonId(), contactPersonUpdateDubboDto.getWecome(), ContactInfoEnum.wecome));
        }
        contactPersonUpdateBusinessDto.setContactInfoList(contactInfoUpdateBusinessDtoList);
        return contactPersonUpdateBusinessDto;
    }

    /**
     * 获取老的联系方式并拼装新的联系方式
     * @param sourceKey         来源key
     * @param contactPersonId   联系人ID
     * @param contactWayArrStr  联系方式数据
     * @param contactInfoEnum   联系方式类型
     * <AUTHOR>
     * @date 2023/5/16 15:06
     * @return com.ce.scrm.customer.service.business.entity.dto.ContactPersonUpdateBusinessDto.ContactInfoUpdateBusinessDto
     **/
    private ContactPersonUpdateBusinessDto.ContactInfoUpdateBusinessDto getAndPackage(String sourceKey, String contactPersonId, String contactWayArrStr, ContactInfoEnum contactInfoEnum) {
        String[] contactWayArray = contactWayArrStr.split(UtilConstant.KEY_VALUE_SEPARATOR);
        ContactPersonUpdateBusinessDto.ContactInfoUpdateBusinessDto contactInfoUpdateBusinessDto = new ContactPersonUpdateBusinessDto.ContactInfoUpdateBusinessDto();
        if (StrUtil.isNotBlank(contactWayArray[0])) {
            ContactInfoBusinessDto contactInfoBusinessDto = new ContactInfoBusinessDto();
            contactInfoBusinessDto.setContactType(contactInfoEnum.getType());
            contactInfoBusinessDto.setContactWay(contactWayArray[0]);
            contactInfoBusinessDto.setContactPersonId(contactPersonId);
            contactInfoBusinessDto.setSourceKey(sourceKey);
            ContactInfoBusinessView contactInfoBusinessView = contactInfoBusiness.getCustomerContactInfoData(contactInfoBusinessDto);
            if (contactInfoBusinessView == null) {
                contactInfoUpdateBusinessDto.setContactType(contactInfoEnum.getType());
            } else {
                contactInfoUpdateBusinessDto.setContactInfoId(contactInfoBusinessView.getContactInfoId());
            }
        } else {
            contactInfoUpdateBusinessDto.setContactType(contactInfoEnum.getType());
        }
        if (contactWayArray.length > 1) {
            contactInfoUpdateBusinessDto.setContactWay(contactWayArray[1]);
        }
        contactInfoUpdateBusinessDto.setSourceKey(sourceKey);
        return contactInfoUpdateBusinessDto;
    }

    /**
     * 添加默认标记
     *
     * @param orderDefaultFlagDubboDto 打标参数
     * <AUTHOR>
     * @date 2023/5/23 09:43
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Sign
    @Override
    public DubboResult<Boolean> addDefaultFlag(OrderDefaultFlagDubboDto orderDefaultFlagDubboDto) {
        if (orderDefaultFlagDubboDto == null || StrUtil.isBlank(orderDefaultFlagDubboDto.getContactPersonId()) || StrUtil.isBlank(orderDefaultFlagDubboDto.getPhone()) || StrUtil.isBlank(orderDefaultFlagDubboDto.getEmail()) || StrUtil.isBlank(orderDefaultFlagDubboDto.getOperator())) {
            log.warn("联系人打标，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必要参数不能为空");
        }
        return DubboResult.success(orderDefaultFlagBusiness.add(orderDefaultFlagDubboDto.getContactPersonId(), orderDefaultFlagDubboDto.getPhone(), orderDefaultFlagDubboDto.getEmail(), orderDefaultFlagDubboDto.getOperator()));
    }

    /**
     * 联系人绑定微信unionId
     *
     * @param contactPersonBindUnionIdDubboDto 绑定参数
     * <AUTHOR>
     * @date 2023/6/16 09:51
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Sign
    @Override
    public DubboResult<Boolean> bindUnionId(ContactPersonBindUnionIdDubboDto contactPersonBindUnionIdDubboDto) {
        String operator = contactPersonBindUnionIdDubboDto.getOperator();
        if (contactPersonBindUnionIdDubboDto == null || StrUtil.isBlank(contactPersonBindUnionIdDubboDto.getContactPersonId()) || StrUtil.isBlank(contactPersonBindUnionIdDubboDto.getUnionId()) || StrUtil.isBlank(operator)) {
            log.warn("联系人绑定unionId，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必要参数不能为空");
        }
        //绑定类型不传默认为微信类型
        if (contactPersonBindUnionIdDubboDto.getBindType() == null) {
            contactPersonBindUnionIdDubboDto.setBindType(BindTypeEnum.WECHAT.getType());
        }
        //绑定类型校验
        if (!BindTypeEnum.check(contactPersonBindUnionIdDubboDto.getBindType())) {
            log.warn("联系人绑定unionId，绑定类型传入异常");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "绑定类型传入异常");
        }
        ContactPersonBusinessView contactPersonBusinessView = contactPersonBusiness.detail(contactPersonBindUnionIdDubboDto.getContactPersonId());
        if (StrUtil.isNotBlank(contactPersonBusinessView.getUnionId())) {
            log.warn("联系人绑定unionId,{}", DubboCodeMessageEnum.CONTACT_PERSON_UNION_ID_ALREADY_BIND.getMessage());
            return DubboResult.error(DubboCodeMessageEnum.CONTACT_PERSON_UNION_ID_ALREADY_BIND);
        }
        String contactPersonName = contactPersonBusinessView.getContactPersonName();
        contactPersonBusinessView = contactPersonBusiness.getByUnionId(contactPersonBindUnionIdDubboDto.getBindType(), contactPersonBindUnionIdDubboDto.getUnionId());
        if (contactPersonBusinessView != null) {
            log.warn("联系人绑定unionId,{}", DubboCodeMessageEnum.UNION_ID_ALREADY_USED.getMessage());
            return DubboResult.error(DubboCodeMessageEnum.UNION_ID_ALREADY_USED);
        }
        Boolean result = contactPersonBusiness.bindUnionId(contactPersonBindUnionIdDubboDto.getContactPersonId(), contactPersonBindUnionIdDubboDto.getBindType(), contactPersonBindUnionIdDubboDto.getUnionId(),contactPersonBindUnionIdDubboDto.getWechatNickName(),contactPersonBindUnionIdDubboDto.getSourceKey(), operator);
        if (result){
            CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
            customerContactBusinessDto.setContactPersonId(contactPersonBindUnionIdDubboDto.getContactPersonId());
            CustomerContactBusinessView customerContactBusinessView = customerContactBusiness.customerContactByContactId(customerContactBusinessDto);
            if (customerContactBusinessView!=null){

                CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
                customerBusinessDto.setCustomerId(customerContactBusinessView.getCustomerId());
                CustomerBusinessView detail = customerBusiness.detail(customerBusinessDto);
                String sourceDataId = detail.getSourceDataId();
                String protectSalerId = detail.getProtectSalerId();
                Integer tagRetainCust = detail.getTagRetainCust();
                CompleteOrgInfo completeOrgInfo = employeeThirdService.getParentOrgByEmployeeId(operator);
                if (Objects.isNull(completeOrgInfo)) {
                    log.error("绑定联系人时获取员工={}数据失败 contactPersonBindUnionIdDubboDto={}",operator,JSON.toJSONString(contactPersonBindUnionIdDubboDto));
                    return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM,"未找到员工");
                }

                CustomerBindWx customerBindWx = new CustomerBindWx();
                customerBindWx.setType(0);
                customerBindWx.setBindType(2);
                customerBindWx.setPid(sourceDataId);
                customerBindWx.setCustomerId(customerContactBusinessView.getCustomerId());
                customerBindWx.setContactPersonId(contactPersonBindUnionIdDubboDto.getContactPersonId());
                customerBindWx.setBindUserType(contactPersonBindUnionIdDubboDto.getBindType());
                customerBindWx.setUnionid(contactPersonBindUnionIdDubboDto.getUnionId());
                customerBindWx.setWxName(contactPersonBindUnionIdDubboDto.getWechatNickName());
                customerBindWx.setDeptId(completeOrgInfo.getOrgId());
                customerBindWx.setDeptName(completeOrgInfo.getOrgName());
                customerBindWx.setAreaId(completeOrgInfo.getAreaId());
                customerBindWx.setAreaName(completeOrgInfo.getAreaName());
                customerBindWx.setSubId(completeOrgInfo.getSubId());
                customerBindWx.setSubName(completeOrgInfo.getSubName());
                customerBindWx.setBuId(completeOrgInfo.getBuId());
                customerBindWx.setBuName(completeOrgInfo.getBuName());

                if (Objects.equals(tagRetainCust,1)){
                    customerBindWx.setIsTradeWhenOperate(1);
                }else {
                    customerBindWx.setIsTradeWhenOperate(0);
                }
                if (Objects.equals(protectSalerId,customerBindWx.getCreateId())){
                    customerBindWx.setIsSameSalerWhenOperation(1);
                }else {
                    customerBindWx.setIsSameSalerWhenOperation(0);
                }
                if (StrUtil.isNotBlank(protectSalerId)){
                    customerBindWx.setIsProtectedWhenOperate(1);
                }else {
                    customerBindWx.setIsProtectedWhenOperate(0);
                }
                customerBindWx.setCreateId(operator);
                customerBindWx.setCreateTime(new Date());
                customerBindWx.setUpdateTime(new Date());

                customerBindWxService.save(customerBindWx);

                //绑定成功之后 发送MQ 写入绑定事件
                WechatBindData wechatBindData = new WechatBindData();
                wechatBindData.setType(1);
                wechatBindData.setCustomerId(customerContactBusinessView.getCustomerId());
                wechatBindData.setContactPersonId(contactPersonBindUnionIdDubboDto.getContactPersonId());
                wechatBindData.setContactPersonName(contactPersonName);
                wechatBindData.setBindType(contactPersonBindUnionIdDubboDto.getBindType());
                wechatBindData.setUnionId(contactPersonBindUnionIdDubboDto.getUnionId());
                wechatBindData.setWechatNickName(contactPersonBindUnionIdDubboDto.getWechatNickName());
                wechatBindData.setOperator(operator);
                wechatBindData.setOperatorTime(new Date());
                sendMqService.sendWechatBindMq(JSONObject.toJSONString(wechatBindData));
            }
        }
        return DubboResult.success(result);
    }

    /**
     * 联系人解绑微信unionId
     *
     * @param contactPersonUnbindUnionIdDubboDto 解绑参数
     * <AUTHOR>
     * @date 2023/6/16 09:54
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Sign
    @Override
    public DubboResult<Boolean> unbindUnionId(ContactPersonUnbindUnionIdDubboDto contactPersonUnbindUnionIdDubboDto) {
        String operator = contactPersonUnbindUnionIdDubboDto.getOperator();
        if (contactPersonUnbindUnionIdDubboDto == null || StrUtil.isBlank(contactPersonUnbindUnionIdDubboDto.getContactPersonId()) || StrUtil.isBlank(operator)) {
            log.warn("联系人解绑unionId，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必要参数不能为空");
        }
        //绑定类型不传默认为微信类型
        if (contactPersonUnbindUnionIdDubboDto.getBindType() == null) {
            contactPersonUnbindUnionIdDubboDto.setBindType(BindTypeEnum.WECHAT.getType());
        }
        //绑定类型校验
        if (!BindTypeEnum.check(contactPersonUnbindUnionIdDubboDto.getBindType())) {
            log.warn("联系人绑定unionId，绑定类型传入异常");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "绑定类型传入异常");
        }
        ContactPersonBusinessView contactPersonBusinessView = contactPersonBusiness.detail(contactPersonUnbindUnionIdDubboDto.getContactPersonId());
        String unionIdOld = contactPersonBusinessView.getUnionId();
        if (StrUtil.isBlank(unionIdOld)) {
            log.warn("联系人解绑unionId,{}", DubboCodeMessageEnum.CONTACT_PERSON_UNION_ID_ALREADY_UNBIND.getMessage());
            return DubboResult.error(DubboCodeMessageEnum.CONTACT_PERSON_UNION_ID_ALREADY_UNBIND);
        }
        if (!contactPersonUnbindUnionIdDubboDto.getBindType().equals(contactPersonBusinessView.getBindType())) {
            log.warn("联系人解绑unionId,{}", DubboCodeMessageEnum.CONTACT_PERSON_UNBIND_TYPE_EXCEPTION.getMessage());
            return DubboResult.error(DubboCodeMessageEnum.CONTACT_PERSON_UNBIND_TYPE_EXCEPTION);
        }
        String wechatNickNameOld = contactPersonBusinessView.getWechatNickName();
        Boolean result = contactPersonBusiness.unbindUnionId(contactPersonUnbindUnionIdDubboDto.getContactPersonId(), contactPersonUnbindUnionIdDubboDto.getSourceKey(), operator);
        if (result){
            CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
            customerContactBusinessDto.setContactPersonId(contactPersonUnbindUnionIdDubboDto.getContactPersonId());
            CustomerContactBusinessView customerContactBusinessView = customerContactBusiness.customerContactByContactId(customerContactBusinessDto);
            if (customerContactBusinessView!=null){
                //解绑成功之后 发送MQ 写入解绑事件
                WechatBindData wechatBindData = new WechatBindData();
                wechatBindData.setType(2);
                wechatBindData.setCustomerId(customerContactBusinessView.getCustomerId());
                wechatBindData.setContactPersonId(contactPersonUnbindUnionIdDubboDto.getContactPersonId());
                wechatBindData.setContactPersonName(contactPersonBusinessView.getContactPersonName());
                wechatBindData.setBindType(contactPersonUnbindUnionIdDubboDto.getBindType());
                wechatBindData.setOperator(contactPersonUnbindUnionIdDubboDto.getOperator());
                wechatBindData.setOperatorTime(new Date());
                sendMqService.sendWechatBindMq(JSONObject.toJSONString(wechatBindData));

                CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
                customerBusinessDto.setCustomerId(customerContactBusinessView.getCustomerId());
                CustomerBusinessView detail = customerBusiness.detail(customerBusinessDto);
                String sourceDataId = detail.getSourceDataId();
                String protectSalerId = detail.getProtectSalerId();
                Integer tagRetainCust = detail.getTagRetainCust();
                CompleteOrgInfo completeOrgInfo = employeeThirdService.getParentOrgByEmployeeId(operator);
                if (Objects.isNull(completeOrgInfo)) {
                    log.error("解绑联系人时获取员工={}数据失败 contactPersonBindUnionIdDubboDto={}",operator,JSON.toJSONString(contactPersonUnbindUnionIdDubboDto));
                    return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM,"未找到员工");
                }

                CustomerBindWx customerBindWx = new CustomerBindWx();
                customerBindWx.setType(1);
                customerBindWx.setBindType(2);
                customerBindWx.setPid(sourceDataId);
                customerBindWx.setCustomerId(customerContactBusinessView.getCustomerId());
                customerBindWx.setContactPersonId(contactPersonUnbindUnionIdDubboDto.getContactPersonId());
                customerBindWx.setBindUserType(contactPersonUnbindUnionIdDubboDto.getBindType());
                customerBindWx.setUnionid(unionIdOld);
                customerBindWx.setWxName(wechatNickNameOld);
                customerBindWx.setDeptId(completeOrgInfo.getOrgId());
                customerBindWx.setDeptName(completeOrgInfo.getOrgName());
                customerBindWx.setAreaId(completeOrgInfo.getAreaId());
                customerBindWx.setAreaName(completeOrgInfo.getAreaName());
                customerBindWx.setSubId(completeOrgInfo.getSubId());
                customerBindWx.setSubName(completeOrgInfo.getSubName());
                customerBindWx.setBuId(completeOrgInfo.getBuId());
                customerBindWx.setBuName(completeOrgInfo.getBuName());

                if (Objects.equals(tagRetainCust,1)){
                    customerBindWx.setIsTradeWhenOperate(1);
                }else {
                    customerBindWx.setIsTradeWhenOperate(0);
                }
                if (Objects.equals(protectSalerId,customerBindWx.getCreateId())){
                    customerBindWx.setIsSameSalerWhenOperation(1);
                }else {
                    customerBindWx.setIsSameSalerWhenOperation(0);
                }
                if (StrUtil.isNotBlank(protectSalerId)){
                    customerBindWx.setIsProtectedWhenOperate(1);
                }else {
                    customerBindWx.setIsProtectedWhenOperate(0);
                }
                customerBindWx.setCreateId(operator);
                customerBindWx.setCreateTime(new Date());
                customerBindWx.setUpdateTime(new Date());
                customerBindWxService.save(customerBindWx);
            }
        }
        return DubboResult.success(result);
    }

    /**
     * 根据绑定unionId获取联系人客户数据
     *
     * @param bindUnionIdQueryDubboDto 查询参数
     * <AUTHOR>
     * @date 2023/6/16 10:28
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonCustomerDataDubboView>>
     **/
    @Sign
    @Override
    public DubboResult<List<ContactPersonCustomerDataDubboView>> getContactPersonDataByUnionId(BindUnionIdQueryDubboDto bindUnionIdQueryDubboDto) {
        if (bindUnionIdQueryDubboDto == null || CollectionUtil.isEmpty(bindUnionIdQueryDubboDto.getUnionIdList())) {
            log.warn("根据绑定unionId获取联系人客户数据，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必要参数不能为空");
        }
        List<ContactPersonBusinessView> contactPersonBusinessViewList = contactPersonBusiness.list(bindUnionIdQueryDubboDto.getUnionIdList());
        return DubboResult.success(contactPersonBusinessViewList.stream().map(contactPersonBusinessView -> {
            ContactPersonCustomerDataDubboView contactPersonCustomerDataDubboView = BeanUtil.copyProperties(contactPersonBusinessView, ContactPersonCustomerDataDubboView.class);
            if (CollectionUtil.isNotEmpty(contactPersonBusinessView.getContactInfoList())) {
                contactPersonCustomerDataDubboView.setContactInfoDataDubboViewList(BeanUtil.copyToList(contactPersonBusinessView.getContactInfoList(), ContactPersonCustomerDataDubboView.ContactInfoDataDubboView.class));
            }
            CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
            customerContactBusinessDto.setContactPersonId(contactPersonBusinessView.getContactPersonId());
            CustomerContactBusinessView customerContactBusinessView = customerContactBusiness.customerContactByContactId(customerContactBusinessDto);
            if (customerContactBusinessView == null) {
                return null;
            }
            CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
            customerBusinessDto.setCustomerId(customerContactBusinessView.getCustomerId());
            CustomerBusinessView customerBusinessView = customerBusiness.detail(customerBusinessDto);
            if (customerBusinessView == null) {
                return null;
            }
            contactPersonCustomerDataDubboView.setCustomerDataDubboView(BeanUtil.copyProperties(customerBusinessView, ContactPersonCustomerDataDubboView.CustomerDataDubboView.class));
            return contactPersonCustomerDataDubboView;
        }).filter(contactPersonCustomerDataDubboView -> contactPersonCustomerDataDubboView != null && contactPersonCustomerDataDubboView.getCustomerDataDubboView() != null).collect(Collectors.toList()));
    }


    
    /***
     * 根据员工ID和unionIdList 查询联系人列表
     * @param bindUnionIdQueryDubboDto
     * <AUTHOR>
     * @date 2024/11/25 19:56
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.customer.dubbo.entity.view.WechatBindCustomerInfoDubboView>>
    **/
    @Sign
    @Override
    public DubboResult<List<WechatBindCustomerInfoDubboView>> getWechatBindCustomerInfoUnionId(BindUnionIdQueryDubboDto bindUnionIdQueryDubboDto) {
        if (bindUnionIdQueryDubboDto == null || CollectionUtil.isEmpty(bindUnionIdQueryDubboDto.getEmpIdList()) || CollectionUtil.isEmpty(bindUnionIdQueryDubboDto.getUnionIdList())) {
            log.warn("根据绑定unionId获取联系人客户数据，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必要参数不能为空");
        }
        String empId= bindUnionIdQueryDubboDto.getEmpIdList().get(0);
        List<ContactPersonBusinessView> contactPersonBusinessViewList = contactPersonBusiness.getContactPersonListByEmpIdAndUnionIdList(empId,bindUnionIdQueryDubboDto.getUnionIdList());
        // 数据库中取出所有联系人数据
        List<WechatBindCustomerInfoDubboView> collect = contactPersonBusinessViewList.stream().map(contactPersonBusinessView -> {
            CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
            customerContactBusinessDto.setContactPersonId(contactPersonBusinessView.getContactPersonId());
            CustomerContactBusinessView customerContactBusinessView = customerContactBusiness.customerContactByContactId(customerContactBusinessDto);
            if (customerContactBusinessView == null) {
                return null;
            }
            CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
            customerBusinessDto.setCustomerId(customerContactBusinessView.getCustomerId());
            CustomerBusinessView customerBusinessView = customerBusiness.detail(customerBusinessDto);
            if (customerBusinessView == null) {
                return null;
            }
            WechatBindCustomerInfoDubboView wechatBindCustomerInfoDubboView = new WechatBindCustomerInfoDubboView();
            wechatBindCustomerInfoDubboView.setContactPersonId(contactPersonBusinessView.getContactPersonId());
            wechatBindCustomerInfoDubboView.setContactPersonName(contactPersonBusinessView.getContactPersonName());
            wechatBindCustomerInfoDubboView.setBindType(contactPersonBusinessView.getBindType());
            wechatBindCustomerInfoDubboView.setUnionId(contactPersonBusinessView.getUnionId());
            wechatBindCustomerInfoDubboView.setUnionIdBindTime(contactPersonBusinessView.getUnionIdBindTime());
            wechatBindCustomerInfoDubboView.setCustomerId(contactPersonBusinessView.getContactPersonId());
            wechatBindCustomerInfoDubboView.setCustomerName(contactPersonBusinessView.getContactPersonId());
            wechatBindCustomerInfoDubboView.setOperator(contactPersonBusinessView.getOperator());
            wechatBindCustomerInfoDubboView.setWechatNickName(contactPersonBusinessView.getWechatNickName());
            return wechatBindCustomerInfoDubboView;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 按照unionId去重，取出最新一条的数据
        return DubboResult.success(collect.stream()
                .collect(Collectors.toMap(
                        WechatBindCustomerInfoDubboView::getUnionId,
                        d -> d,
                        (d1, d2) -> d1.getUnionIdBindTime().isAfter(d2.getUnionIdBindTime()) ? d1 : d2
                )).values().stream().collect(Collectors.toList()));
    }

    /***
     * 根据员工ID 查询绑定联系人列表
     * 分页
     * @param bindUnionIdQueryDubboDto
     * <AUTHOR>
     * @date 2024/11/25 20:54
     * @version 1.0.0
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView>
     **/
    @Override
    public DubboResult<DubboPageInfo<WechatBindCustomerInfoDubboView>> getWechatBindCustomerInfoEmpId(BindUnionIdQueryDubboDto bindUnionIdQueryDubboDto) {
        if (bindUnionIdQueryDubboDto == null || CollectionUtil.isEmpty(bindUnionIdQueryDubboDto.getEmpIdList())) {
            log.warn("根据绑定unionId获取联系人客户数据，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必要参数不能为空");
        }
        if (bindUnionIdQueryDubboDto.getEmpIdList().size() > 150) {
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "员工ID最大长度150");
        }
        if (bindUnionIdQueryDubboDto.getUnionIdBindBeginTime()==null){
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "绑定开始时间不能为空");
        }
        if (bindUnionIdQueryDubboDto.getUnionIdBindEndTime()==null){
            bindUnionIdQueryDubboDto.setUnionIdBindEndTime(LocalDateTime.now());
        }
        if (bindUnionIdQueryDubboDto.getPageNum()==null || bindUnionIdQueryDubboDto.getPageNum()<1){
            bindUnionIdQueryDubboDto.setPageNum(1);
        }
        if (bindUnionIdQueryDubboDto.getPageSize()==null || bindUnionIdQueryDubboDto.getPageSize()>100){
            bindUnionIdQueryDubboDto.setPageSize(20);
        }
        BindUnionIdQueryBusinessDto bindUnionIdQueryBusinessDto = BeanUtil.copyProperties(bindUnionIdQueryDubboDto, BindUnionIdQueryBusinessDto.class);
        Page<ContactPersonBusinessView> page = contactPersonBusiness.getContactPersonListByEmpIdList(bindUnionIdQueryBusinessDto);
        DubboPageInfo<WechatBindCustomerInfoDubboView> dubboViewDubboPageInfo = PageUtil.pageConversion(page, WechatBindCustomerInfoDubboView.class);
        // 数据库中取出所有联系人数据
        List<WechatBindCustomerInfoDubboView> collect = page.getRecords().stream().map(contactPersonBusinessView -> {
            CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
            customerContactBusinessDto.setContactPersonId(contactPersonBusinessView.getContactPersonId());
            CustomerContactBusinessView customerContactBusinessView = customerContactBusiness.customerContactByContactId(customerContactBusinessDto);
            if (customerContactBusinessView == null) {
                return null;
            }
            CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
            customerBusinessDto.setCustomerId(customerContactBusinessView.getCustomerId());
            CustomerBusinessView customerBusinessView = customerBusiness.detail(customerBusinessDto);
            if (customerBusinessView == null) {
                return null;
            }
            WechatBindCustomerInfoDubboView wechatBindCustomerInfoDubboView = new WechatBindCustomerInfoDubboView();
            wechatBindCustomerInfoDubboView.setContactPersonId(contactPersonBusinessView.getContactPersonId());
            wechatBindCustomerInfoDubboView.setContactPersonName(contactPersonBusinessView.getContactPersonName());
            wechatBindCustomerInfoDubboView.setBindType(contactPersonBusinessView.getBindType());
            wechatBindCustomerInfoDubboView.setUnionId(contactPersonBusinessView.getUnionId());
            wechatBindCustomerInfoDubboView.setUnionIdBindTime(contactPersonBusinessView.getUnionIdBindTime());
            wechatBindCustomerInfoDubboView.setCustomerId(contactPersonBusinessView.getContactPersonId());
            wechatBindCustomerInfoDubboView.setCustomerName(contactPersonBusinessView.getContactPersonId());
            wechatBindCustomerInfoDubboView.setOperator(contactPersonBusinessView.getOperator());
            wechatBindCustomerInfoDubboView.setWechatNickName(contactPersonBusinessView.getWechatNickName());
            return wechatBindCustomerInfoDubboView;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        dubboViewDubboPageInfo.setList(collect);
        return DubboResult.success(dubboViewDubboPageInfo);
    }

    /**
     * 删除联系人
     *
     * @param contactPersonId 联系人ID
     * <AUTHOR>
     * @date 2024/1/15 13:58
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Override
    public DubboResult<Boolean> del(String contactPersonId) {
        if (StrUtil.isBlank(contactPersonId)) {
            log.warn("删除联系人，联系人ID不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "联系人ID不能为空");
        }
        return DubboResult.success(contactPersonBusiness.del(contactPersonId));
    }
}