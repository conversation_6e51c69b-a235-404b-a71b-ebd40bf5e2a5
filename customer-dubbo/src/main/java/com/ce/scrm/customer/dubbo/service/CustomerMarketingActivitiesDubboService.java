package com.ce.scrm.customer.dubbo.service;

import cn.ce.cesupport.framework.core.utils.BeanCopyUtils;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.dao.entity.abm.CustomerMarketingActivities;
import com.ce.scrm.customer.dao.entity.abm.EmpCustomerCountVo;
import com.ce.scrm.customer.dao.mapper.abm.CustomerDistributeSdrRecordMapper;
import com.ce.scrm.customer.dao.service.abm.CustomerMarketingActivitiesService;
import com.ce.scrm.customer.dubbo.aop.sign.Sign;
import com.ce.scrm.customer.dubbo.api.ICustomerMarketingActivitiesDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerMarketingActivitiesCreateDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerMarketingActivitiesPageDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerMarketingActivitiesUpdateDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.PotentialCustomerMarketingRulesDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.abm.*;
import com.ce.scrm.customer.dubbo.util.CronUtil;
import com.ce.scrm.customer.dubbo.util.PageUtil;
import com.ce.scrm.customer.dubbo.util.ValidatorUtils;
import com.ce.scrm.customer.service.business.ICustomerMarketingActivitiesBusiness;
import com.ce.scrm.customer.service.business.ICustomerMarketingActivitiesRecordBusiness;
import com.ce.scrm.customer.service.business.entity.dto.abm.CustomerMarketingActivitiesBusinessPageDto;
import com.ce.scrm.customer.service.business.entity.dto.abm.CustomerMarketingActivitiesBusinessReqDto;
import com.ce.scrm.customer.service.business.entity.view.abm.CustomerMarketingActivitiesBusinessView;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.service.enums.CustomerMarketingActivityTypeEnum;
import com.ce.scrm.customer.support.mq.RocketMqOperate;
import com.ce.scrm.customer.support.xxl.JobTemplate;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum.OPER_FAIL;

/**
 * 跨境ABM 客户营销活动dubbo业务
 * <AUTHOR>
 * @date 2025/7/9 15:50
 * @version 1.0.0
 */
@Slf4j
@DubboService(interfaceClass = ICustomerMarketingActivitiesDubbo.class)
public class CustomerMarketingActivitiesDubboService implements ICustomerMarketingActivitiesDubbo {

	@Resource
    private ICustomerMarketingActivitiesBusiness customerMarketingActivitiesBusiness;
	@Resource
    private CustomerMarketingActivitiesService customerMarketingActivitiesService;
	@Resource
	private JobTemplate jobTemplate;
	@Resource
	private RocketMqOperate rocketMqOperate;
	@Resource
	private ICustomerMarketingActivitiesRecordBusiness customerMarketingActivitiesRecordBusiness;
	@Resource
	private CustomerDistributeSdrRecordMapper customerDistributeSdrRecordMapper;

	/**
	 * 创建营销活动
	 *
	 * @param createActivityDubboDtoReq 查询参数
	 * <AUTHOR>
	 * @date 2025/7/9 16:59
	 */
	@Sign
	@Override
	public DubboResult<?> createActivity(CustomerMarketingActivitiesCreateDubboDto createActivityDubboDtoReq) {
		log.info("创建营销活动,请求参数={}", JSON.toJSONString(createActivityDubboDtoReq));
		Integer activityType = createActivityDubboDtoReq.getActivityType();
		if (CustomerMarketingActivityTypeEnum.supportCreateActivityType().contains(activityType)) {
			DubboResult<?> validateRs = ValidatorUtils.validate(createActivityDubboDtoReq);
			if (!validateRs.checkSuccess()) {
				return DubboResult.error(validateRs);
			}
		}
		if (Objects.equals(createActivityDubboDtoReq.getActivityType(), CustomerMarketingActivityTypeEnum.OTHERS.getCode())) {
			return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "触达方式为其他时，不支持创建活动");
		}
		if (Objects.equals(createActivityDubboDtoReq.getActivityType(), CustomerMarketingActivityTypeEnum.PHONE.getCode()) && CollectionUtils.isEmpty(createActivityDubboDtoReq.getSdrIds())) {
			return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "电话触达方式时，SDR不能为空");
		}

		if (createActivityDubboDtoReq.getExecuteTime().before(new Date())) {
			return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "执行时间不能在当前时间之前");
		}
		CustomerMarketingActivitiesBusinessReqDto req = BeanUtil.copyProperties(createActivityDubboDtoReq, CustomerMarketingActivitiesBusinessReqDto.class);
		Long activityId = customerMarketingActivitiesBusiness.save(req);
		List<Integer> needAddJobTypes = Lists.newArrayList(CustomerMarketingActivityTypeEnum.SMS.getCode(), CustomerMarketingActivityTypeEnum.EMAIL.getCode());
		if (Objects.nonNull(activityId) && needAddJobTypes.contains(createActivityDubboDtoReq.getActivityType())) {
			// 拉取分群code中的客户列表到customer_marketing_activities_record
			JSONObject obj = new JSONObject();
			obj.put("segmentId", req.getSegmentId());
			obj.put("activityId", activityId);
			rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CUSTOMER_CDP_ACTIVITY_SEGMENT_ID_TOPIC, JSON.toJSONString(obj));
			// 根据执行时间生成定时任务，到期执行
			int jobId = jobTemplate.addJob(createActivityDubboDtoReq.getActivityName(),
				CronUtil.toCron(req.getExecuteTime()),
				ServiceConstant.JobConstant.JobHandlerConstant.SCRM_CUSTOMER_MARKETING_ACTIVITIES_JOB_HANDLER,
				activityId.toString()
			);
			log.info("创建活动成功，活动名称={}, jobId={}", createActivityDubboDtoReq.getActivityName(), jobId);
			return DubboResult.success();
		} else {
			log.error("创建营销活动失败，请求参数={}, 活动id={}", JSON.toJSONString(req), activityId);
			return DubboResult.error(OPER_FAIL);
		}
	}

	@Transactional
	@Override
	public DubboResult<?> updateActivity(CustomerMarketingActivitiesUpdateDubboDto updateDubboDto) {
		CustomerMarketingActivities byId = customerMarketingActivitiesService.getById(updateDubboDto.getId());
		if (Objects.isNull(byId)) {
			log.error("修改营销活动，活动不存在，id={}", updateDubboDto.getId());
			return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "活动不存在");
		}
		if (byId.getExecuteState() == 1 || byId.getExecuteTime().before(new Date())) {
			log.error("修改营销活动，活动已执行，无法修改，id={}", updateDubboDto.getId());
			return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "活动已执行");
		}
		log.info("修改营销活动dubbo CustomerMarketingActivitiesDubboService#updateActivity，请求参数={}", JSON.toJSONString(updateDubboDto));
		/*
		boolean changedActivityType = !Objects.equals(byId.getActivityType(), updateDubboDto.getActivityType());
		Integer oriXxlJobId = byId.getJobId();
		Integer currXxlJobId = null;

		if (changedActivityType && Objects.nonNull(oriXxlJobId)) { // 修改了触达方式
			log.warn("修改了触达方式，取消原来未执行的定时任务并创建新的定时任务。原xxlJobId={}, 修改明细={}", oriXxlJobId, JSON.toJSONString(updateDubboDto));
			jobTemplate.removeJob(oriXxlJobId);
			currXxlJobId = jobTemplate.addJob(updateDubboDto.getActivityName(),
				CronUtil.toCron(byId.getExecuteTime()),
				ServiceConstant.JobConstant.JobHandlerConstant.SCRM_CUSTOMER_MARKETING_ACTIVITIES_JOB_HANDLER,
				updateDubboDto.getId().toString());
			byId.setJobId(currXxlJobId);
		}*/
		boolean update = customerMarketingActivitiesService.lambdaUpdate()
			.eq(CustomerMarketingActivities::getId, updateDubboDto.getId())
			// .set(Objects.nonNull(currXxlJobId), CustomerMarketingActivities::getJobId, currXxlJobId)
			.set(StringUtils.isNotBlank(updateDubboDto.getActivityName()), CustomerMarketingActivities::getActivityName, updateDubboDto.getActivityName())
			.set(CustomerMarketingActivities::getActivityType, updateDubboDto.getActivityType())
			.set(StringUtils.isNotBlank(updateDubboDto.getTemplateId()), CustomerMarketingActivities::getTemplateId, updateDubboDto.getTemplateId())
			.set(StringUtils.isNotBlank(updateDubboDto.getActivityContent()), CustomerMarketingActivities::getActivityContent, updateDubboDto.getActivityContent())
			.set(StringUtils.isNotBlank(updateDubboDto.getActivityUrl()), CustomerMarketingActivities::getActivityUrl, updateDubboDto.getActivityUrl())
			.set(StringUtils.isNotBlank(updateDubboDto.getSegmentId()), CustomerMarketingActivities::getSegmentId, updateDubboDto.getSegmentId())
			.set(Objects.nonNull(updateDubboDto.getSegmentCustCount()), CustomerMarketingActivities::getSegmentCustCount, updateDubboDto.getSegmentCustCount())
			.set(StringUtils.isNotBlank(updateDubboDto.getSegmentName()), CustomerMarketingActivities::getSegmentName, updateDubboDto.getSegmentName())
			.set(CustomerMarketingActivities::getUpdateBy, updateDubboDto.getUpdateBy())
			.set(CustomerMarketingActivities::getUpdatedTime, new Date())
			.update();
		return DubboResult.success(update);
	}

	/**
	 * 营销活动列表
	 * @param pageDubboDto 查询参数
	 * @return 营销活动列表分页
	 */
	@Sign
	@Override
	public DubboResult<DubboPageInfo<CustomerMarketingActivitiesDubboView>> activitiesPages(CustomerMarketingActivitiesPageDubboDto pageDubboDto) {
		Page<CustomerMarketingActivitiesBusinessView> page = customerMarketingActivitiesBusiness.page(BeanUtil.copyProperties(pageDubboDto, CustomerMarketingActivitiesBusinessPageDto.class));
		DubboPageInfo<CustomerMarketingActivitiesDubboView> customerDubboViewDubboPageInfo = PageUtil.pageConversion(page, CustomerMarketingActivitiesDubboView.class);
		return DubboResult.success(customerDubboViewDubboPageInfo);
	}

	/**
	 * 营销活动详情
	 * @param activitiesId 营销活动id
	 * @return 营销活动详情信息
	 */
	@Override
	public DubboResult<CustomerMarketingActivitiesDubboView> activityDetail(Long activitiesId) {
		if (Objects.isNull(activitiesId)) {
			return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "营销活动ID不能为空");
		}
		CustomerMarketingActivitiesBusinessView detail = customerMarketingActivitiesBusiness.detail(activitiesId);
		if (detail == null) {
			return DubboResult.error(DubboCodeMessageEnum.RETURN_NULL);
		}
		CustomerMarketingActivitiesDubboView copyResult = BeanUtil.copyProperties(detail, CustomerMarketingActivitiesDubboView.class);
		if (Objects.equals(CustomerMarketingActivityTypeEnum.PHONE.getCode(), copyResult.getActivityType())) {
			List<EmpCustomerCountVo> empCustomerCountVos = customerDistributeSdrRecordMapper.countCustomerByEmp(activitiesId);
			List<CustomerMarketingActivityDistributeSDRDubboView> sdrList = BeanCopyUtils.convertToVoList(empCustomerCountVos, CustomerMarketingActivityDistributeSDRDubboView.class);
			copyResult.setDistributeSDRList(sdrList);
		}
		return DubboResult.success(copyResult);
	}

	@Override
	public DubboResult<Boolean> updateDistributeState(String customerId) {
		return DubboResult.success(customerMarketingActivitiesRecordBusiness.updateDistributeState(customerId));
	}

	@Override
	public DubboResult<List<CustomerMarketingActivitiesCreatorSelectDubboView>> creatorSelect() {
		QueryWrapper<CustomerMarketingActivities> wrapper = new QueryWrapper<>();
		wrapper.select("DISTINCT create_by, create_name")
			.isNotNull("create_by")
			.isNotNull("create_name");
		List<CustomerMarketingActivities> list = customerMarketingActivitiesService.list(wrapper);
		if (CollectionUtils.isEmpty(list)) {
			log.warn("暂无营销活动创建人信息");
			return DubboResult.error(DubboCodeMessageEnum.RETURN_NULL);
		}
		List<CustomerMarketingActivitiesCreatorSelectDubboView> views = BeanUtil.copyToList(list, CustomerMarketingActivitiesCreatorSelectDubboView.class);
		return DubboResult.success(views);
	}
}