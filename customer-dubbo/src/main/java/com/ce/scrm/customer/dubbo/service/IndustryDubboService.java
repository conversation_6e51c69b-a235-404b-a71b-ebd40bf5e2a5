package com.ce.scrm.customer.dubbo.service;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.customer.dubbo.aop.sign.Sign;
import com.ce.scrm.customer.dubbo.api.IIndustryDubbo;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.IndustryDubboView;
import com.ce.scrm.customer.service.business.IIndustryBusiness;
import com.ce.scrm.customer.service.business.entity.view.IndustryBusinessView;
import com.google.common.collect.Lists;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 行业dubbo业务
 * <AUTHOR>
 * @date 2023/4/13 14:50
 * @version 1.0.0
 */
@DubboService(interfaceClass = IIndustryDubbo.class)
public class IndustryDubboService implements IIndustryDubbo {

    @Resource
    private IIndustryBusiness industryBusiness;

//    @Sign
    @Override
    public DubboResult<List<IndustryDubboView>> getAllData() {
        List<IndustryBusinessView> industryBusinessViewList = industryBusiness.getAllData();

        List<IndustryDubboView> industryDubboViewList = Optional.of(industryBusinessViewList).orElse(Lists.newArrayList()).stream().map(record->{
            IndustryDubboView industryDubboView = new IndustryDubboView();
            BeanUtil.copyProperties(record,industryDubboView);
            return industryDubboView;
        }).collect(Collectors.toList());

        return DubboResult.success(industryDubboViewList);
    }
}