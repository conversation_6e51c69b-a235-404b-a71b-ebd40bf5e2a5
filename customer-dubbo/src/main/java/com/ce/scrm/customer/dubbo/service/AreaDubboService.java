package com.ce.scrm.customer.dubbo.service;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.customer.dubbo.aop.sign.Sign;
import com.ce.scrm.customer.dubbo.api.IAreaDubbo;
import com.ce.scrm.customer.dubbo.entity.base.SignData;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.AreaDubboView;
import com.ce.scrm.customer.service.business.IAreaBusiness;
import com.ce.scrm.customer.service.business.entity.view.AreaBusinessView;
import com.google.common.collect.Lists;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 区域dubbo业务
 * <AUTHOR>
 * @date 2023/4/13 14:50
 * @version 1.0.0
 */
@DubboService(interfaceClass = IAreaDubbo.class)
public class AreaDubboService implements IAreaDubbo {

    @Resource
    private IAreaBusiness areaBusiness;

    @Sign
    @Override
    public DubboResult<List<AreaDubboView>> getAllData(SignData signData) {
        List<AreaBusinessView> areaBusinessViewList = areaBusiness.getAllData();

        List<AreaDubboView> industryDubboViewList = Optional.of(areaBusinessViewList).orElse(Lists.newArrayList()).stream().map(record->{
            AreaDubboView areaDubboView = new AreaDubboView();
            BeanUtil.copyProperties(record,areaDubboView);
            return areaDubboView;
        }).collect(Collectors.toList());

        return DubboResult.success(industryDubboViewList);
    }
}