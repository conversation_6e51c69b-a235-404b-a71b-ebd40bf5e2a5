package com.ce.scrm.customer.dubbo.service.sync.entity;

import lombok.Data;

import java.util.List;

@Deprecated
@Data
public class Enterprise {
    private String pid;
    private String entname;
    private String uncid;
    private String historyNames;
    private String legalPerson;
    private String establishDate;
    private String regNo;
    private String province;
    private String city;
    private String district;
    private String entType;
    private String regCap;
    private String regCapCur;
    private String opFrom;
    private String opEnd;
    private String regOrg;
    private String apprDate;
    private String revokeDate;
    private String entStatus;
    private String opLocation;
    private String opScope;
    private String industryL1Desc;
    private List<String> industryL2DescList;
    private String provinceCode;
    private String cityCode;
    private String districtCode;
    private String regAddress;
    private String regProvinceCode;
    private String geoAddress;
    private String regCityCode;
    private String regDistrictCode;
}
