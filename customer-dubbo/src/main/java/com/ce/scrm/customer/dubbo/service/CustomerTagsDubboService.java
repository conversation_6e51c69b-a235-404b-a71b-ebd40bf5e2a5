package com.ce.scrm.customer.dubbo.service;

import cn.ce.cesupport.framework.base.enums.CommonYesNoEnum;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.dao.entity.abm.CustomerTags;
import com.ce.scrm.customer.dao.entity.abm.CustomerTagsRela;
import com.ce.scrm.customer.dao.service.abm.CustomerTagsRelaService;
import com.ce.scrm.customer.dao.service.abm.CustomerTagsService;
import com.ce.scrm.customer.dubbo.api.ICustomerTagDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerTagRelaCreateDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerTagDubboView;
import com.ce.scrm.customer.support.redis.RedisOperator;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 跨境ABM 客户营销活动dubbo业务
 * <AUTHOR>
 * @date 2025/7/9 15:50
 * @version 1.0.0
 */
@Slf4j
@DubboService(interfaceClass = ICustomerTagDubbo.class)
public class CustomerTagsDubboService implements ICustomerTagDubbo {

	@Resource
    private CustomerTagsService customerTagsService;
	@Resource
    private CustomerTagsRelaService customerTagsRelaService;
	@Resource
    private RedisOperator redisOperator;


	/**
	 * 导入客户和标签后：增加客户标签关联关系、修改权重积分
	 * @param tagRelaCreateDubboDto 增加客户标签关联关系
	 * @return 是否修改成功
	 */
	@Override
	@Transactional
	public DubboResult<Boolean> addCustomerTagRela(@Valid CustomerTagRelaCreateDubboDto tagRelaCreateDubboDto) {
		List<CustomerTagRelaCreateDubboDto.TagInfo> tagInfoList = tagRelaCreateDubboDto.getTagInfoList();
		if (CollectionUtils.isEmpty(tagInfoList)) {
			return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "标签信息不能为空");
		}
		log.info("=== addCustomerTagRela -> tagRelaCreateDubboDto: {}", JSON.toJSONString(tagRelaCreateDubboDto));
		List<CustomerTagsRela> waitSaveCustomerTagsRelaList = new ArrayList<>();
		for (CustomerTagRelaCreateDubboDto.TagInfo tagInfo : tagInfoList) {
			CustomerTags tag = customerTagsService.lambdaQuery().eq(CustomerTags::getId, tagInfo.getTagId()).one();
			if (tag == null) {
				return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "标签不存在");
			}
			// String tagName = StringUtils.isBlank(tagInfo.getTagName()) || !Objects.equals(tagInfo.getTagName(), tag.getTagName()) ? tag.getTagName() : tagInfo.getTagName();
			if (Objects.equals(tag.getIsActive(), CommonYesNoEnum.no_0.getValue())) {
				tag.setIsActive(CommonYesNoEnum.yes_1.getValue());
			}
			tag.setWeight(tagInfo.getWeight());
			customerTagsService.updateById(tag);

			// 增加customer_tags_rela
			CustomerTagsRela customerTagsRela = new CustomerTagsRela();
			customerTagsRela.setTagId(tag.getId());
			customerTagsRela.setCustomerId(tagRelaCreateDubboDto.getCustomerId());
			if (StringUtils.isBlank(tagRelaCreateDubboDto.getCreateBy())) {
				customerTagsRela.setCreateBy(tagRelaCreateDubboDto.getCreateBy());
			}
			waitSaveCustomerTagsRelaList.add(customerTagsRela);
		}
		customerTagsRelaService.saveOrUpdateBatch(waitSaveCustomerTagsRelaList);
		return null;
	}

	/**
	 * 根据客户id列表获取客户标签列表，按积分从大到小排序
	 * @param customerIdList 客户id列表
	 * @return 客户标签信息Map，key为客户ID，value为该客户的标签信息
	 */
	@Override
	public DubboResult<Map<String, List<CustomerTagDubboView>>> customerTagList(List<String> customerIdList) {
		try {
			// 参数校验
			if (CollectionUtils.isEmpty(customerIdList)) {
				log.warn("=== customerTagList -> 客户ID列表为空");
				return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户ID列表不能为空");
			}

			log.info("=== customerTagList -> 开始查询客户标签列表，客户ID数量: {}", customerIdList.size());

			// 结果Map
			Map<String, CustomerTagDubboView> resultMap = new HashMap<>();

			// 遍历每个客户ID，获取其标签信息
			for (String customerId : customerIdList) {
				if (StringUtils.isBlank(customerId)) {
					continue;
				}

				// 获取客户的最高权重标签
				CustomerTagDubboView customerTag = getCustomerTopWeightTag(customerId);
				if (customerTag != null) {
					resultMap.put(customerId, customerTag);
				}
			}

			log.info("=== customerTagList -> 查询完成，返回客户标签数量: {}", resultMap.size());
			return DubboResult.success(resultMap);

		} catch (Exception e) {
			log.error("=== customerTagList -> 查询客户标签列表异常，客户ID列表: {}", JSON.toJSONString(customerIdList), e);
			return DubboResult.error(DubboCodeMessageEnum.EXCEPTION, "查询客户标签列表失败");
		}
	}

	/**
	 * 获取客户的最高权重标签
	 * @param customerId 客户ID
	 * @return 客户标签视图对象，如果没有标签则返回null
	 */
	private CustomerTagDubboView getCustomerTopWeightTag(String customerId) {
		try {
			// 先尝试从Redis ZSet中获取排序后的标签
			String redisKey = "customer:tags:" + customerId;
			List<String> sortedTagIds = redisOperator.zGetSetByIndex(redisKey, 0, 0, RedisOperator.DESC);

			CustomerTags topTag = null;

			if (!CollectionUtils.isEmpty(sortedTagIds)) {
				// 从Redis中获取到了排序后的标签ID，取第一个（权重最高的）
				Long topTagId = Long.valueOf(sortedTagIds.get(0));
				topTag = customerTagsService.getById(topTagId);
			} else {
				// Redis中没有数据，从数据库查询并缓存到Redis
				topTag = queryAndCacheCustomerTags(customerId);
			}

			if (topTag == null) {
				return null;
			}
			return convertToCustomerTagDubboView(topTag);

		} catch (Exception e) {
			log.error("=== getCustomerTopWeightTag -> 获取客户最高权重标签异常，客户ID: {}", customerId, e);
			return null;
		}
	}

	/**
	 * 从数据库查询客户标签并缓存到Redis ZSet中
	 * @param customerId 客户ID
	 * @return 权重最高的标签，如果没有标签则返回null
	 */
	private CustomerTags queryAndCacheCustomerTags(String customerId) {
		try {
			// 查询客户的所有标签关联关系
			List<CustomerTagsRela> tagRelaList = customerTagsRelaService.lambdaQuery()
					.eq(CustomerTagsRela::getCustomerId, customerId)
					.list();

			if (CollectionUtils.isEmpty(tagRelaList)) {
				log.warn("=== queryAndCacheCustomerTags -> 客户没有关联的标签，客户ID: {}", customerId);
				return null;
			}

			// 获取标签ID列表
			List<Long> tagIdList = tagRelaList.stream().map(CustomerTagsRela::getTagId).collect(Collectors.toList());

			// 查询标签详情
			List<CustomerTags> tagList = customerTagsService.lambdaQuery()
					.in(CustomerTags::getId, tagIdList)
					.eq(CustomerTags::getIsActive, CommonYesNoEnum.yes_1.getValue())
					.list();

			if (CollectionUtils.isEmpty(tagList)) {
				log.debug("=== queryAndCacheCustomerTags -> 客户没有有效的标签，客户ID: {}", customerId);
				return null;
			}

			// 缓存到Redis ZSet中，按权重排序
			cacheCustomerTagsToRedis(customerId, tagList);

			// 返回权重最高的标签
			return tagList.stream()
					.max(Comparator.comparing(CustomerTags::getWeight))
					.orElse(null);

		} catch (Exception e) {
			log.error("=== queryAndCacheCustomerTags -> 查询并缓存客户标签异常，客户ID: {}", customerId, e);
			return null;
		}
	}

	/**
	 * 将客户标签缓存到Redis ZSet中
	 * @param customerId 客户ID
	 * @param tagList 标签列表
	 */
	private void cacheCustomerTagsToRedis(String customerId, List<CustomerTags> tagList) {
		try {
			String redisKey = "customer:tags:" + customerId;

			// 构建ZSet数据，使用标签权重作为score
			List<DefaultTypedTuple<String>> tuples = tagList.stream()
					.map(tag -> new DefaultTypedTuple<>(tag.getId().toString(), tag.getWeight().doubleValue()))
					.collect(Collectors.toList());

			// 批量添加到ZSet
			DefaultTypedTuple<String>[] tupleArray = tuples.toArray(new DefaultTypedTuple[0]);
			redisOperator.zSet(redisKey, 30, TimeUnit.DAYS, tupleArray);

			log.debug("=== cacheCustomerTagsToRedis -> 成功缓存客户标签到Redis，客户ID: {}, 标签数量: {}", customerId, tagList.size());

		} catch (Exception e) {
			log.error("=== cacheCustomerTagsToRedis -> 缓存客户标签到Redis异常，客户ID: {}", customerId, e);
		}
	}

	/**
	 * 将CustomerTags实体转换为CustomerTagDubboView视图对象
	 * @param customerTags 客户标签实体
	 * @return 客户标签视图对象
	 */
	private CustomerTagDubboView convertToCustomerTagDubboView(CustomerTags customerTags) {
		if (customerTags == null) {
			return null;
		}
		try {
			CustomerTagDubboView view = BeanUtil.copyProperties(customerTags, CustomerTagDubboView.class);
			log.info("=== convertToCustomerTagDubboView -> 成功转换标签对象，标签ID: {}, 标签名称: {}", customerTags.getId(), customerTags.getTagName());
			return view;

		} catch (Exception e) {
			log.error("=== convertToCustomerTagDubboView -> 转换标签对象异常，标签ID: {}", customerTags.getId(), e);
			return null;
		}
	}
}