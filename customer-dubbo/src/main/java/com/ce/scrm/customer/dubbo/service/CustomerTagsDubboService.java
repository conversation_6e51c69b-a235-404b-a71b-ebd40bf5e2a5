package com.ce.scrm.customer.dubbo.service;

import cn.ce.cesupport.framework.base.enums.CommonYesNoEnum;
import cn.ce.cesupport.framework.core.utils.BeanCopyUtils;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.dao.entity.abm.CustomerTags;
import com.ce.scrm.customer.dao.entity.abm.CustomerTagsCategory;
import com.ce.scrm.customer.dao.entity.abm.CustomerTagsRela;
import com.ce.scrm.customer.dao.service.abm.CustomerTagsCategoryService;
import com.ce.scrm.customer.dao.service.abm.CustomerTagsRelaService;
import com.ce.scrm.customer.dao.service.abm.CustomerTagsService;
import com.ce.scrm.customer.dubbo.api.ICustomerTagDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerTagRelaCreateDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerTagDubboView;
import com.ce.scrm.customer.support.redis.RedisOperator;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 跨境ABM 客户营销活动dubbo业务
 * <AUTHOR>
 * @date 2025/7/9 15:50
 * @version 1.0.0
 */
@Slf4j
@DubboService(interfaceClass = ICustomerTagDubbo.class)
public class CustomerTagsDubboService implements ICustomerTagDubbo {

	@Resource
    private CustomerTagsService customerTagsService;
	@Resource
    private CustomerTagsRelaService customerTagsRelaService;
	@Resource
    private CustomerTagsCategoryService customerTagsCategoryService;
	@Resource
    private RedisOperator redisOperator;


	/**
	 * 导入客户和标签后：增加客户标签关联关系、修改权重积分
	 * @param tagRelaCreateDubboDto 增加客户标签关联关系
	 * @return 是否修改成功
	 */
	@Override
	@Transactional
	public DubboResult<Boolean> addCustomerTagRela(@Valid CustomerTagRelaCreateDubboDto tagRelaCreateDubboDto) {
		List<CustomerTagRelaCreateDubboDto.TagInfo> tagInfoList = tagRelaCreateDubboDto.getTagInfoList();
		if (CollectionUtils.isEmpty(tagInfoList)) {
			return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "标签信息不能为空");
		}
		log.info("=== addCustomerTagRela -> tagRelaCreateDubboDto: {}", JSON.toJSONString(tagRelaCreateDubboDto));
		List<CustomerTagsRela> waitSaveCustomerTagsRelaList = new ArrayList<>();
		for (CustomerTagRelaCreateDubboDto.TagInfo tagInfo : tagInfoList) {
			CustomerTags tag = customerTagsService.lambdaQuery().eq(CustomerTags::getId, tagInfo.getTagId()).one();
			if (tag == null) {
				return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "标签不存在");
			}
			// String tagName = StringUtils.isBlank(tagInfo.getTagName()) || !Objects.equals(tagInfo.getTagName(), tag.getTagName()) ? tag.getTagName() : tagInfo.getTagName();
			if (Objects.equals(tag.getIsActive(), CommonYesNoEnum.no_0.getValue())) {
				tag.setIsActive(CommonYesNoEnum.yes_1.getValue());
			}
			tag.setWeight(tagInfo.getWeight());
			customerTagsService.updateById(tag);

			// 增加customer_tags_rela
			CustomerTagsRela customerTagsRela = new CustomerTagsRela();
			customerTagsRela.setTagCode(tag.getTagCode());
			customerTagsRela.setCustomerId(tagRelaCreateDubboDto.getCustomerId());
			if (StringUtils.isBlank(tagRelaCreateDubboDto.getCreateBy())) {
				customerTagsRela.setCreateBy(tagRelaCreateDubboDto.getCreateBy());
			}
			waitSaveCustomerTagsRelaList.add(customerTagsRela);
		}
		customerTagsRelaService.saveOrUpdateBatch(waitSaveCustomerTagsRelaList);
		return null;
	}

	/**
	 * 根据客户id列表获取客户标签列表，按积分从大到小排序
	 * @param customerIdList 客户id列表
	 * @return 客户标签信息Map，key为客户ID，value为该客户的所有标签列表（按权重降序排列）
	 */
	@Override
	public DubboResult<Map<String, List<CustomerTagDubboView>>> customerTagList(List<String> customerIdList) {
		try {
			// 参数校验
			if (CollectionUtils.isEmpty(customerIdList)) {
				log.warn("=== customerTagList -> 客户ID列表为空");
				return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户ID列表不能为空");
			}

			log.info("=== customerTagList -> 开始查询客户标签列表，客户ID数量: {}", customerIdList.size());

			// 结果Map
			Map<String, List<CustomerTagDubboView>> resultMap = new HashMap<>();

			// 遍历每个客户ID，获取其标签信息
			for (String customerId : customerIdList) {
				if (StringUtils.isBlank(customerId)) {
					continue;
				}

				// 获取客户的所有标签列表
				List<CustomerTagDubboView> customerTags = getCustomerAllTags(customerId);
				if (!CollectionUtils.isEmpty(customerTags)) {
					resultMap.put(customerId, customerTags);
				}
			}

			log.info("=== customerTagList -> 查询完成，返回客户数量: {}，总标签数量: {}",
					resultMap.size(), resultMap.values().stream().mapToInt(List::size).sum());
			return DubboResult.success(resultMap);

		} catch (Exception e) {
			log.error("=== customerTagList -> 查询客户标签列表异常，客户ID列表: {}", JSON.toJSONString(customerIdList), e);
			return DubboResult.error(DubboCodeMessageEnum.EXCEPTION, "查询客户标签列表失败");
		}
	}

	/**
	 * 获取客户的所有标签列表，按权重从大到小排序
	 * @param customerId 客户ID
	 * @return 客户标签视图对象列表，如果没有标签则返回空列表
	 */
	private List<CustomerTagDubboView> getCustomerAllTags(String customerId) {
		try {
			// 先尝试从Redis ZSet中获取排序后的标签
			String redisKey = "customer:tags:" + customerId;
			List<String> sortedTagIds = redisOperator.zGetSetByIndex(redisKey, 0, -1, RedisOperator.DESC);

			List<CustomerTags> allTags = new ArrayList<>();

			if (!CollectionUtils.isEmpty(sortedTagIds)) {
				// 从Redis中获取到了排序后的标签ID列表
				List<Long> tagIds = sortedTagIds.stream()
						.map(Long::valueOf)
						.collect(Collectors.toList());

				allTags = customerTagsService.lambdaQuery()
						.in(CustomerTags::getId, tagIds)
						.eq(CustomerTags::getIsActive, CommonYesNoEnum.yes_1.getValue())
						.list();

				// 按Redis中的顺序排序（权重从大到小）
				Map<Long, Integer> orderMap = new HashMap<>();
				for (int i = 0; i < tagIds.size(); i++) {
					orderMap.put(tagIds.get(i), i);
				}
				allTags.sort(Comparator.comparing(tag -> orderMap.getOrDefault(tag.getId(), Integer.MAX_VALUE)));

			} else {
				allTags = queryAndCacheCustomerTags(customerId);
			}

			if (CollectionUtils.isEmpty(allTags)) {
				return new ArrayList<>();
			}

			return BeanCopyUtils.convertToVoList(allTags, CustomerTagDubboView.class);

		} catch (Exception e) {
			log.error("=== getCustomerAllTags -> 获取客户所有标签异常，客户ID: {}", customerId, e);
			return new ArrayList<>();
		}
	}

	/**
	 * 从数据库查询客户标签并缓存到Redis ZSet中
	 * @param customerId 客户ID
	 * @return 所有标签列表，按权重从大到小排序，如果没有标签则返回空列表
	 */
	private List<CustomerTags> queryAndCacheCustomerTags(String customerId) {
		try {
			// 查询客户的所有标签关联关系
			List<CustomerTagsRela> tagRelaList = customerTagsRelaService.lambdaQuery()
					.eq(CustomerTagsRela::getCustomerId, customerId)
					.list();

			if (CollectionUtils.isEmpty(tagRelaList)) {
				log.warn("=== queryAndCacheCustomerTags -> 客户没有关联的标签，客户ID: {}", customerId);
				return null;
			}

			// 获取标签ID列表
			List<String> tagIdCodeList = tagRelaList.stream().map(CustomerTagsRela::getTagCode).collect(Collectors.toList());

			// 查询标签详情
			List<CustomerTags> tagList = customerTagsService.lambdaQuery()
					.in(CustomerTags::getTagCode, tagIdCodeList)
					.eq(CustomerTags::getIsActive, CommonYesNoEnum.yes_1.getValue())
					.list();

			if (CollectionUtils.isEmpty(tagList)) {
				log.debug("=== queryAndCacheCustomerTags -> 客户没有有效的标签，客户ID: {}", customerId);
				return new ArrayList<>();
			}

			// 按权重从大到小排序
			tagList.sort(Comparator.comparing(CustomerTags::getWeight).reversed());

			// 缓存到Redis ZSet中，按权重排序
			cacheCustomerTagsToRedis(customerId, tagList);

			// 返回所有标签列表
			return tagList;

		} catch (Exception e) {
			log.error("=== queryAndCacheCustomerTags -> 查询并缓存客户标签异常，客户ID: {}", customerId, e);
			return new ArrayList<>();
		}
	}

	/**
	 * 将客户标签缓存到Redis ZSet中
	 * @param customerId 客户ID
	 * @param tagList 标签列表
	 */
	private void cacheCustomerTagsToRedis(String customerId, List<CustomerTags> tagList) {
		try {
			String redisKey = "customer:tags:" + customerId;

			// 构建ZSet数据，使用标签权重作为score
			List<DefaultTypedTuple<String>> tuples = tagList.stream()
					.map(tag -> new DefaultTypedTuple<>(tag.getId().toString(), tag.getWeight().doubleValue()))
					.collect(Collectors.toList());

			// 批量添加到ZSet
			DefaultTypedTuple<String>[] tupleArray = tuples.toArray(new DefaultTypedTuple[0]);
			redisOperator.zSet(redisKey, 30, TimeUnit.DAYS, tupleArray);

			log.debug("=== cacheCustomerTagsToRedis -> 成功缓存客户标签到Redis，客户ID: {}, 标签数量: {}", customerId, tagList.size());

		} catch (Exception e) {
			log.error("=== cacheCustomerTagsToRedis -> 缓存客户标签到Redis异常，客户ID: {}", customerId, e);
		}
	}


	/**
	 * 根据单个分类ID获取该分类及其所有子分类的标签列表
	 * @param tagCategoryId 标签分类ID
	 * @return 标签列表，按权重从大到小排序
	 */
	@Override
	public DubboResult<List<CustomerTagDubboView>> getTagsByCategoryId(Long tagCategoryId) {
		try {
			if (tagCategoryId == null) {
				return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "分类ID不能为空");
			}

			log.info("=== getTagsByCategoryId -> 开始查询分类标签，分类ID: {}", tagCategoryId);

			// 获取该分类及其所有子分类的ID列表
			Set<Long> allCategoryIds = getAllCategoryIds(tagCategoryId);

			if (CollectionUtils.isEmpty(allCategoryIds)) {
				log.info("=== getTagsByCategoryId -> 未找到有效的分类，分类ID: {}", tagCategoryId);
				return DubboResult.success(new ArrayList<>());
			}

			// 查询这些分类下的所有启用标签
			List<CustomerTags> tagList = customerTagsService.lambdaQuery()
					.in(CustomerTags::getTagCategoryId, allCategoryIds)
					.eq(CustomerTags::getIsActive, CommonYesNoEnum.yes_1.getValue())
					.list();

			if (CollectionUtils.isEmpty(tagList)) {
				log.info("=== getTagsByCategoryId -> 分类下没有启用的标签，分类ID: {}", tagCategoryId);
				return DubboResult.success(new ArrayList<>());
			}

			// 按权重从大到小排序
			tagList.sort(Comparator.comparing(CustomerTags::getWeight).reversed());

			List<CustomerTagDubboView> resultList = BeanCopyUtils.convertToVoList(tagList, CustomerTagDubboView.class);
			log.info("=== getTagsByCategoryId -> 查询完成，分类ID: {}，返回标签数量: {}", tagCategoryId, resultList.size());
			return DubboResult.success(resultList);

		} catch (Exception e) {
			log.error("=== getTagsByCategoryId -> 查询分类标签异常，分类ID: {}", tagCategoryId, e);
			return DubboResult.error(DubboCodeMessageEnum.EXCEPTION, "查询分类标签失败");
		}
	}

	/**
	 * 根据分类ID列表获取这些分类及其所有子分类的标签列表，按分类ID分组返回
	 * @param tagCategoryIds 标签分类ID列表
	 * @return Map结构，key为分类ID（字符串），value为该分类ID及其子分类下的标签列表（按权重从大到小排序）
	 */
	@Override
	public DubboResult<Map<String, List<CustomerTagDubboView>>> getTagsByCategoryIds(List<Long> tagCategoryIds) {
		try {
			if (CollectionUtils.isEmpty(tagCategoryIds)) {
				return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "分类ID列表不能为空");
			}
			log.info("=== getTagsByCategoryIds -> 开始查询分类标签，分类ID列表: {}", tagCategoryIds);

			Map<String, List<CustomerTagDubboView>> resultMap = new HashMap<>();

			// 遍历每个分类ID，分别获取其标签
			for (Long categoryId : tagCategoryIds) {
				if (categoryId == null) {
					continue;
				}
				Set<Long> allCategoryIds = getAllCategoryIds(categoryId);

				if (CollectionUtils.isEmpty(allCategoryIds)) {
					log.debug("=== getTagsByCategoryIds -> 未找到有效的分类，分类ID: {}", categoryId);
					resultMap.put(categoryId.toString(), new ArrayList<>());
					continue;
				}

				// 查询这些分类下的所有启用标签
				List<CustomerTags> tagList = customerTagsService.lambdaQuery()
						.in(CustomerTags::getTagCategoryId, allCategoryIds)
						.eq(CustomerTags::getIsActive, CommonYesNoEnum.yes_1.getValue())
						.list();

				if (CollectionUtils.isEmpty(tagList)) {
					log.debug("=== getTagsByCategoryIds -> 分类下没有启用的标签，分类ID: {}", categoryId);
					resultMap.put(categoryId.toString(), new ArrayList<>());
					continue;
				}

				// 按权重从大到小排序
				tagList.sort(Comparator.comparing(CustomerTags::getWeight).reversed());

				List<CustomerTagDubboView> categoryTagList = BeanCopyUtils.convertToVoList(tagList, CustomerTagDubboView.class);
				resultMap.put(categoryId.toString(), categoryTagList);

				log.debug("=== getTagsByCategoryIds -> 分类ID: {}，返回标签数量: {}", categoryId, categoryTagList.size());
			}

			log.info("=== getTagsByCategoryIds -> 查询完成，分类ID列表: {}，返回分类数量: {}，总标签数量: {}",
					tagCategoryIds, resultMap.size(), resultMap.values().stream().mapToInt(List::size).sum());
			return DubboResult.success(resultMap);

		} catch (Exception e) {
			log.error("=== getTagsByCategoryIds -> 查询分类标签异常，分类ID列表: {}", tagCategoryIds, e);
			return DubboResult.error(DubboCodeMessageEnum.EXCEPTION, "查询分类标签失败");
		}
	}

	/**
	 * 获取指定分类列表及其所有子分类的ID列表
	 * @param categoryIds 分类ID列表
	 * @return 分类ID列表，包含传入的分类ID及其所有子分类ID
	 */
	private Set<Long> getAllCategoryIdsByList(List<Long> categoryIds) {
		try {
			Set<Long> allCategoryIds = new HashSet<>();

			for (Long categoryId : categoryIds) {
				if (categoryId == null) {
					continue;
				}

				// 获取单个分类及其子分类的ID列表
				Set<Long> singleCategoryIds = getAllCategoryIds(categoryId);
				allCategoryIds.addAll(singleCategoryIds);
			}

			log.debug("=== getAllCategoryIdsByList -> 分类ID列表: {}，包含子分类总数: {}", categoryIds, allCategoryIds.size());
			return allCategoryIds;

		} catch (Exception e) {
			log.error("=== getAllCategoryIdsByList -> 获取分类ID列表异常，分类ID列表: {}", categoryIds, e);
			return new HashSet<>();
		}
	}

	/**
	 * 获取指定分类及其所有子分类的ID列表
	 * @param categoryId 分类ID
	 * @return 分类ID列表，包含传入的分类ID及其所有子分类ID
	 */
	private Set<Long> getAllCategoryIds(Long categoryId) {
		try {
			Set<Long> allCategoryIds = new HashSet<>();

			// 首先检查传入的分类是否存在且启用
			CustomerTagsCategory category = customerTagsCategoryService.lambdaQuery()
					.eq(CustomerTagsCategory::getId, categoryId)
					.eq(CustomerTagsCategory::getIsActive, CommonYesNoEnum.yes_1.getValue())
					.one();

			if (category == null) {
				log.debug("=== getAllCategoryIds -> 分类不存在或未启用，分类ID: {}", categoryId);
				return allCategoryIds;
			}

			// 添加当前分类ID
			allCategoryIds.add(categoryId);

			findAllSubCategories(categoryId, allCategoryIds);

			log.debug("=== getAllCategoryIds -> 分类ID: {}，包含子分类总数: {}", categoryId, allCategoryIds.size());
			return allCategoryIds;

		} catch (Exception e) {
			log.error("=== getAllCategoryIds -> 获取分类ID列表异常，分类ID: {}", categoryId, e);
			return new HashSet<>();
		}
	}

	/**
	 * 递归查找所有子分类
	 * @param parentId 父分类ID
	 * @param allCategoryIds 用于收集所有分类ID的列表
	 */
	private void findAllSubCategories(Long parentId, Set<Long> allCategoryIds) {
		try {
			// 查询当前分类的所有启用子分类
			List<CustomerTagsCategory> subCategories = customerTagsCategoryService.lambdaQuery()
					.eq(CustomerTagsCategory::getParentId, parentId)
					.eq(CustomerTagsCategory::getIsActive, CommonYesNoEnum.yes_1.getValue())
					.list();
			if (!CollectionUtils.isEmpty(subCategories)) {
				for (CustomerTagsCategory subCategory : subCategories) {
					Long subCategoryId = subCategory.getId();

					// 避免重复添加和循环引用
					if (!allCategoryIds.contains(subCategoryId)) {
						allCategoryIds.add(subCategoryId);

						// 递归查找子分类的子分类
						findAllSubCategories(subCategoryId, allCategoryIds);
					}
				}
			}
		} catch (Exception e) {
			log.error("=== findAllSubCategories -> 递归查找子分类异常，父分类ID: {}", parentId, e);
		}
	}
}