package com.ce.scrm.customer.dubbo.util;

import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

public class CronUtil {

    public static String toCron(Date date) {
	    Objects.requireNonNull(date, "CronUtil#toCron date is null");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return String.format("%d %d %d %d %d ? %d",
                calendar.get(Calendar.SECOND),
                calendar.get(Calendar.MINUTE),
                calendar.get(Calendar.HOUR_OF_DAY),
                calendar.get(Calendar.DAY_OF_MONTH),
				// 月份是从0开始的
                calendar.get(Calendar.MONTH) + 1,
                calendar.get(Calendar.YEAR));
    }
}