package com.ce.scrm.customer.dubbo.service;

import cn.ce.cesupport.framework.core.utils.BeanCopyUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.dao.entity.CustomerBindWx;
import com.ce.scrm.customer.dao.entity.CustomerLeads;
import com.ce.scrm.customer.dao.entity.CustomerShareTemporary;
import com.ce.scrm.customer.dao.service.CustomerBindWxService;
import com.ce.scrm.customer.dao.service.CustomerShareTemporaryService;
import com.ce.scrm.customer.dubbo.aop.sign.Sign;
import com.ce.scrm.customer.dubbo.api.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.*;
import com.ce.scrm.customer.dubbo.entity.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.*;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerLeadsDubboView;
import com.ce.scrm.customer.dubbo.service.sync.entity.WechatBindData;
import com.ce.scrm.customer.dubbo.util.PageUtil;
import com.ce.scrm.customer.service.business.*;
import com.ce.scrm.customer.service.business.entity.dto.*;
import com.ce.scrm.customer.service.business.entity.view.*;
import com.ce.scrm.customer.service.config.UniqueIdService;
import com.ce.scrm.customer.service.enums.BindTypeEnum;
import com.ce.scrm.customer.service.enums.CustomerTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户dubbo业务
 * <AUTHOR>
 * @date 2023/4/13 14:50
 * @version 1.0.0
 */
@Slf4j
@DubboService(interfaceClass = ICustomerDubbo.class)
public class CustomerDubboService implements ICustomerDubbo {

    @Resource
    private ICustomerBusiness customerBusiness;

    @Resource
    private IContactPersonBusiness contactPersonBusiness;

    @Resource
    private IContactInfoBusiness contactInfoBusiness;

    @Resource
    private ICustomerContactBusiness customerContactBusiness;

    @Resource
    private UniqueIdService uniqueIdService;

    @Resource
    private SendMqService sendMqService;

    @Resource
    private CustomerBindWxService customerBindWxService;

    @Resource
    private CustomerShareTemporaryService customerShareTemporaryService;


    @Resource
    private ICustomerLeadsBusiness customerLeadsBusiness;

    @Resource
    private ContactPersonDubboService contactPersonDubboService;

    /**
     * 获取客户数据
     *
     * @param customerDetailDubboDto 查询参数
     * <AUTHOR>
     * @date 2023/4/13 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.DemoDubboView>
     **/
    @Sign
    @Override
    public DubboResult<CustomerDubboView> detail(CustomerDetailDubboDto customerDetailDubboDto) {
        if (customerDetailDubboDto == null || StrUtil.isBlank(customerDetailDubboDto.getCustomerId())) {
            log.warn("查询客户详情，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM);
        }
        CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
        customerBusinessDto.setCustomerId(customerDetailDubboDto.getCustomerId());
        CustomerBusinessView customerBusinessView = customerBusiness.detail(customerBusinessDto);
        log.info("返回查询的客户数据，customerBusinessView：{}", JSON.toJSONString(customerBusinessView));
        CustomerDubboView customerDubboView = BeanUtil.copyProperties(customerBusinessView, CustomerDubboView.class);
        sendMqService.sendCheckValidCustomerMq("CustomerDubboService.detail", Collections.singletonList(customerDetailDubboDto.getCustomerId()));
        return DubboResult.success(customerDubboView);
    }

    @Sign
    @Override
    public DubboResult<DubboPageInfo<CustomerDubboView>> pageList(CustomerPageDubboDto customerPageDubboDto) {
        CustomerPageBusinessDto customerPageBusinessDto = BeanUtil.copyProperties(customerPageDubboDto, CustomerPageBusinessDto.class);
        Page<CustomerBusinessView> customerBusinessViewPage = customerBusiness.pageList(customerPageBusinessDto);
        DubboPageInfo<CustomerDubboView> dubboPageInfoDubboPageInfo = PageUtil.pageConversion(customerBusinessViewPage, CustomerDubboView.class);
        List<CustomerDubboView> customerDubboViewList = dubboPageInfoDubboPageInfo.getList();
        if (CollectionUtil.isNotEmpty(customerDubboViewList)) {
            sendMqService.sendCheckValidCustomerMq("CustomerDubboService.pageList", customerDubboViewList.stream().map(CustomerDubboView::getCustomerId).collect(Collectors.toList()));
        }
        return DubboResult.success(dubboPageInfoDubboPageInfo);
    }

    /**
     * 根据条件查询客户信息列表
     * @param customerConditionDubboDto
     * @return
     */
    @Sign
    @Override
    public DubboResult<List<CustomerDubboView>> findByCondition(CustomerConditionDubboDto customerConditionDubboDto) {

        if (customerConditionDubboDto == null) {
            log.warn("根据条件查询客户详情，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM);
        }

        // 根据手机号查
        if (StringUtils.isNotEmpty(customerConditionDubboDto.getContactWay()) && customerConditionDubboDto.getContactType() != null) {
            return DubboResult.success(getByContactWay(customerConditionDubboDto.getContactWay(), customerConditionDubboDto.getContactType()));
        }

        //根据客户名称查询客户数据
        if (StrUtil.isNotBlank(customerConditionDubboDto.getCustomerName())) {
            return DubboResult.success(getByCustomerName(customerConditionDubboDto.getCustomerName()));
        }

        log.warn("根据条件查询客户详情，必要参数不能为空");
        return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM);
    }

    /**
     * 根据客户名称查询客户信息
     * @param customerName 客户名称
     * <AUTHOR>
     * @date 2024/2/29 09:15
     * @return java.util.List<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
     **/
    private List<CustomerDubboView> getByCustomerName(String customerName) {
        Optional<CustomerBusinessView> customerBusinessViewOptional = customerBusiness.detail(customerName);
        return customerBusinessViewOptional.map(customerBusinessView -> Collections.singletonList(BeanUtil.copyProperties(customerBusinessView, CustomerDubboView.class))).orElseGet(Lists::newArrayList);
    }

    /**
     * 根据手机号查客户、联系人和联系方式
     * @param contactWay
     * <AUTHOR>
     * @date 2023/8/10 14:12
     * @version 1.0.0
     * @return java.util.List<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
     **/
    private List<CustomerDubboView> getByContactWay(String contactWay, Integer contactType) {
        ContactInfoBusinessDto contactInfoBusinessDto = new ContactInfoBusinessDto();
        contactInfoBusinessDto.setContactWay(contactWay);
        contactInfoBusinessDto.setContactType(contactType);
        // 根据手机号先查询联系方式
        List<ContactInfoBusinessView> contactInfoBusinessViews = contactInfoBusiness.detailList(contactInfoBusinessDto);
        if (CollectionUtil.isEmpty(contactInfoBusinessViews)) {
            return null;
        }
        // 按照联系人id进行分组
        Map<String, List<ContactInfoBusinessView>> contactInfoBusinessViewCollect = contactInfoBusinessViews.stream().collect(Collectors.groupingBy(e -> e.getContactPersonId()));
        if (CollectionUtil.isEmpty(contactInfoBusinessViewCollect)) {
            return null;
        }
        List<CustomerDubboView> customerDubboViews = new ArrayList<>();
        // 遍历分组的map,key是联系人id
        contactInfoBusinessViewCollect.forEach((k, v) -> {
            // 查询联系人信息
            ContactPersonBusinessView contactPersonBusinessView = contactPersonBusiness.detail(k);
            List<ContactPersonDubboView> contactPersonDubboViews = new ArrayList<ContactPersonDubboView>();

            // 如果联系人不为空，为联系人设置联系方式
            if (contactPersonBusinessView != null) {
                ContactPersonDubboView contactPersonDubboView = BeanUtil.copyProperties(contactPersonBusinessView, ContactPersonDubboView.class);
                List<ContactInfoDubboView> contactInfoList = v.stream().map(e -> {
                    ContactInfoDubboView contactInfoDubboView = BeanUtil.copyProperties(e, ContactInfoDubboView.class);
                    return contactInfoDubboView;
                }).collect(Collectors.toList());
                contactPersonDubboView.setContactInfoList(contactInfoList);
                contactPersonDubboViews.add(contactPersonDubboView);
            }
            CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
            customerContactBusinessDto.setContactPersonId(k);
            // 查询客户联系人关系表
            CustomerContactBusinessView customerContactBusinessView = customerContactBusiness.customerContactByContactId(customerContactBusinessDto);
            if (customerContactBusinessView != null) {
                CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
                customerBusinessDto.setCustomerId(customerContactBusinessView.getCustomerId());
                // 通过关系表中的客户id查询客户信息
                CustomerBusinessView customerBusinessView = customerBusiness.detail(customerBusinessDto);
                if (customerBusinessView != null) {
                    CustomerDubboView customerDubboView = BeanUtil.copyProperties(customerBusinessView, CustomerDubboView.class);
                    customerDubboView.setContactPersonDubboViewList(contactPersonDubboViews);
                    customerDubboViews.add(customerDubboView);
                }
            }
        });
        return customerDubboViews;
    }

    /**
     * 添加客户，给其他系统提供
     *
     * @param customerAddDubboDto 客户数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerAddDubboView>
     **/
    @Sign
    @Override
    public DubboResult<CustomerAddDubboView> add(CustomerAddDubboDto customerAddDubboDto) {

        // 检查添加客户参数
        DubboResult<?> checkParam = checkAddCustomerParam(customerAddDubboDto);
        if (!checkParam.checkSuccess()) {
            return DubboResult.error(checkParam);
        }

        // 查询此客户是否已存在，不存在则添加
        CustomerPageDubboDto customerDubboRequest = new CustomerPageDubboDto();
        customerDubboRequest.setCustomerName(customerAddDubboDto.getCustomerName());
        customerDubboRequest.setPageSize(1);
        customerDubboRequest.setPageNum(1);
        DubboResult<DubboPageInfo<CustomerDubboView>> dubboPageInfoDubboResult = this.pageList(customerDubboRequest);

        if (!dubboPageInfoDubboResult.checkSuccess()) {
            log.error("通过客户名称查询客户信息失败:{}", JSON.toJSONString(dubboPageInfoDubboResult));
            return DubboResult.error(dubboPageInfoDubboResult);
        }

        DubboPageInfo<CustomerDubboView> customerDubboView = dubboPageInfoDubboResult.getData();
        if (customerDubboView == null) {
            return DubboResult.error(DubboCodeMessageEnum.RETURN_NULL, "通过客户名称查询客户信息失败，返回接口为空");
        }

        List<CustomerDubboView> list = customerDubboView.getList();
        if (!CollectionUtils.isEmpty(list)) {
            CustomerAddDubboView customerAddDubboView = new CustomerAddDubboView();
            customerAddDubboView.setCustomerId(list.get(0).getCustomerId());
            return DubboResult.success(customerAddDubboView);
        }

        //拼装客户数据
        CustomerAddBusinessDto customerAddBusinessDto = packgeCustomerAddBusinessDto(customerAddDubboDto);
        //保存客户
        customerBusiness.add(customerAddBusinessDto);
        // 将客户信息返回
        CustomerAddDubboView customerAddDubboView = new CustomerAddDubboView();
        customerAddDubboView.setCustomerId(customerAddBusinessDto.getCustomerId());
        sendMqService.sendCheckValidCustomerMq("CustomerDubboService.add", Collections.singletonList(customerAddBusinessDto.getCustomerId()));
        //异步打标(目前只有skb)
        sendMqService.sendSkbSyncFlagMq(Collections.singletonList(customerAddBusinessDto.getCustomerId()));
        return DubboResult.success(customerAddDubboView);
    }

    /**
     * 保存企业客户
     *
     * @param enterpriseCustomerSaveDubboDto 企业客户保存参数
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerAddDubboView>
     * <AUTHOR>
     * @date 2024/2/29 10:00
     **/
    @Sign
    @Override
    public DubboResult<CustomerAddDubboView> saveEnterpriseCustomer(EnterpriseCustomerSaveDubboDto enterpriseCustomerSaveDubboDto) {
        if (enterpriseCustomerSaveDubboDto == null) {
            log.warn("保存企业客户信息，参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM);
        }
        if (StrUtil.isBlank(enterpriseCustomerSaveDubboDto.getCustomerName())) {
            log.warn("保存企业客户信息，客户名称不能为空，参数为:{}", JSON.toJSONString(enterpriseCustomerSaveDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户名称不能为空");
        }
        if (enterpriseCustomerSaveDubboDto.getPresentStage() == null || enterpriseCustomerSaveDubboDto.getPresentStage() < 1) {
            log.warn("保存企业客户信息，客户阶段不能为空，参数为:{}", JSON.toJSONString(enterpriseCustomerSaveDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户阶段不能为空");
        }
        if (enterpriseCustomerSaveDubboDto.getCreateWay() == null || enterpriseCustomerSaveDubboDto.getCreateWay() < 1) {
            log.warn("保存企业客户信息，客户创建方式不能为空，参数为:{}", JSON.toJSONString(enterpriseCustomerSaveDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户创建方式不能为空");
        }
        if (StrUtil.isBlank(enterpriseCustomerSaveDubboDto.getOperator())) {
            log.warn("保存企业客户信息，操作人不能为空，参数为:{}", JSON.toJSONString(enterpriseCustomerSaveDubboDto));
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "操作人不能为空");
        }
        String customerId = customerBusiness.saveEnterpriseCustomer(BeanUtil.copyProperties(enterpriseCustomerSaveDubboDto, EnterpriseCustomerSaveBusinessDto.class));
        CustomerAddDubboView customerAddDubboView = new CustomerAddDubboView();
        customerAddDubboView.setCustomerId(customerId);
        return DubboResult.success(customerAddDubboView);
    }

    @NotNull
    private CustomerAddBusinessDto packgeCustomerAddBusinessDto(CustomerAddDubboDto customerAddDubboDto) {
        CustomerAddBusinessDto customerAddBusinessDto = BeanUtil.copyProperties(customerAddDubboDto, CustomerAddBusinessDto.class);
        if (StringUtils.isEmpty(customerAddBusinessDto.getCustomerId())) {
            customerAddBusinessDto.setCustomerId(uniqueIdService.getId());
        }
        if (customerAddDubboDto.getOpenStartTime() != null) {
            customerAddBusinessDto.setOpenStartTime(Instant.ofEpochMilli(customerAddDubboDto.getOpenStartTime().getTime()).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        }

        if (customerAddDubboDto.getOpenEndTime() != null) {
            customerAddBusinessDto.setOpenEndTime(Instant.ofEpochMilli(customerAddDubboDto.getOpenEndTime().getTime()).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        }

        if (customerAddDubboDto.getEstablishDate() != null) {
            customerAddBusinessDto.setEstablishDate(Instant.ofEpochMilli(customerAddDubboDto.getEstablishDate().getTime()).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        }

        if (customerAddDubboDto.getApproveDate() != null) {
            customerAddBusinessDto.setApproveDate(Instant.ofEpochMilli(customerAddDubboDto.getApproveDate().getTime()).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        }
        return customerAddBusinessDto;
    }

    /**
     * 更新客户，给其他系统提供
     *
     * @param customerUpdateDubboDto 客户数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Sign
    @Override
    public DubboResult<Boolean> update(CustomerUpdateDubboDto customerUpdateDubboDto) {
        DubboResult<?> checkParam = checkUpdateCustomerParam(customerUpdateDubboDto);
        if (!checkParam.checkSuccess()) {
            return DubboResult.error(checkParam);
        }
        CustomerUpdateBusinessDto customerUpdateBusinessDto = BeanUtil.copyProperties(customerUpdateDubboDto, CustomerUpdateBusinessDto.class);
        if (customerUpdateDubboDto.getOpenStartTime() != null) {
            customerUpdateBusinessDto.setOpenStartTime(Instant.ofEpochMilli(customerUpdateDubboDto.getOpenStartTime().getTime()).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        }

        if (customerUpdateDubboDto.getOpenEndTime() != null) {
            customerUpdateBusinessDto.setOpenEndTime(Instant.ofEpochMilli(customerUpdateDubboDto.getOpenEndTime().getTime()).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        }

        if (customerUpdateDubboDto.getEstablishDate() != null) {
            customerUpdateBusinessDto.setEstablishDate(Instant.ofEpochMilli(customerUpdateDubboDto.getEstablishDate().getTime()).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        }

        if (customerUpdateDubboDto.getApproveDate() != null) {
            customerUpdateBusinessDto.setApproveDate(Instant.ofEpochMilli(customerUpdateDubboDto.getApproveDate().getTime()).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        }
        sendMqService.sendCheckValidCustomerMq("CustomerDubboService.update", Collections.singletonList(customerUpdateDubboDto.getCustomerId()));
        return DubboResult.success(customerBusiness.update(customerUpdateBusinessDto));
    }

    @Override
    public DubboResult<DubboPageInfo<CustomerDubboView>> elasticsearchMatchByCondition(CustomerPageDubboDto customerPageDubboDto) {
        CustomerPageBusinessDto customerPageBusinessDto = BeanUtil.copyProperties(customerPageDubboDto, CustomerPageBusinessDto.class);
        Page<CustomerBusinessView> customerBusinessViewPage = customerBusiness.elasticsearchMatchByCondition(customerPageBusinessDto);
        List<CustomerBusinessView> records = new ArrayList<>();
        List<CustomerBusinessView> collect = Optional.ofNullable(customerBusinessViewPage.getRecords()).orElse(Lists.newArrayList()).stream().filter(t -> Objects.equals(t.getCustomerName(), customerPageDubboDto.getCustomerName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)){
            if (StringUtils.isNotBlank(customerPageDubboDto.getCustomerName())){
                Optional<CustomerBusinessView> customerBusinessViewOptional = customerBusiness.detail(customerPageDubboDto.getCustomerName());
                if (customerBusinessViewOptional.isPresent()){
                    CustomerBusinessView customerBusinessView = customerBusinessViewOptional.get();
                    records.add(customerBusinessView);
                }
            }
        }
        records.addAll(customerBusinessViewPage.getRecords());
        customerBusinessViewPage.setRecords(records);
        DubboPageInfo<CustomerDubboView> dubboPageInfoDubboPageInfo = PageUtil.pageConversion(customerBusinessViewPage, CustomerDubboView.class);
        return DubboResult.success(dubboPageInfoDubboPageInfo);
    }

    /**
     * <AUTHOR>
     * @date 2025/7/14 11:24:31
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo<com.ce.scrm.customer.dubbo.entity.view.CustomerESDubboView>>
     * @desc 从客户ES中查询客户数据
     */
    @Sign
    @Override
    public DubboResult<DubboPageInfo<CustomerESDubboView>> customerESMatchByCondition(CustomerESPageDubboDto customerESPageDubboDto) {
        CustomerESPageBusinessDto customerESPageBusinessDto = BeanUtil.copyProperties(customerESPageDubboDto, CustomerESPageBusinessDto.class);
        Page<CustomerESBusinessView> customerESBusinessViewPage = customerBusiness.customerESMatchByCondition(customerESPageBusinessDto);
        List<CustomerESBusinessView> records = new ArrayList<>(customerESBusinessViewPage.getRecords());
        customerESBusinessViewPage.setRecords(records);
        DubboPageInfo<CustomerESDubboView> dubboPageInfoDubboPageInfo = PageUtil.pageConversion(customerESBusinessViewPage, CustomerESDubboView.class);

        //在客户端主动调用，保持接口的单一性，leads列表也会放在客户端调用。
        /*
        List<CustomerESDubboView> listDubboView = dubboPageInfoDubboPageInfo.getList();
        List<String> listCustomerIds = listDubboView.stream().map(CustomerESDubboView::getCustomerId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, List<CustomerLeads>> mapLeads = customerLeadsBusiness.getCustIdLeadsMap(listCustomerIds);
        // 将 leads 设置到每个 record 中
        listDubboView.forEach(view -> {
            String customerId = view.getCustomerId();
            if (customerId != null && mapLeads.containsKey(customerId)) {
                List<CustomerLeadsDubboView> customerLeadsDubboViews = BeanUtil.copyToList(mapLeads.get(customerId), CustomerLeadsDubboView.class);
                view.setCustomerLeadsList(customerLeadsDubboViews);
            } else {
                // 如果没有 leads，可以设置为空列表
                view.setCustomerLeadsList(Collections.emptyList());
            }
            CustomerDetailDubboDto dto = new CustomerDetailDubboDto();
            dto.setCustomerId(customerId);
            dto.setSourceKey(customerESPageDubboDto.getSourceKey());
            dto.setSourceSecret(customerESPageDubboDto.getSourceSecret());
            DubboResult<List<ContactPersonDataDubboView>> result = contactPersonDubboService.getCustomerData(dto);
            if(result.checkSuccess()){
                view.setContactPersonList(result.getData());
            } else {
                view.setContactPersonList(Collections.emptyList());
            }
        });
         */
        return DubboResult.success(dubboPageInfoDubboPageInfo);
    }

    @Override
    public DubboResult<CustomerDubboView> elasticsearchExactByCondition(CustomerPageDubboDto customerPageDubboDto) {
        CustomerPageBusinessDto customerPageBusinessDto = BeanUtil.copyProperties(customerPageDubboDto, CustomerPageBusinessDto.class);
        CustomerBusinessView customerBusinessView = customerBusiness.elasticsearchExactByCondition(customerPageBusinessDto);
        if (customerBusinessView != null) {
            CustomerDubboView customerDubboView = new CustomerDubboView();
            BeanUtils.copyProperties(customerBusinessView, customerDubboView);
            return DubboResult.success(customerDubboView);
        }
        return DubboResult.success(null);
    }

    /**
     * 判断一个客户的联系人，是否是法人
     *
     *  @param customerId 客户ID
     *  @param contactName 联系人姓名
     *  <AUTHOR>
     *  @date 2024/9/10
     *  @return boolean 是否法人
     */
    @Override
    public DubboResult<Boolean> isLegalPerson(String customerId, String contactName){
        if (StrUtil.isBlank(customerId) || StrUtil.isBlank(contactName)) {
            log.warn("isLegalPerson -> customerId or contactName is null, customerId: {}, contactName: {}", customerId, contactName);
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "参数不能为空");
        }
        return DubboResult.success(customerBusiness.isLegalPerson(customerId, contactName));
    }

    /**
     * 添加客户参数校验
     * @param customerAddDubboDto   添加客户参数
     * <AUTHOR>
     * @date 2023/5/18 12:45
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<?>
     **/
    private DubboResult<?> checkAddCustomerParam(CustomerAddDubboDto customerAddDubboDto) {
        if (customerAddDubboDto == null) {
            log.warn("添加客户，参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "参数不能为空");
        }

        if (StringUtils.isBlank(customerAddDubboDto.getCustomerName()) || StringUtils.isBlank(customerAddDubboDto.getCustomerName().trim()) ) {
            log.warn("添加客户，客户名称不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户名称不能为空");
        }

        if (customerAddDubboDto.getCustomerType() == null) {
            log.warn("添加客户，客户类型不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户类型不能为空");
        }

        if (customerAddDubboDto.getCustomerType() == CustomerTypeEnum.personal.getType()) {
            if (customerAddDubboDto.getCertificateType() == null) {
                log.warn("添加客户，个人客户证件类型不能为空");
                return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "个人客户证件类型不能为空");
            }

            if (StringUtils.isEmpty(customerAddDubboDto.getCertificateCode())) {
                log.warn("添加客户，个人客户证件号码不能为空");
                return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "个人客户证件号码不能为空");
            }
        }

        DubboResult<?> checkCustomerNameFormatResult = checkCustomerNameFormat(customerAddDubboDto.getCustomerName(), customerAddDubboDto.getCustomerType());
        if (!checkCustomerNameFormatResult.checkSuccess()) {
            return DubboResult.error(checkCustomerNameFormatResult);
        }

        if (customerAddDubboDto.getCreateWay() == null) {
            log.warn("添加客户，客户创建方式不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户创建方式不能为空");
        }

        if (StringUtils.isEmpty(customerAddDubboDto.getCreator())) {
            log.warn("添加客户，客户创建者不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户创建者不能为空");
        }

        return DubboResult.success();
    }

    /**
     * 更新客户参数校验
     * @param customerUpdateDubboDto   更新参数
     * <AUTHOR>
     * @date 2023/5/18 12:45
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<?>
     **/
    private DubboResult<?> checkUpdateCustomerParam(CustomerUpdateDubboDto customerUpdateDubboDto) {
        if (customerUpdateDubboDto == null) {
            log.warn("更新客户，参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "参数不能为空");
        }

        if (StringUtils.isEmpty(customerUpdateDubboDto.getCustomerId())) {
            log.warn("更新客户，客户Id不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "客户Id不能为空");
        }

        if (StringUtils.isEmpty(customerUpdateDubboDto.getOperator())) {
            log.warn("更新客户，更新者不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "更新者不能为空");
        }

        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        customerDetailDubboDto.setCustomerId(customerUpdateDubboDto.getCustomerId());
        DubboResult<CustomerDubboView> detailDubboResult = detail(customerDetailDubboDto);
        if (!detailDubboResult.checkSuccess()) {
            log.warn("更新客户，查客户失败");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "查客户失败");
        }
        CustomerDubboView customerDubboView = detailDubboResult.getData();

        if (customerDubboView == null) {
            log.warn("更新客户，客户不存在");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "查客户失败");
        }

        if (StringUtils.isNotEmpty(customerUpdateDubboDto.getCustomerName())) {
            Integer customerType = null;
            if (customerUpdateDubboDto.getCustomerType() != null) {
                customerType = customerUpdateDubboDto.getCustomerType();
            } else {
                customerType = customerDubboView.getCustomerType();
            }
            DubboResult<?> checkCustomerNameFormatResult = checkCustomerNameFormat(customerUpdateDubboDto.getCustomerName(), customerType);
            if (!checkCustomerNameFormatResult.checkSuccess()) {
                return DubboResult.error(checkCustomerNameFormatResult);
            }
        }

        return DubboResult.success();
    }

    /**
     * 校验客户名称格式
     * @param customerName  需要校验的客户名
     * @param customerType   客户类型
     * <AUTHOR>
     * @date 2023/5/25 10:03
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<?>
     **/
    private DubboResult<?> checkCustomerNameFormat(String customerName, Integer customerType) {
        if (customerType == CustomerTypeEnum.company.getType()) {
            // 企业客户
            String custNameRegex = "([\\u4E00-\\u9FA5]|\\（|\\）|[0-9]|〇|《|》|—|、){5,100}";
            // 校验客户名称
            if (!customerName.matches(custNameRegex)) {
                return DubboResult.error(DubboCodeMessageEnum.CUSTOMER_NAME_FORMAT_ERROR);
            }
        }
        return DubboResult.success();
    }

    /**
     * 获取首页拜访率
     * @param loginInfo
     * @return
     */
    @Sign
    @Override
    public DubboResult<HomePageCallRateDubboView> getCallRate(LoginInfoDubboDto loginInfo) {

        LoginInfoBusinessDto loginInfoBusinessDto = BeanUtil.copyProperties(loginInfo, LoginInfoBusinessDto.class);

        Integer thirtyCallRate1 = customerBusiness.getCallRate(loginInfoBusinessDto,0,30,Boolean.TRUE);
        Integer thirtyCallRate2 = customerBusiness.getCallRate(loginInfoBusinessDto,0,30,Boolean.FALSE);

        Integer thirtyCallRate3 = customerBusiness.getCallRate(loginInfoBusinessDto,30,60,Boolean.TRUE);
        Integer thirtyCallRate4 = customerBusiness.getCallRate(loginInfoBusinessDto,30,60,Boolean.FALSE);

        Integer thirtyCallRate5 = customerBusiness.getCallRate(loginInfoBusinessDto,60,90,Boolean.TRUE);
        Integer thirtyCallRate6 = customerBusiness.getCallRate(loginInfoBusinessDto,60,90,Boolean.FALSE);

        HomePageCallRateDubboView result = new HomePageCallRateDubboView();

        try {
            // 使用BigDecimal进行精确的除法运算
            BigDecimal bd = new BigDecimal(thirtyCallRate1)
                    .divide(new BigDecimal(thirtyCallRate2), 4, RoundingMode.HALF_UP); // 4代表小数点后四位，实际保留两位小数
            // 转换为百分比并保留两位小数
            BigDecimal percentValue = bd.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            result.setThirtyCallRate(formatZero(percentValue)+"%");
        }catch (Exception e){
            //log.warn("拜访率计算错误parm1:{},parm2:{}",JSON.toJSONString(thirtyCallRate1),JSON.toJSONString(thirtyCallRate2));
        }

        try {
            // 使用BigDecimal进行精确的除法运算
            BigDecimal bd = new BigDecimal(thirtyCallRate3)
                    .divide(new BigDecimal(thirtyCallRate4), 4, RoundingMode.HALF_UP); // 4代表小数点后四位，实际保留两位小数
            // 转换为百分比并保留两位小数
            BigDecimal percentValue = bd.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            result.setSixtyCallRate(formatZero(percentValue)+"%");
        }catch (Exception e){
            //log.warn("拜访率计算错误parm1:{},parm2:{}",JSON.toJSONString(thirtyCallRate3),JSON.toJSONString(thirtyCallRate4));
        }

        try {
            // 使用BigDecimal进行精确的除法运算
            BigDecimal bd = new BigDecimal(thirtyCallRate5)
                    .divide(new BigDecimal(thirtyCallRate6), 4, RoundingMode.HALF_UP); // 4代表小数点后四位，实际保留两位小数
            // 转换为百分比并保留两位小数
            BigDecimal percentValue = bd.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            result.setNinetyCallRate(formatZero(percentValue)+"%");
        }catch (Exception e){
            log.warn("拜访率计算错误parm1:{},parm2:{}",JSON.toJSONString(thirtyCallRate5),JSON.toJSONString(thirtyCallRate6));
        }

        return DubboResult.success(result);
    }

    /**
     * 处理末尾0
     * @param data
     * @return
     */
    private String formatZero(BigDecimal data) {
        if(data == null) {
            return null;
        }
        char[] charArray = data.toString().toCharArray();
        int i = charArray.length-1;
        for (; i > 0; i--) {
            char c = charArray[i];
            if(c == '0') {
                continue;
            } else {
                break;
            }
        }
        return data.toString().substring(0,i+1).replaceAll("\\.$", "");
    }

    /**
     * 获取打卡内页列表
     *
     * @param callDetailsDubboDto
     * @return
     */
    @Sign
    @Override
    public DubboResult<DubboPageInfo<CustomerDubboView>> getCallDetails(CallDetailsDubboDto callDetailsDubboDto) {

        CallDetailsBusinessDto callDetailsBusinessDto = BeanUtil.copyProperties(callDetailsDubboDto, CallDetailsBusinessDto.class);

        Page<CustomerBusinessView> result = customerBusiness.getCallDetails(callDetailsBusinessDto);

        DubboPageInfo<CustomerDubboView> callDetailsDubboView = PageUtil.pageConversion(result, CustomerDubboView.class);
        return DubboResult.success(callDetailsDubboView);
    }

    /**
     * 监听保护关系表binlog 然后同步customer
     * @param updateDubboDto
     * @return
     */
    @Sign
    @Override
    public DubboResult<Boolean> monitorProtectInfoConsumer(CustomerUpdateDubboDto updateDubboDto) {
        log.info("updateDubboDto: {}" ,JSON.toJSONString(updateDubboDto));
        DubboResult<?> checkParam = checkUpdateCustomerParam(updateDubboDto);
        if (!checkParam.checkSuccess()) {
            return DubboResult.error(checkParam);
        }
        CustomerUpdateBusinessDto customerUpdateBusinessDto = BeanUtil.copyProperties(updateDubboDto, CustomerUpdateBusinessDto.class);
        return DubboResult.success(customerBusiness.monitorProtectInfoConsumer(customerUpdateBusinessDto));
    }

    /**
     * 更新客户表数据 只更新CustType字段
     * @param updateDubboDto
     * @return
     */
    @Sign
    @Override
    public DubboResult<Boolean> updateCustomerCustType(CustomerUpdateDubboDto updateDubboDto) {
        log.info("updateDubboDto: {}" ,JSON.toJSONString(updateDubboDto));
        DubboResult<?> checkParam = checkUpdateCustomerParam(updateDubboDto);
        if (!checkParam.checkSuccess()) {
            return DubboResult.error(checkParam);
        }
        CustomerUpdateBusinessDto customerUpdateBusinessDto = BeanUtil.copyProperties(updateDubboDto, CustomerUpdateBusinessDto.class);
        return DubboResult.success(customerBusiness.updateCustomerCustType(customerUpdateBusinessDto));
    }

    @Override
    public DubboResult<DubboPageInfo<CustomerDubboView>> qualifiedNewCustomerList(CustomerPageDubboDto customerPageDubboDto) {
        if (customerPageDubboDto == null
                || CollectionUtils.isEmpty(customerPageDubboDto.getCustomerIdList())
                || Objects.isNull(customerPageDubboDto.getQualifiedNewCustBeginTime())
                || Objects.isNull(customerPageDubboDto.getQualifiedNewCustEndTime())
        ) {
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM);
        }
        CustomerPageBusinessDto customerPageBusinessDto = BeanUtil.copyProperties(customerPageDubboDto, CustomerPageBusinessDto.class);
        Page<CustomerBusinessView> customerBusinessViewPage = customerBusiness.qualifiedNewCustomerList(customerPageBusinessDto);
        DubboPageInfo<CustomerDubboView> dubboPageInfoDubboPageInfo = PageUtil.pageConversion(customerBusinessViewPage, CustomerDubboView.class);
        return DubboResult.success(dubboPageInfoDubboPageInfo);
    }

    @Override
    public DubboResult<Boolean> bindUnionId(CustomerBindUnionIdDubboDto customerBindUnionIdDubboDto) {
        String pid = customerBindUnionIdDubboDto.getPid();
        String unionId = customerBindUnionIdDubboDto.getUnionId();
        if ((StrUtil.isBlank(pid) && StrUtil.isBlank(customerBindUnionIdDubboDto.getCustomerId())) || StrUtil.isBlank(unionId) || StrUtil.isBlank(customerBindUnionIdDubboDto.getOperator())) {
            log.error("客户绑定unionId，必要参数不能为空");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "必要参数不能为空");
        }
        //绑定类型不传默认为微信类型
        if (customerBindUnionIdDubboDto.getBindType() == null) {
            customerBindUnionIdDubboDto.setBindType(BindTypeEnum.WECHAT.getType());
        }
        //绑定类型校验
        if (!BindTypeEnum.check(customerBindUnionIdDubboDto.getBindType())) {
            log.warn("客户绑定unionId，绑定类型传入异常");
            return DubboResult.error(DubboCodeMessageEnum.INVALID_PARAM, "绑定类型传入异常");
        }

        // 根据pid查询 customerId
        if (StringUtils.isBlank(customerBindUnionIdDubboDto.getCustomerId())) {
            CustomerPageBusinessDto selectCustomerData = new CustomerPageBusinessDto();
            selectCustomerData.setSourceDataId(pid);
            Page<CustomerBusinessView> customerBusinessViewPage = customerBusiness.pageList(selectCustomerData);
            if(CollectionUtil.isNotEmpty(customerBusinessViewPage.getRecords())) {
                customerBindUnionIdDubboDto.setCustomerId(customerBusinessViewPage.getRecords().get(0).getCustomerId());
            }
        }

        CustomerBindWx customerBindWxSave = new CustomerBindWx();
        Date currentDate = new Date();
        customerBindWxSave.setCreateTime(currentDate);
        customerBindWxSave.setUpdateTime(currentDate);
        customerBindWxSave.setPid(customerBindUnionIdDubboDto.getPid());
        customerBindWxSave.setCustomerId(customerBindUnionIdDubboDto.getCustomerId());
        customerBindWxSave.setBindType(customerBindUnionIdDubboDto.getBindType());
        customerBindWxSave.setUnionid(customerBindUnionIdDubboDto.getUnionId());
        customerBindWxSave.setWxName(customerBindUnionIdDubboDto.getWechatNickName());
        customerBindWxSave.setCreateId(customerBindUnionIdDubboDto.getOperator());
        // 保存表
//        boolean save = customerBindWxService.save(customerBindWxSave);
        boolean save = true;
        if(save){
            // 发送mq
            WechatBindData wechatBindData = new WechatBindData();
            wechatBindData.setType(3);
            wechatBindData.setPid(customerBindWxSave.getPid());
            wechatBindData.setCustomerId(customerBindWxSave.getCustomerId());
            wechatBindData.setBindType(customerBindWxSave.getBindType());
            wechatBindData.setUnionId(customerBindWxSave.getUnionid());
            wechatBindData.setWechatNickName(customerBindWxSave.getWxName());
            wechatBindData.setOperator(customerBindWxSave.getCreateId());
            wechatBindData.setOperatorTime(customerBindWxSave.getCreateTime());
            sendMqService.sendWechatBindMq(JSON.toJSONString(wechatBindData));
        }
        return DubboResult.success(Boolean.TRUE);
    }

    @Override
    public DubboResult<Boolean> bindUnionIdDataHandle(CustomerBindUnionIdDubboDto customerBindUnionIdDubboDto) {
//        List<CustomerBindWx> list = customerBindWxService.lambdaQuery().isNull(CustomerBindWx::getUpdateTime).list();
//        log.error("bindUnionIdDataHandle开始处理,size={}",list.size());
//        int count = 0;
////        for (CustomerBindWx customerBindWxSave : list) {
////            count ++ ;
////            if (count % 100 == 0) {
////                log.error("bindUnionIdDataHandle处理,size={},count={}",list.size(),count);
////            }
////            // 发送mq
////            WechatBindData wechatBindData = new WechatBindData();
////            wechatBindData.setType(3);
////            wechatBindData.setPid(customerBindWxSave.getPid());
////            wechatBindData.setCustomerId(customerBindWxSave.getCustomerId());
////            wechatBindData.setBindType(customerBindWxSave.getBindType());
////            wechatBindData.setUnionId(customerBindWxSave.getUnionId());
////            wechatBindData.setWechatNickName(customerBindWxSave.getWechatNickName());
////            wechatBindData.setOperator(customerBindWxSave.getOperator());
////            wechatBindData.setOperatorTime(customerBindWxSave.getCreateTime());
////            sendMqService.sendWechatBindMq(JSON.toJSONString(wechatBindData));
////        }
////        log.error("bindUnionIdDataHandle结束处理,size={}",list.size());
        return DubboResult.success(Boolean.TRUE);
    }

    @Override
    public DubboResult<List<CustomerShareTemporaryDubboView>> getCustomerShareTemporaryByCustomerId(CustomerShareQueryDubboDto queryDubboDto) {
        if (StringUtils.isBlank(queryDubboDto.getCustomerId())) {
            return DubboResult.success(Collections.emptyList());
        }
        List<CustomerShareTemporary> list = customerShareTemporaryService.lambdaQuery()
                .eq(CustomerShareTemporary::getCustomerId, queryDubboDto.getCustomerId())
                .gt(queryDubboDto.getStartTime() != null, CustomerShareTemporary::getCreateTime, queryDubboDto.getStartTime())
                .list();
        List<CustomerShareTemporaryDubboView> result = BeanUtil.copyToList(list, CustomerShareTemporaryDubboView.class);
        return DubboResult.success(result);
    }

    @Override
    public DubboResult<List<CompanyDistributionSummaryDubboView>> zqCompanyDistributionSummary(CompanyDistributionSummaryDubboDto dubboDto) {
        CompanyDistributionSummaryBusinessDto businessDto = BeanUtil.copyProperties(dubboDto, CompanyDistributionSummaryBusinessDto.class);
        List<CompanyDistributionSummaryBusinessView> businessViews = customerBusiness.zqCompanyDistributionSummary(businessDto);
        List<CompanyDistributionSummaryDubboView> views = BeanUtil.copyToList(businessViews, CompanyDistributionSummaryDubboView.class);
        return DubboResult.success(views);
    }

}