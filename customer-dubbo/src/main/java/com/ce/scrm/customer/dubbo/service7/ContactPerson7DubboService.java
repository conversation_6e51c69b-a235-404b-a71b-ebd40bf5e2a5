package com.ce.scrm.customer.dubbo.service7;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ce.scrm.customer.dubbo.api7.IContactPersonDubbo;
import com.ce.scrm.customer.dubbo.entity7.base.SignData;
import com.ce.scrm.customer.dubbo.entity7.dto.*;
import com.ce.scrm.customer.dubbo.entity7.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity7.view.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 联系人dubbo业务
 * <AUTHOR>
 * @date 2023/4/13 14:50
 * @version 1.0.0
 */
@Slf4j
@DubboService(interfaceClass = IContactPersonDubbo.class)
public class ContactPerson7DubboService implements IContactPersonDubbo {

    @Resource
    private com.ce.scrm.customer.dubbo.api.IContactPersonDubbo contactPersonDubbo;

    @Resource
    private com.ce.scrm.customer.dubbo.api.IAreaDubbo areaDubbo;

    /**
     * 获取客户联系人
     *
     * @param customerDetailDubboDto 客户查询dto
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboView>>
     **/
    @Override
    public DubboResult<CustomerDubboView> customerContactDetail(CustomerDetailDubboDto customerDetailDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView> customerDubboViewDubboResult = contactPersonDubbo.customerContactDetail(BeanUtil.copyProperties(customerDetailDubboDto, com.ce.scrm.customer.dubbo.entity.dto.CustomerDetailDubboDto.class));
        if (customerDubboViewDubboResult.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(customerDubboViewDubboResult), new TypeReference<DubboResult<CustomerDubboView>>() {
            });
        }
        DubboResult<CustomerDubboView> dubboResult = new DubboResult<>();
        dubboResult.setCode(customerDubboViewDubboResult.getCode());
        dubboResult.setMsg(customerDubboViewDubboResult.getMsg());
        return dubboResult;
    }

    /**
     * 获取联系人数据，给其他系统提供
     *
     * @param contactPersonDubboDto 查询参数
     * <AUTHOR>
     * @date 2023/5/15 10:14
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView>>
     **/
    @Override
    public DubboResult<List<ContactPersonDataDubboView>> getData(ContactPersonDubboDto contactPersonDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<List<com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView>> contactPersonDubboData = contactPersonDubbo.getData(BeanUtil.copyProperties(contactPersonDubboDto, com.ce.scrm.customer.dubbo.entity.dto.ContactPersonDubboDto.class));
        if (contactPersonDubboData.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(contactPersonDubboData), new TypeReference<DubboResult<List<ContactPersonDataDubboView>>>() {
            });
        }
        DubboResult<List<ContactPersonDataDubboView>> dubboResult = new DubboResult<>();
        dubboResult.setCode(contactPersonDubboData.getCode());
        dubboResult.setMsg(contactPersonDubboData.getMsg());
        return dubboResult;
    }

    /**
     * 获取客户联系人数据，给其他系统提供
     *
     * @param customerDetailDubboDto 查询参数
     * <AUTHOR>
     * @date 2023/5/15 10:14
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView>>
     **/
    @Override
    public DubboResult<List<ContactPersonDataDubboView>> getCustomerData(CustomerDetailDubboDto customerDetailDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<List<com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView>> contactPersonDubboCustomerData = contactPersonDubbo.getCustomerData(BeanUtil.copyProperties(customerDetailDubboDto, com.ce.scrm.customer.dubbo.entity.dto.CustomerDetailDubboDto.class));
        if (contactPersonDubboCustomerData.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(contactPersonDubboCustomerData), new TypeReference<DubboResult<List<ContactPersonDataDubboView>>>() {
            });
        }
        DubboResult<List<ContactPersonDataDubboView>> dubboResult = new DubboResult<>();
        dubboResult.setCode(contactPersonDubboCustomerData.getCode());
        dubboResult.setMsg(contactPersonDubboCustomerData.getMsg());
        return dubboResult;
    }

    /**
     * 添加联系人，给其他系统提供
     *
     * @param contactPersonAddDubboDto 联系人数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.ContactPersonAddDubboView>
     **/
    @Override
    public DubboResult<ContactPersonAddDubboView> add(ContactPersonAddDubboDto contactPersonAddDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.ContactPersonAddDubboView> contactPersonAddDubboViewDubboResult = contactPersonDubbo.add(BeanUtil.copyProperties(contactPersonAddDubboDto, com.ce.scrm.customer.dubbo.entity.dto.ContactPersonAddDubboDto.class));
        if (contactPersonAddDubboViewDubboResult.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(contactPersonAddDubboViewDubboResult), new TypeReference<DubboResult<ContactPersonAddDubboView>>() {
            });
        }
        DubboResult<ContactPersonAddDubboView> dubboResult = new DubboResult<>();
        dubboResult.setCode(contactPersonAddDubboViewDubboResult.getCode());
        dubboResult.setMsg(contactPersonAddDubboViewDubboResult.getMsg());
        return dubboResult;
    }

    /**
     * 更新联系人，给其他系统提供
     *
     * @param contactPersonUpdateDubboDto 联系人数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Override
    public DubboResult<Boolean> update(ContactPersonUpdateDubboDto contactPersonUpdateDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<Boolean> updateDubboResult = contactPersonDubbo.update(BeanUtil.copyProperties(contactPersonUpdateDubboDto, com.ce.scrm.customer.dubbo.entity.dto.ContactPersonUpdateDubboDto.class));
        if (updateDubboResult.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(updateDubboResult), new TypeReference<DubboResult<Boolean>>() {
            });
        }
        DubboResult<Boolean> dubboResult = new DubboResult<>();
        dubboResult.setCode(updateDubboResult.getCode());
        dubboResult.setMsg(updateDubboResult.getMsg());
        return dubboResult;
    }

    /**
     * 更新联系人的联系方式手机号是否已验证，给其他系统提供
     * @param contactPersonUpdateDubboDto 联系人数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Override
    public DubboResult<Boolean> updatePhoneVerifiedFlag(ContactPersonUpdateDubboDto contactPersonUpdateDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<Boolean> updateDubboResult = contactPersonDubbo.updatePhoneVerifiedFlag(BeanUtil.copyProperties(contactPersonUpdateDubboDto, com.ce.scrm.customer.dubbo.entity.dto.ContactPersonUpdateDubboDto.class));
        if (updateDubboResult.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(updateDubboResult), new TypeReference<DubboResult<Boolean>>() {
            });
        }
        DubboResult<Boolean> dubboResult = new DubboResult<>();
        dubboResult.setCode(updateDubboResult.getCode());
        dubboResult.setMsg(updateDubboResult.getMsg());
        return dubboResult;
    }

    /**
     * 根据所有区域数据
     *
     * @param signData 签名数据
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.AreaDubboView>
     **/
    @Override
    public DubboResult<List<AreaDubboView>> getAllAreaData(SignData signData) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<List<com.ce.scrm.customer.dubbo.entity.view.AreaDubboView>> areaDubboAllDataDubboResult = areaDubbo.getAllData(BeanUtil.copyProperties(signData, com.ce.scrm.customer.dubbo.entity.base.SignData.class));
        if (areaDubboAllDataDubboResult.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(areaDubboAllDataDubboResult), new TypeReference<DubboResult<List<AreaDubboView>>>() {
            });
        }
        DubboResult<List<AreaDubboView>> dubboResult = new DubboResult<>();
        dubboResult.setCode(areaDubboAllDataDubboResult.getCode());
        dubboResult.setMsg(areaDubboAllDataDubboResult.getMsg());
        return dubboResult;
    }

    /**
     * 添加默认标记
     *
     * @param orderDefaultFlagDubboDto 打标参数
     * <AUTHOR>
     * @date 2023/5/23 09:43
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Override
    public DubboResult<Boolean> addDefaultFlag(OrderDefaultFlagDubboDto orderDefaultFlagDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<Boolean> addDefaultFlagDubboResult = contactPersonDubbo.addDefaultFlag(BeanUtil.copyProperties(orderDefaultFlagDubboDto, com.ce.scrm.customer.dubbo.entity.dto.OrderDefaultFlagDubboDto.class));
        if (addDefaultFlagDubboResult.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(addDefaultFlagDubboResult), new TypeReference<DubboResult<Boolean>>() {
            });
        }
        DubboResult<Boolean> dubboResult = new DubboResult<>();
        dubboResult.setCode(addDefaultFlagDubboResult.getCode());
        dubboResult.setMsg(addDefaultFlagDubboResult.getMsg());
        return dubboResult;
    }

    /**
     * 联系人绑定微信unionId
     *
     * @param contactPersonBindUnionIdDubboDto 绑定参数
     * <AUTHOR>
     * @date 2023/6/16 09:51
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Override
    public DubboResult<Boolean> bindUnionId(ContactPersonBindUnionIdDubboDto contactPersonBindUnionIdDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<Boolean> bindUnionIdDubboResult = contactPersonDubbo.bindUnionId(BeanUtil.copyProperties(contactPersonBindUnionIdDubboDto, com.ce.scrm.customer.dubbo.entity.dto.ContactPersonBindUnionIdDubboDto.class));
        if (bindUnionIdDubboResult.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(bindUnionIdDubboResult), new TypeReference<DubboResult<Boolean>>() {
            });
        }
        DubboResult<Boolean> dubboResult = new DubboResult<>();
        dubboResult.setCode(bindUnionIdDubboResult.getCode());
        dubboResult.setMsg(bindUnionIdDubboResult.getMsg());
        return dubboResult;
    }

    /**
     * 联系人解绑微信unionId
     *
     * @param contactPersonUnbindUnionIdDubboDto 解绑参数
     * <AUTHOR>
     * @date 2023/6/16 09:54
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    @Override
    public DubboResult<Boolean> unbindUnionId(ContactPersonUnbindUnionIdDubboDto contactPersonUnbindUnionIdDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<Boolean> unbindUnionIdDubboResult = contactPersonDubbo.unbindUnionId(BeanUtil.copyProperties(contactPersonUnbindUnionIdDubboDto, com.ce.scrm.customer.dubbo.entity.dto.ContactPersonUnbindUnionIdDubboDto.class));
        if (unbindUnionIdDubboResult.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(unbindUnionIdDubboResult), new TypeReference<DubboResult<Boolean>>() {
            });
        }
        DubboResult<Boolean> dubboResult = new DubboResult<>();
        dubboResult.setCode(unbindUnionIdDubboResult.getCode());
        dubboResult.setMsg(unbindUnionIdDubboResult.getMsg());
        return dubboResult;
    }

    /**
     * 根据绑定unionId获取联系人客户数据
     *
     * @param bindUnionIdQueryDubboDto 查询参数
     * <AUTHOR>
     * @date 2023/6/16 10:28
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonCustomerDataDubboView>>
     **/
    @Override
    public DubboResult<List<ContactPersonCustomerDataDubboView>> getContactPersonDataByUnionId(BindUnionIdQueryDubboDto bindUnionIdQueryDubboDto) {
        com.ce.scrm.customer.dubbo.entity.response.DubboResult<List<com.ce.scrm.customer.dubbo.entity.view.ContactPersonCustomerDataDubboView>> listDubboResult = contactPersonDubbo.getContactPersonDataByUnionId(BeanUtil.copyProperties(bindUnionIdQueryDubboDto, com.ce.scrm.customer.dubbo.entity.dto.BindUnionIdQueryDubboDto.class));
        if (listDubboResult.checkSuccess()) {
            return JSON.parseObject(JSON.toJSONString(listDubboResult), new TypeReference<DubboResult<List<ContactPersonCustomerDataDubboView>>>() {
            });
        }
        DubboResult<List<ContactPersonCustomerDataDubboView>> dubboResult = new DubboResult<>();
        dubboResult.setCode(listDubboResult.getCode());
        dubboResult.setMsg(listDubboResult.getMsg());
        return dubboResult;
    }
}