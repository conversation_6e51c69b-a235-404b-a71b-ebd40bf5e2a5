package com.ce.scrm.customer.dubbo.service7;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.customer.dubbo.api7.IRocketTopicSwitchDubbo;
import com.ce.scrm.customer.dubbo.entity7.dto.RocketTopicSwitchDubboDto;
import com.ce.scrm.customer.dubbo.entity7.response.DubboCodeMessageEnum;
import com.ce.scrm.customer.dubbo.entity7.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity7.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity7.view.CustomerDubboView;
import com.ce.scrm.customer.dubbo.entity7.view.RocketTopicSwitchView;
import com.ce.scrm.customer.service.business.IRocketTopicSwitchBusiness;
import com.ce.scrm.customer.service.business.entity.dto.RocketTopicSwitchBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.RocketTopicSwitchBusinessView;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * rocket mq 开关
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/7/10 17:08
 */
@Slf4j
@DubboService(interfaceClass = IRocketTopicSwitchDubbo.class)
public class RocketTopicSwitch7DubboService implements IRocketTopicSwitchDubbo {

    @Resource
    IRocketTopicSwitchBusiness iRocketTopicSwitchBusiness;

    @Override
    public DubboResult<Integer> getTopicSwitchByTopicName(String topicName) {
        DubboResult<Integer> dubboResult = new DubboResult<>();
        dubboResult.setCode(DubboCodeMessageEnum.REQUEST_SUCCESS.getCode());
        if (StringUtils.isEmpty(topicName)) {
            dubboResult.setCode(DubboCodeMessageEnum.INVALID_PARAM.getCode());
            dubboResult.setMsg("参数不能为空");
            return dubboResult;
        }
        Integer status = iRocketTopicSwitchBusiness.getTopicSwitchByTopicName(topicName);
        dubboResult.setData(status);
        return dubboResult;
    }

    @Override
    public DubboResult<List<RocketTopicSwitchView>> getTopicSwitchByConditation(RocketTopicSwitchDubboDto rocketTopicSwitchDubboDto) {
        DubboResult<List<RocketTopicSwitchView>> dubboResult = new DubboResult<>();
        dubboResult.setCode(DubboCodeMessageEnum.REQUEST_SUCCESS.getCode());
        RocketTopicSwitchBusinessDto rocketTopicSwitchBusinessDto = new RocketTopicSwitchBusinessDto();
        rocketTopicSwitchBusinessDto.setTopic(rocketTopicSwitchDubboDto.getTopic());
        rocketTopicSwitchBusinessDto.setStatus(rocketTopicSwitchDubboDto.getStatus());
        List<RocketTopicSwitchBusinessView> lists = iRocketTopicSwitchBusiness.getTopicSwitchByConditation(rocketTopicSwitchBusinessDto);
        List<RocketTopicSwitchView> viewList = Optional.of(lists).orElse(Lists.newArrayList()).stream().map(record->{
            RocketTopicSwitchView view = BeanUtil.copyProperties(record, RocketTopicSwitchView.class);
            return view;
        }).collect(Collectors.toList());
        dubboResult.setData(viewList);
        return dubboResult;
    }

    @Override
    public DubboResult<Boolean> update(RocketTopicSwitchDubboDto rocketTopicSwitchDubboDto) {
        DubboResult<Boolean> dubboResult = new DubboResult<>();
        dubboResult.setCode(DubboCodeMessageEnum.REQUEST_SUCCESS.getCode());
        if (StringUtils.isEmpty(rocketTopicSwitchDubboDto.getTopic()) || rocketTopicSwitchDubboDto.getStatus()==null){
            dubboResult.setCode(DubboCodeMessageEnum.INVALID_PARAM.getCode());
            dubboResult.setMsg("参数不能为空");
            return dubboResult;
        }
        RocketTopicSwitchBusinessDto rocketTopicSwitchBusinessDto = new RocketTopicSwitchBusinessDto();
        rocketTopicSwitchBusinessDto.setTopic(rocketTopicSwitchDubboDto.getTopic());
        rocketTopicSwitchBusinessDto.setStatus(rocketTopicSwitchDubboDto.getStatus());
        Boolean result = iRocketTopicSwitchBusiness.update(rocketTopicSwitchBusinessDto);
        dubboResult.setData(result);
        return dubboResult;
    }
}
