package com.ce.scrm.customer.dubbo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ce.scrm.customer.ScrmCustomerDubboApplication;
import com.ce.scrm.customer.dubbo.api.*;
import com.ce.scrm.customer.dubbo.entity.dto.*;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.ContactInfoDubboView;
import com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView;
import com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboView;
import com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ResourceLoader;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.IntStream;

@Slf4j
@SpringBootTest(classes = ScrmCustomerDubboApplication.class)
class ScrmCustomerDubboApplicationTests {

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private IAreaDubbo areaDubbo;
    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    IContactInfoDubbo contactInfoDubbo;
    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    IContactPersonDubbo contactPersonDubbo;
    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    ICustomerDubbo customerDubbo;
    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    IIndustryDubbo industryDubbo;
    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    ISyncIncrementDataDubbo syncIncrementDataDubbo;

    @Resource
    private ResourceLoader resourceLoader;


//    @Test
//    void getIndustryAllData() {
//        System.out.println(JSON.toJSONString(industryDubbo.getAllData()));
//    }

    @Test
    void getContactInfoDetailByWay() {

        ContactInfoDubboDto contactInfoDubboDto = new ContactInfoDubboDto();
        contactInfoDubboDto.setContactInfoId("36406e13840a48d986abf0fb86151b7f");
        contactInfoDubboDto.setContactType(4);
        DubboResult<ContactPersonDubboView> contactPersonDubboViewDubboResult = contactInfoDubbo.detailByWay(contactInfoDubboDto);
        System.out.println(JSON.toJSONString(contactPersonDubboViewDubboResult));
    }

    @Test
    void getContactInfoDetailList() {

        ContactInfoDubboDto contactInfoDubboDto = new ContactInfoDubboDto();
        List<Integer> contactTypeList = new ArrayList<>();
        contactTypeList.add(3);
        contactTypeList.add(4);
        contactInfoDubboDto.setContactTypeList(contactTypeList);
        //contactInfoDubboDto.setContactInfoId("36406e13840a48d986abf0fb86151b7f");
        contactInfoDubboDto.setContactPersonId("9bb4fd04cf614f9c9c7df5b8cd0aced8");
        DubboResult<List<ContactInfoDubboView>> listDubboResult = contactInfoDubbo.detailList(contactInfoDubboDto);
        System.out.println(JSON.toJSONString(listDubboResult));
    }

    @Test
    void getContactPersonDetail() {

        ContactPersonDubboDto contactPersonDubboDto = new ContactPersonDubboDto();
        contactPersonDubboDto.setContactPersonId("1731510781754736672");
        contactPersonDubboDto.setSourceKey("scrm");
        contactPersonDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        DubboResult<ContactPersonDubboView> detail = contactPersonDubbo.detail(contactPersonDubboDto);
        System.out.println(JSON.toJSONString(detail));
    }

    @Test
    void getContactPersonCustomerContactDetail() {

        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        customerDetailDubboDto.setCustomerId("010213596126");
        customerDetailDubboDto.setSourceKey("scrm");
        customerDetailDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        DubboResult<CustomerDubboView> customerDubboViewDubboResult = contactPersonDubbo.customerContactDetail(customerDetailDubboDto);
        System.out.println(JSON.toJSONString(customerDubboViewDubboResult));
    }

    @Test
    void getContactPersonCustomerContactDetailByMemberCodes() {

        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        List<String> memberCodes = new ArrayList<>();
        memberCodes.add("CEM10300067");
        customerDetailDubboDto.setMemberCodeList(memberCodes);
        customerDetailDubboDto.setSourceKey("scrm");
        customerDetailDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        DubboResult<List<CustomerDubboView>> customerDubboViewDubboResult = contactPersonDubbo.customerContactDetailByMemberCodes(customerDetailDubboDto);
        System.out.println(JSON.toJSONString(customerDubboViewDubboResult));
    }

    @Test
    void getContactPersonDetailList() {

        ContactPersonDubboDto contactPersonDubboDto = new ContactPersonDubboDto();
        List<String> contactPersonIdList = new ArrayList<>();
        contactPersonIdList.add("d54e7d8d724a4c90aaf6edfcdd979132");
        contactPersonIdList.add("");
        contactPersonDubboDto.setContactPersonIdList(contactPersonIdList);
        DubboResult<List<ContactPersonDubboView>> listDubboResult = contactPersonDubbo.detailList(contactPersonDubboDto);
        System.out.println(JSON.toJSONString(listDubboResult));
    }

    @Test
    void getCustomerDetail() {

        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        customerDetailDubboDto.setCustomerId("010213594187");
        customerDetailDubboDto.setSourceKey("scrm");
        customerDetailDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        DubboResult<CustomerDubboView> detail = customerDubbo.detail(customerDetailDubboDto);
        System.out.println(JSON.toJSONString(detail));
    }

    @Test
    void getCustomerPage() {
        List<String> customerIdList = new ArrayList<>();
        IntStream.range(0, 1000).boxed().forEach(integer -> {
            customerIdList.add("1");
        });
        CustomerPageDubboDto customerPageDubboDto = new CustomerPageDubboDto();
        customerPageDubboDto.setSourceKey("scrm");
        customerPageDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        customerPageDubboDto.setCustomerIdList(customerIdList);
        DubboResult<DubboPageInfo<CustomerDubboView>> dubboPageInfoDubboResult = customerDubbo.pageList(customerPageDubboDto);
        System.out.println(JSON.toJSONString(dubboPageInfoDubboResult));
    }

    @Test
    void getDataByBigDate() throws IOException {
        String params = "[{\"customerId\":\"1286929\",\"customerType\":\"1\",\"customerName\":\"东莞市威威实业有限公司\",\"certificateType\":\"1\",\"provinceCode\":\"440000\",\"cityCode\":\"441900\",\"districtCode\":\"441901\",\"registerAddress\":\"育兴路92号集运大楼2楼\",\"deleteFlag\":\"1\",\"createTime\":\"2014-02-22 19:33:18\",\"updateTime\":\"2023-04-20 14:10:27\"},{\"customerId\":\"53ED9C7DD3B946DA89246F95CC14CB2E\",\"customerType\":\"1\",\"customerName\":\"长度的限制客户\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110102\",\"registerAddress\":\"是vs的风格和你的世界观的链接过来我看了。看刮风打雷阿克苏解放立刻什么法拉第2759536过了多久过来的口感恐怕螺丝钉解放了是顶级联赛的凯瑟琳\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 10:02:07\",\"updateTime\":\"2023-04-23 10:02:07\"},{\"customerId\":\"EC5ADD37E0FB440783867A40521802FC\",\"customerType\":\"1\",\"customerName\":\"这是客户名称哦这是客户名称哦这是客户名称哦这是客户名称哦这是客户名称哦这是客户名称哦这是客户名称哦了\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110102\",\"registerAddress\":\"这是详细地址哦噢噢噢噢噢噢噢噢哦哦\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 10:31:57\",\"updateTime\":\"2023-04-23 10:31:57\"},{\"customerId\":\"20A4E5E907D242B7BF5B60260A72926B\",\"customerType\":\"1\",\"customerName\":\"新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110102\",\"registerAddress\":\"地址新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业国内公司新增企业\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 15:07:48\",\"updateTime\":\"2023-04-23 15:07:48\"},{\"customerId\":\"7FF37BAC9D6E4ABC815D57A8FB3B0BD8\",\"customerType\":\"1\",\"customerName\":\"中机城市建设有限公司\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110119\",\"registerAddress\":\"北京市延庆区中关村延庆园风谷四路8号院27号楼2018\",\"deleteFlag\":\"1\",\"createTime\":\"2022-10-25 14:53:08\",\"updateTime\":\"2023-04-20 16:48:52\"},{\"customerId\":\"BC206D7990504C59A79CAE2FED1A4A2F\",\"customerType\":\"1\",\"customerName\":\"测试客户（莉莉国内客户）\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110115\",\"registerAddress\":\"详细地址333333333333333333333333\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-20 12:45:35\",\"updateTime\":\"2023-04-20 12:45:35\"},{\"customerId\":\"6C290D4447714BDB934BA97E2CCB6B51\",\"customerType\":\"2\",\"customerName\":\"个人客户\",\"certificateType\":\"SF\",\"certificateCode\":\"371082197910205496\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110102\",\"registerAddress\":\"详细地址00001\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-20 15:02:32\",\"updateTime\":\"2023-04-20 15:02:32\"},{\"customerId\":\"15B6505A13674975A2B618364BE831BE\",\"customerType\":\"1\",\"customerName\":\"北京悦呈工程管理有限公司\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110119\",\"registerAddress\":\"北京市延庆区延庆镇百泉街9号1幢1至2层1-115\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-20 19:11:13\",\"updateTime\":\"2023-04-20 19:11:13\"},{\"customerId\":\"6B8D6B502149403DB2424BBFD49DF42F\",\"customerType\":\"3\",\"customerName\":\"国内及港澳台\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110102\",\"registerAddress\":\"33\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-21 13:10:08\",\"updateTime\":\"2023-04-21 13:10:08\"},{\"customerId\":\"FFFC7B4697C54EB08F8871896306F8C7\",\"customerType\":\"1\",\"customerName\":\"测试公司张三三三\",\"certificateType\":\"\",\"certificateCode\":\"\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110101\",\"registerAddress\":\"朝阳区\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-21 14:40:50\",\"updateTime\":\"2023-04-21 14:40:50\"},{\"customerId\":\"479C8F1C5EBB45C883A74177008B18C0\",\"customerType\":\"3\",\"customerName\":\"p\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110102\",\"registerAddress\":\"132\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-21 15:10:56\",\"updateTime\":\"2023-04-21 15:10:56\"},{\"customerId\":\"B099FFDB7CE44638ABAE88A1C5C4D2F0\",\"customerType\":\"1\",\"customerName\":\"创建国内客户\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110115\",\"registerAddress\":\"经济技术开发区数码庄园B座001\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 10:01:00\",\"updateTime\":\"2023-04-23 10:01:00\"},{\"customerId\":\"69EC6D7DFEC34894AA263495AD46F015\",\"customerType\":\"1\",\"customerName\":\"气味的客户\",\"certificateType\":\"\",\"certificateCode\":\"\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110101\",\"registerAddress\":\"这是这个详细地址韩国女星ghbnj7578(hjffhjhvh)\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 10:37:12\",\"updateTime\":\"2023-04-23 10:37:12\"},{\"customerId\":\"900B0460C04A4C84A1960A5D1A328650\",\"customerType\":\"1\",\"customerName\":\"气味的客人\",\"certificateType\":\"\",\"certificateCode\":\"\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110101\",\"registerAddress\":\"这是这个详细地址韩国女星ghbnj7578(hjffhjhvh)\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 10:38:10\",\"updateTime\":\"2023-04-23 10:38:10\"},{\"customerId\":\"B14B434530844C468E97ED7B425F4B19\",\"customerType\":\"3\",\"customerName\":\"添加国外客户\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110115\",\"registerAddress\":\"数码庄园A座\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 11:10:47\",\"updateTime\":\"2023-04-23 11:10:47\"},{\"customerId\":\"BCB4AA7459934EE6AF1BBA24106E0316\",\"customerType\":\"2\",\"customerName\":\"个人客户一身份证\",\"certificateType\":\"SF\",\"certificateCode\":\"360830197604012922\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110102\",\"registerAddress\":\"数码庄园001\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 12:39:55\",\"updateTime\":\"2023-04-23 12:39:55\"},{\"customerId\":\"970150321EF44BA2940E40863F0BD4A0\",\"customerType\":\"2\",\"customerName\":\"军人身份证\",\"certificateType\":\"JR\",\"certificateCode\":\"2321456987\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110102\",\"registerAddress\":\"超青羊区\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 13:10:25\",\"updateTime\":\"2023-04-23 13:10:25\"},{\"customerId\":\"B6E3F6BAC9B34CD8AD3102ABC96DC258\",\"customerType\":\"2\",\"customerName\":\"护照身份证\",\"certificateType\":\"HZ\",\"certificateCode\":\"2564789\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110102\",\"registerAddress\":\"护照身份证\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 13:22:48\",\"updateTime\":\"2023-04-23 13:22:48\"},{\"customerId\":\"4CB2B2525AA440D09C526DBDF4F5FFE5\",\"customerType\":\"2\",\"customerName\":\"港澳居民身份证\",\"certificateType\":\"GA\",\"certificateCode\":\"123456\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110102\",\"registerAddress\":\"详细地址\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 13:30:38\",\"updateTime\":\"2023-04-23 13:30:38\"},{\"customerId\":\"9CB17ED413374F5A9026F765040BD72A\",\"customerType\":\"2\",\"customerName\":\"台湾居民身份证\",\"certificateType\":\"TW\",\"certificateCode\":\"123456\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110102\",\"registerAddress\":\"333333333\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 13:37:54\",\"updateTime\":\"2023-04-23 13:37:54\"},{\"customerId\":\"A2DBEEBFE7404B74A5BE8F6817715E85\",\"customerType\":\"2\",\"customerName\":\"外国人身份证\",\"certificateType\":\"WG\",\"certificateCode\":\"236547885\",\"provinceCode\":\"120000\",\"cityCode\":\"120100\",\"districtCode\":\"120102\",\"registerAddress\":\"水水水水水水水水水水水\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 13:44:24\",\"updateTime\":\"2023-04-23 13:44:24\"},{\"customerId\":\"0C01515DE2C34263B56A9F1107E407DA\",\"customerType\":\"1\",\"customerName\":\"北京祥林博瑞文化传播有限公司\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110105\",\"registerAddress\":\"北京市朝阳区朝阳路67号9号楼-1层01128\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 15:10:20\",\"updateTime\":\"2023-04-23 15:10:20\"},{\"customerId\":\"5CC1C317935D4A0BADF778A7BCDD63F1\",\"customerType\":\"1\",\"customerName\":\"正在添加企微\",\"certificateType\":\"\",\"certificateCode\":\"\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110101\",\"registerAddress\":\"加错哦\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 15:55:42\",\"updateTime\":\"2023-04-23 15:55:42\"},{\"customerId\":\"2B98146F0B274D14B65584F4EA8BCA42\",\"customerType\":\"1\",\"customerName\":\"北京天眼文化传媒有限公司\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110113\",\"registerAddress\":\"北京市顺义区南彩镇彩达二街2号223\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 15:59:50\",\"updateTime\":\"2023-04-23 15:59:50\"},{\"customerId\":\"E02A6D7DC7764956AE75BAB6591B6D81\",\"customerType\":\"1\",\"customerName\":\"这是一个人\",\"certificateType\":\"\",\"certificateCode\":\"\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110101\",\"registerAddress\":\"在于如何看待这个\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 16:00:25\",\"updateTime\":\"2023-04-23 16:00:25\"},{\"customerId\":\"879D8CCC9D7D4C6E9FD98E7EB6F9D290\",\"customerType\":\"2\",\"customerName\":\"丽丽\",\"certificateType\":\"JR\",\"certificateCode\":\"147258\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110101\",\"registerAddress\":\"在于自己在别人\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 16:02:58\",\"updateTime\":\"2023-04-23 16:02:58\"},{\"customerId\":\"DE8F77A21E9147409A90C445B133EA55\",\"customerType\":\"1\",\"customerName\":\"青羊工业总部基地\",\"certificateType\":\"\",\"certificateCode\":\"\",\"provinceCode\":\"110000\",\"cityCode\":\"110100\",\"districtCode\":\"110101\",\"registerAddress\":\"老同\",\"createWay\":\"ce0\",\"deleteFlag\":\"1\",\"createTime\":\"2023-04-23 18:31:04\",\"updateTime\":\"2023-04-23 18:31:04\"}]";
        JSONArray jsonArray = JSON.parseArray(params);
        jsonArray.forEach(jsonObject -> {
            syncIncrementDataDubbo.sync(1, JSON.toJSONString(jsonObject));
        });
        params = "[{\"customerId\":\"1423136\",\"contactPersonId\":\"0078A85AE1F74041B6E188503ECBE453\",\"contactPersonName\":\"米斯特王\",\"gender\":\"3\",\"position\":\"高级的\",\"createTime\":\"2023-04-23 13:44:10\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 13:44:50\"},{\"customerId\":\"8C634588E77941FEA56B0F6C64D52D01\",\"contactPersonId\":\"029A74CFDA83478C80120AC564B1BD96\",\"contactPersonName\":\"你好\",\"gender\":\"2\",\"position\":\"你好\",\"createTime\":\"2023-04-20 17:33:27\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-20 17:33:27\"},{\"customerId\":\"879D8CCC9D7D4C6E9FD98E7EB6F9D290\",\"contactPersonId\":\"0F5872BEE0C747AD84A6DAF246419BAB\",\"contactPersonName\":\"这些\",\"gender\":\"1\",\"position\":\"不过这个\",\"createTime\":\"2023-04-23 16:02:58\",\"creator\":\"15077\",\"updateTime\":\"2023-04-23 16:02:58\"},{\"customerId\":\"B14B434530844C468E97ED7B425F4B19\",\"contactPersonId\":\"11021D9FB8C1483E8A2D96B9D01C3E07\",\"contactPersonName\":\"联系人三\",\"gender\":\"1\",\"position\":\"职位三\",\"createTime\":\"2023-04-23 11:17:42\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 11:17:42\"},{\"customerId\":\"B099FFDB7CE44638ABAE88A1C5C4D2F0\",\"contactPersonId\":\"115F60137DBA44ABA5CBC65CD25AF792\",\"contactPersonName\":\"创建国内客户姓名\",\"gender\":\"1\",\"position\":\"软件测试哈\",\"createTime\":\"2023-04-23 10:01:00\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 10:49:47\"},{\"customerId\":\"0C01515DE2C34263B56A9F1107E407DA\",\"contactPersonId\":\"1160522B310C45EE94BC2C9FC6500830\",\"contactPersonName\":\"北京祥林博瑞文化传播有限公司\",\"position\":\"\",\"createTime\":\"2023-04-23 15:09:23\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 15:09:23\"},{\"customerId\":\"EF288A2243554373A93EF000A3E4B8AB\",\"contactPersonId\":\"12A597B581D1466DA36CE5E92510C190\",\"contactPersonName\":\"张三三\",\"gender\":\"2\",\"position\":\"D_CPOSITION_001\",\"createTime\":\"2023-04-19 15:57:01\",\"creator\":\"37928\",\"updateTime\":\"2023-04-19 15:57:01\"},{\"customerId\":\"E02A6D7DC7764956AE75BAB6591B6D81\",\"contactPersonId\":\"192C13AFD12F4E5C8372A9B53AFFC8ED\",\"contactPersonName\":\"在一起了\",\"gender\":\"1\",\"position\":\"这么好\",\"createTime\":\"2023-04-23 16:04:57\",\"creator\":\"15077\",\"updateTime\":\"2023-04-23 16:04:57\"},{\"customerId\":\"FFFC7B4697C54EB08F8871896306F8C7\",\"contactPersonId\":\"19A2604D94834F59A56B41D0A6EAA925\",\"contactPersonName\":\"张三\",\"gender\":\"1\",\"position\":\"总监\",\"createTime\":\"2023-04-21 15:42:33\",\"creator\":\"37928\",\"updateTime\":\"2023-04-21 15:42:33\"},{\"customerId\":\"FFFC7B4697C54EB08F8871896306F8C7\",\"contactPersonId\":\"1B52D8CD63AE492B8466692595257F92\",\"contactPersonName\":\"张三三三\",\"gender\":\"1\",\"position\":\"总监\",\"createTime\":\"2023-04-21 15:44:44\",\"creator\":\"37928\",\"updateTime\":\"2023-04-21 15:44:44\"},{\"customerId\":\"6B8D6B502149403DB2424BBFD49DF42F\",\"contactPersonId\":\"1D3AAFF245AD49F299F07EF9C3B896C9\",\"contactPersonName\":\"问我\",\"gender\":\"1\",\"position\":\"测试\",\"createTime\":\"2023-04-21 13:10:08\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-21 13:10:08\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"271A4C8BC40C4FF2A14A266C01EC947E\",\"contactPersonName\":\"添加\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 13:56:40\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 13:56:40\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"28C87038B1E549CD8FA325C17F963B80\",\"contactPersonName\":\"四月\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 11:47:35\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 11:47:35\"},{\"customerId\":\"010213793898\",\"contactPersonId\":\"2933249664BE41DE9B37725C97A70A49\",\"contactPersonName\":\"老客户线索添加的联系人哦h\",\"gender\":\"1\",\"position\":\"职位啦啦啦\",\"createTime\":\"2023-04-23 11:43:09\",\"creator\":\"16525\",\"updateTime\":\"2023-04-23 11:43:51\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"2A5CC7D9E36E4C44A4497960B588B582\",\"contactPersonName\":\"添加\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 13:56:37\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 13:56:37\"},{\"customerId\":\"EC5ADD37E0FB440783867A40521802FC\",\"contactPersonId\":\"2D7ADB593E4444A796A242672933492F\",\"contactPersonName\":\"二是一个名这是一个名这是一个名这是一个名这是一个名这是一个名这是一个名这是一个名这是一个名这是一个名\",\"gender\":\"3\",\"position\":\"这个是职位这个是职位这个是职位这个是职位这个是职位这个是职位这个是职位这个是职位这个是职位这个是职位\",\"createTime\":\"2023-04-23 10:31:57\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 10:31:57\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"3462687679824FCEAC717FC4B2B568CF\",\"contactPersonName\":\"添加\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 13:55:54\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 13:55:54\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"35F5B75F62004ED6BB2BBEBEF5D10E3F\",\"contactPersonName\":\"添加\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 13:56:01\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 13:56:01\"},{\"customerId\":\"0C01515DE2C34263B56A9F1107E407DA\",\"contactPersonId\":\"42F58358D1474645AC369939E024804C\",\"contactPersonName\":\"武龙岗\",\"position\":\"D_CPOSITION_001\",\"createTime\":\"2023-04-23 15:10:19\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 15:10:19\"},{\"customerId\":\"B6E3F6BAC9B34CD8AD3102ABC96DC258\",\"contactPersonId\":\"43380087F37A40159AD60DF3559C9202\",\"contactPersonName\":\"联系人一\",\"gender\":\"1\",\"position\":\"护照\",\"createTime\":\"2023-04-23 13:22:48\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 13:22:48\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"43B0406FBCF3486CBF8790AFED83424A\",\"contactPersonName\":\"四月\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 11:47:14\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 11:47:14\"},{\"customerId\":\"EF288A2243554373A93EF000A3E4B8AB\",\"contactPersonId\":\"455ABEE8C26947B68DE95DB78CDE9498\",\"contactPersonName\":\"张三三\",\"gender\":\"2\",\"position\":\"总监\",\"createTime\":\"2023-04-19 15:55:56\",\"creator\":\"37928\",\"updateTime\":\"2023-04-19 15:55:56\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"45AA29A2F03A4AD9A9F623D04B1550D1\",\"contactPersonName\":\"添加\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 13:56:14\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 13:56:14\"},{\"customerId\":\"B14B434530844C468E97ED7B425F4B19\",\"contactPersonId\":\"46C5145CBA694FCE8327694B6A333647\",\"contactPersonName\":\"国外联系人\",\"gender\":\"1\",\"position\":\"职位一\",\"createTime\":\"2023-04-23 11:10:47\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 11:10:47\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"4AAD576D2E0540BF95DFA3FD247E15BE\",\"contactPersonName\":\"四月\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 11:47:33\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 11:47:33\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"4BD450428946433E91E6251FAB7D1E45\",\"contactPersonName\":\"四月\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 11:47:34\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 11:47:34\"},{\"customerId\":\"BCB4AA7459934EE6AF1BBA24106E0316\",\"contactPersonId\":\"4DFCC9A20ED0497482DE347AFE1C7490\",\"contactPersonName\":\"职位三\",\"position\":\"职位三\",\"createTime\":\"2023-04-23 12:53:22\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 12:53:22\"},{\"customerId\":\"1422677\",\"contactPersonId\":\"4E3C5085976144E08897B36FF341997B\",\"contactPersonName\":\"今日添加客户\",\"gender\":\"2\",\"position\":\"今日新加职位（销售）\",\"createTime\":\"2023-04-23 11:05:10\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 11:09:04\"},{\"customerId\":\"7FF37BAC9D6E4ABC815D57A8FB3B0BD8\",\"contactPersonId\":\"529A047CF2B34B5A964DB6449624FB4B\",\"contactPersonName\":\"赵**\",\"position\":\"\",\"createTime\":\"2023-04-20 16:48:52\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-20 16:48:52\"},{\"customerId\":\"FFFC7B4697C54EB08F8871896306F8C7\",\"contactPersonId\":\"530C76FEEB9F4443B2DC47132217F718\",\"contactPersonName\":\"笑言\",\"gender\":\"1\",\"position\":\"总监\",\"createTime\":\"2023-04-21 15:45:07\",\"creator\":\"37928\",\"updateTime\":\"2023-04-21 15:45:07\"},{\"customerId\":\"69EC6D7DFEC34894AA263495AD46F015\",\"contactPersonId\":\"53A85F96275943A49271ABC6E31C5EB6\",\"contactPersonName\":\"来一个\",\"gender\":\"2\",\"position\":\"以为\",\"createTime\":\"2023-04-23 11:40:09\",\"creator\":\"15077\",\"updateTime\":\"2023-04-23 11:40:09\"},{\"customerId\":\"53ED9C7DD3B946DA89246F95CC14CB2E\",\"contactPersonId\":\"5D364983B8EB4E289C920D869B912EA5\",\"contactPersonName\":\"刚添加的\",\"createTime\":\"2023-04-23 10:15:35\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 10:15:35\",\"operator\":\"96dc187f54a64e89b506b160c8b43531\"},{\"customerId\":\"53ED9C7DD3B946DA89246F95CC14CB2E\",\"contactPersonId\":\"5F5BDD610CF740269A114C62FCF9DEE7\",\"contactPersonName\":\"张三三\",\"gender\":\"1\",\"position\":\"总监\",\"createTime\":\"2023-04-23 18:52:45\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 18:52:45\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"61323C97C5D14593BB3E60159633B69D\",\"contactPersonName\":\"添加\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 13:56:38\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 13:56:38\"},{\"customerId\":\"69EC6D7DFEC34894AA263495AD46F015\",\"contactPersonId\":\"616125CB1EA14B7090CC16EF52B54D17\",\"contactPersonName\":\"联系人姓名联系人姓名\",\"gender\":\"3\",\"position\":\"以为职位hfb()\",\"createTime\":\"2023-04-23 10:37:12\",\"creator\":\"15077\",\"updateTime\":\"2023-04-23 10:37:12\"},{\"customerId\":\"B099FFDB7CE44638ABAE88A1C5C4D2F0\",\"contactPersonId\":\"66D756ABD412417E99DC87D831C63F7B\",\"contactPersonName\":\"常用联系人哈\",\"position\":\"IT负责人哈\",\"createTime\":\"2023-04-23 10:14:19\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 10:52:03\",\"operator\":\"96dc187f54a64e89b506b160c8b43531\"},{\"customerId\":\"B14B434530844C468E97ED7B425F4B19\",\"contactPersonId\":\"7353960A9CDA4028B79C9ADFF028094F\",\"contactPersonName\":\"写记录\",\"createTime\":\"2023-04-23 11:18:47\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 11:18:47\",\"operator\":\"aa1b82df919842a4a83f3e9f96aae737\"},{\"customerId\":\"20A4E5E907D242B7BF5B60260A72926B\",\"contactPersonId\":\"77B7FEDB647D4BBEB18A282B7154D5FC\",\"contactPersonName\":\"联系人姓名联系人姓名联系人姓名联系人姓名联系人姓名联系人姓名联系人姓名联系人姓名联系人姓名联系人姓名\",\"gender\":\"1\",\"position\":\"职位长度哈职位长度哈职位长度哈职位长度哈职位长度哈职位长度哈职位长度哈职位长度哈职位长度哈职位长度哈\",\"createTime\":\"2023-04-23 15:07:48\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 15:07:48\"},{\"customerId\":\"53ED9C7DD3B946DA89246F95CC14CB2E\",\"contactPersonId\":\"7D22B910B71B4CAB866D0FA0952DF7CD\",\"contactPersonName\":\"李思思\",\"gender\":\"2\",\"position\":\"总监\",\"createTime\":\"2023-04-23 18:55:05\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 18:55:05\"},{\"customerId\":\"69EC6D7DFEC34894AA263495AD46F015\",\"contactPersonId\":\"7DF6BFAFB8554DCBA550CA2E73320B0C\",\"contactPersonName\":\"来微信添加\",\"gender\":\"3\",\"position\":\"职位哦\",\"createTime\":\"2023-04-23 11:02:05\",\"creator\":\"15077\",\"updateTime\":\"2023-04-23 11:02:05\"},{\"customerId\":\"53ED9C7DD3B946DA89246F95CC14CB2E\",\"contactPersonId\":\"846263B60AE641B9A1DE5F573F07DABB\",\"contactPersonName\":\"这是一个名这是一个名这是一个名这是一个名这是一个名这是一个名这是一个名这是一个名这是一个名这是一个名\",\"gender\":\"1\",\"position\":\"这个是职位这个是职位这个是职位这个是职位这个是职位这个是职位这个是职位这个是职位这个是职位这个是职位\",\"createTime\":\"2023-04-23 10:02:07\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 10:02:07\"},{\"customerId\":\"9CB17ED413374F5A9026F765040BD72A\",\"contactPersonId\":\"8505EF8993674BF797EA72E137D33239\",\"contactPersonName\":\"试试\",\"gender\":\"1\",\"position\":\"请求\",\"createTime\":\"2023-04-23 13:37:54\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 13:37:54\"},{\"customerId\":\"EF288A2243554373A93EF000A3E4B8AB\",\"contactPersonId\":\"867863A15D524798B8CD2CF45293FAE1\",\"contactPersonName\":\"李老六\",\"gender\":\"3\",\"position\":\"总监助理\",\"createTime\":\"2023-04-20 12:36:36\",\"creator\":\"37928\",\"updateTime\":\"2023-04-20 12:36:36\"},{\"customerId\":\"6C290D4447714BDB934BA97E2CCB6B51\",\"contactPersonId\":\"876C7476039F4115A85C3BBC7EB8D430\",\"contactPersonName\":\"王雨莎\",\"gender\":\"2\",\"position\":\"开发工程师\",\"createTime\":\"2023-04-20 15:02:32\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-20 15:02:32\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"88A80C8C8A284D12BFC22EE1B6F8E07A\",\"contactPersonName\":\"添加\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 13:56:36\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 13:56:36\"},{\"customerId\":\"BCB4AA7459934EE6AF1BBA24106E0316\",\"contactPersonId\":\"89D06E11CB544F7883101A771A032B41\",\"contactPersonName\":\"个人联系一\",\"gender\":\"1\",\"position\":\"开发\",\"createTime\":\"2023-04-23 12:39:55\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 12:39:55\"},{\"customerId\":\"B099FFDB7CE44638ABAE88A1C5C4D2F0\",\"contactPersonId\":\"8F00BBB365FB45AC9E2C50B923F8ABF0\",\"contactPersonName\":\"创建国内客户二\",\"gender\":\"2\",\"position\":\"开发\",\"createTime\":\"2023-04-23 10:10:03\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 10:10:03\"},{\"customerId\":\"53ED9C7DD3B946DA89246F95CC14CB2E\",\"contactPersonId\":\"95D66166E79A4DE3A0FB96FEE0A44BBD\",\"contactPersonName\":\"添加联系人添加联系人添加联系人添加联系人添加联系人添加联系人添加联系人添加联系人添加联系人添加联系人\",\"gender\":\"3\",\"position\":\"这是新职位这是新职位这是新职位这是新职位这是新职位这是新职位这是新职位这是新职位这是新职位这是新职位\",\"createTime\":\"2023-04-23 10:10:30\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 10:10:30\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"97F0265C486D4AE58C2161917A5D5CA0\",\"contactPersonName\":\"添加\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 13:56:21\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 13:56:21\"},{\"customerId\":\"1A627542F3A140D18DD8ECE7EC8DCF97\",\"contactPersonId\":\"98E7188C02F3479089F21E4B29C70643\",\"contactPersonName\":\"笑言\",\"gender\":\"2\",\"position\":\"公司老板\",\"createTime\":\"2023-03-31 14:13:20\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 14:49:52\"},{\"customerId\":\"7FF37BAC9D6E4ABC815D57A8FB3B0BD8\",\"contactPersonId\":\"994AEF3DBC57456EB75A1D4ABD383F3A\",\"contactPersonName\":\"赵**\",\"position\":\"\",\"createTime\":\"2023-04-20 16:47:22\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-20 16:47:22\"},{\"customerId\":\"53ED9C7DD3B946DA89246F95CC14CB2E\",\"contactPersonId\":\"99569BF0F1CB4C9FA4EBB8B8515CA751\",\"contactPersonName\":\"无法\",\"gender\":\"2\",\"position\":\"职位s(shangwu)\",\"createTime\":\"2023-04-23 10:11:59\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 10:13:23\"},{\"customerId\":\"1265833\",\"contactPersonId\":\"9DE302C902014C978E14F4A36F947259\",\"contactPersonName\":\"12\",\"position\":\"不能\",\"createTime\":\"2023-04-23 16:53:58\",\"creator\":\"91995\",\"updateTime\":\"2023-04-23 16:53:58\"},{\"customerId\":\"B099FFDB7CE44638ABAE88A1C5C4D2F0\",\"contactPersonId\":\"9E1961C43E3B4F799A57EDC27FE4A455\",\"contactPersonName\":\"新增联系人\",\"gender\":\"3\",\"position\":\"合河\",\"createTime\":\"2023-04-23 11:02:09\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 11:02:09\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"9F83681B4EF44DAEA2CC171944E463D3\",\"contactPersonName\":\"添加\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 13:56:18\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 13:56:18\"},{\"customerId\":\"15B6505A13674975A2B618364BE831BE\",\"contactPersonId\":\"A4EA4D02C83740AB89603A261693592A\",\"contactPersonName\":\"贾梦凡\",\"position\":\"D_CPOSITION_001\",\"createTime\":\"2023-04-20 19:11:13\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-20 19:11:13\"},{\"customerId\":\"479C8F1C5EBB45C883A74177008B18C0\",\"contactPersonId\":\"A5701791446D47F09D10C1E973660E10\",\"contactPersonName\":\"新添加客户一\",\"position\":\"国外职位\",\"createTime\":\"2023-04-21 15:10:56\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-21 15:10:56\"},{\"customerId\":\"DE8F77A21E9147409A90C445B133EA55\",\"contactPersonId\":\"A609C9B182824056BA67371141C33B3F\",\"contactPersonName\":\"张三\",\"gender\":\"1\",\"position\":\"职位\",\"createTime\":\"2023-04-23 18:31:04\",\"creator\":\"15077\",\"updateTime\":\"2023-04-23 18:31:04\"},{\"customerId\":\"0C01515DE2C34263B56A9F1107E407DA\",\"contactPersonId\":\"B0358BA93BCD4F40BFE557F6B9E78DC1\",\"contactPersonName\":\"武龙岗\",\"position\":\"D_CPOSITION_001\",\"createTime\":\"2023-04-23 15:09:23\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 15:09:23\"},{\"customerId\":\"20A4E5E907D242B7BF5B60260A72926B\",\"contactPersonId\":\"B1FFDDABD02F4F0DAFFDF51573E9FA4B\",\"contactPersonName\":\"联系人二\",\"gender\":\"2\",\"position\":\"职位二\",\"createTime\":\"2023-04-23 15:52:36\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 15:52:36\"},{\"customerId\":\"FFFC7B4697C54EB08F8871896306F8C7\",\"contactPersonId\":\"B39CE53E877C4324B29542FED346F265\",\"contactPersonName\":\"张三\",\"gender\":\"1\",\"position\":\"总监\",\"createTime\":\"2023-04-21 15:42:54\",\"creator\":\"37928\",\"updateTime\":\"2023-04-21 15:42:54\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"B3DA49E9BF474701BACE81B01339F44C\",\"contactPersonName\":\"四月\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 11:47:52\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 11:47:52\"},{\"customerId\":\"B14B434530844C468E97ED7B425F4B19\",\"contactPersonId\":\"B6220CF6D09F43F48A4EF4D905748A9F\",\"contactPersonName\":\"职位联系人二\",\"gender\":\"3\",\"position\":\"职位二\",\"createTime\":\"2023-04-23 11:16:25\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 11:16:25\"},{\"customerId\":\"BC206D7990504C59A79CAE2FED1A4A2F\",\"contactPersonId\":\"B783E10E02614F50AC88F56487DFA940\",\"contactPersonName\":\"李兰页\",\"gender\":\"2\",\"position\":\"测试工程师\",\"createTime\":\"2023-04-20 12:45:35\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-20 12:45:35\"},{\"customerId\":\"BCB4AA7459934EE6AF1BBA24106E0316\",\"contactPersonId\":\"B8B5E48F6E71488E87B47AB2319D88F4\",\"contactPersonName\":\"联系人二\",\"gender\":\"2\",\"position\":\"职位二\",\"createTime\":\"2023-04-23 12:52:55\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 12:52:55\"},{\"customerId\":\"970150321EF44BA2940E40863F0BD4A0\",\"contactPersonId\":\"B990268161124F579C1378FC8DD1CF19\",\"contactPersonName\":\"军人姓名\",\"gender\":\"2\",\"position\":\"军人\",\"createTime\":\"2023-04-23 13:10:25\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 13:10:25\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"BF648822C549486999AA461788D58C6D\",\"contactPersonName\":\"添加\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 13:56:16\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 13:56:16\"},{\"customerId\":\"FFFC7B4697C54EB08F8871896306F8C7\",\"contactPersonId\":\"C14CC90ED28E4A5983E4E904677D226A\",\"contactPersonName\":\"笑言\",\"gender\":\"2\",\"position\":\"总监\",\"createTime\":\"2023-04-21 15:44:26\",\"creator\":\"37928\",\"updateTime\":\"2023-04-21 15:44:26\"},{\"customerId\":\"20A4E5E907D242B7BF5B60260A72926B\",\"contactPersonId\":\"C1AA97188B164104BD83D32023D632C5\",\"contactPersonName\":\"联系人一\",\"gender\":\"1\",\"position\":\"职位一\",\"createTime\":\"2023-04-23 15:52:03\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 15:52:03\"},{\"customerId\":\"4CB2B2525AA440D09C526DBDF4F5FFE5\",\"contactPersonId\":\"C2AB5420E7F14204AF35C4194781959E\",\"contactPersonName\":\"高\",\"gender\":\"1\",\"position\":\"测试\",\"createTime\":\"2023-04-23 13:30:38\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 13:30:38\"},{\"customerId\":\"3xTw9$jvp7VqdKC2cv4fNS\",\"contactPersonId\":\"C650848328BF4C0C8A6DDB099D53B11F\",\"contactPersonName\":\"站撒氨基酸的啦\",\"gender\":\"1\",\"position\":\"nkj你好职位（总监）\",\"createTime\":\"2023-04-23 17:42:21\",\"creator\":\"16525\",\"updateTime\":\"2023-04-23 17:42:21\"},{\"customerId\":\"900B0460C04A4C84A1960A5D1A328650\",\"contactPersonId\":\"C9C4EAE32E7F47529B8B2B444AFE24A1\",\"contactPersonName\":\"联系人姓名联系人姓名\",\"gender\":\"3\",\"position\":\"以为职位hfb()\",\"createTime\":\"2023-04-23 10:38:10\",\"creator\":\"15077\",\"updateTime\":\"2023-04-23 10:38:10\"},{\"customerId\":\"760213088291\",\"contactPersonId\":\"CA2A175FBAC14B51B2AF28F1CFD5A80C\",\"contactPersonName\":\"王雨莎\",\"createTime\":\"2023-04-20 16:08:43\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-20 16:08:43\",\"operator\":\"96dc187f54a64e89b506b160c8b43531\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"D36AF0F9EC1A4DEBA9F6AEFFA6738819\",\"contactPersonName\":\"添加\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 13:56:17\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 13:56:17\"},{\"customerId\":\"DDA2DC7435FD4EFCB0B48C499A084F30\",\"contactPersonId\":\"D56BF4FF648A42AC884F4C59E0775BE7\",\"contactPersonName\":\"张三三\",\"gender\":\"2\",\"position\":\"总监\",\"createTime\":\"2023-04-23 11:31:30\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 11:32:00\"},{\"customerId\":\"A2DBEEBFE7404B74A5BE8F6817715E85\",\"contactPersonId\":\"D5BA57914B5E45F9B689E14E44A66CB4\",\"contactPersonName\":\"试试\",\"gender\":\"1\",\"position\":\"试试\",\"createTime\":\"2023-04-23 13:44:24\",\"creator\":\"aa1b82df919842a4a83f3e9f96aae737\",\"updateTime\":\"2023-04-23 13:44:24\"},{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"DADFC02DD2F24F228AD2C99D500BCFDE\",\"contactPersonName\":\"添加\",\"gender\":\"3\",\"position\":\"职位\",\"createTime\":\"2023-04-21 13:56:39\",\"creator\":\"15077\",\"updateTime\":\"2023-04-21 13:56:39\"},{\"customerId\":\"FFFC7B4697C54EB08F8871896306F8C7\",\"contactPersonId\":\"DBF420003D5441E0A948207AE6F41BAD\",\"contactPersonName\":\"张三三三\",\"gender\":\"2\",\"position\":\"总监\",\"createTime\":\"2023-04-21 14:40:50\",\"creator\":\"37928\",\"updateTime\":\"2023-04-21 14:40:50\"},{\"customerId\":\"E02A6D7DC7764956AE75BAB6591B6D81\",\"contactPersonId\":\"DDE088041B64448FA90A9B9499CFC19D\",\"contactPersonName\":\"这种东西\",\"gender\":\"1\",\"position\":\"在乎我们\",\"createTime\":\"2023-04-23 16:00:25\",\"creator\":\"15077\",\"updateTime\":\"2023-04-23 16:00:25\"},{\"customerId\":\"1422677\",\"contactPersonId\":\"EFECF2F4B91542D0A5CC38F44172F6CC\",\"contactPersonName\":\"跟进新增\",\"createTime\":\"2023-04-23 11:20:42\",\"creator\":\"96dc187f54a64e89b506b160c8b43531\",\"updateTime\":\"2023-04-23 11:20:42\",\"operator\":\"96dc187f54a64e89b506b160c8b43531\"},{\"customerId\":\"5CC1C317935D4A0BADF778A7BCDD63F1\",\"contactPersonId\":\"F30154DC37CF4B84A48EEB22E4150491\",\"contactPersonName\":\"啦啦啦\",\"gender\":\"2\",\"position\":\"这是这个\",\"createTime\":\"2023-04-23 15:55:42\",\"creator\":\"15077\",\"updateTime\":\"2023-04-23 15:55:42\"}]";
        jsonArray = JSON.parseArray(params);
        jsonArray.forEach(jsonObject -> {
            syncIncrementDataDubbo.sync(2, JSON.toJSONString(jsonObject));
        });
        org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:test.json");
        InputStream inputStream = resource.getInputStream();
        params = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        jsonArray = JSON.parseArray(params);
        jsonArray.forEach(jsonObject -> {
            syncIncrementDataDubbo.sync(3, JSON.toJSONString(jsonObject));
        });
    }

    @Test
    void testPositionMappingFile() {
        log.info(String.valueOf(ServiceConstant.POSITION_MAPPING.size()));
    }

    @Test
    void testBigData() {
        String params = "{\"customerId\":\"A5E53189BDA54C0B9F4015656F969C4B\",\"contactPersonId\":\"56AD4A71C58243C0B739616B8DC54A77\",\"contactPersonName\":\"会员中心\",\"sourceTag\":\"ce5\",\"createTime\":\"2023-05-04 13:28:27\",\"updateTime\":\"2023-05-04 13:28:27\"}";
        log.info(JSON.toJSONString(syncIncrementDataDubbo.sync(2, params)));
    }

    @Test
    void getAreaAllData() {
//        SignData signData = new SignData();
//        signData.setSourceKey("scrm");
//        signData.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
//        log.info(JSON.toJSONString(areaDubbo.getAllData(signData)));
        OrderDefaultFlagDubboDto orderDefaultFlagDubboDto = new OrderDefaultFlagDubboDto();
        orderDefaultFlagDubboDto.setSourceKey("scrm");
        orderDefaultFlagDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        orderDefaultFlagDubboDto.setContactPersonId("1032E7349F714098807FFFDDBD11D0DE");
        orderDefaultFlagDubboDto.setPhone("");
        orderDefaultFlagDubboDto.setEmail("");
        orderDefaultFlagDubboDto.setOperator("");
        log.info(JSON.toJSONString(contactPersonDubbo.addDefaultFlag(orderDefaultFlagDubboDto)));
    }

    @Test
    void testGetContactPersonData() {
        //获取客户下联系人数据
        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        customerDetailDubboDto.setSourceKey("scrm");
        customerDetailDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        customerDetailDubboDto.setCustomerId("1KHWN2ZaJ0gqpw7wuc$hXE");
        DubboResult<List<ContactPersonDataDubboView>> contactPersonDubboCustomerData = contactPersonDubbo.getCustomerData(customerDetailDubboDto);
        //获取联系人数据（根据联系人ID）
        ContactPersonDubboDto contactPersonDubboDto = new ContactPersonDubboDto();
        contactPersonDubboDto.setSourceKey("scrm");
        contactPersonDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        contactPersonDubboDto.setContactPersonId("1659039320993955853");
        DubboResult<List<ContactPersonDataDubboView>> contactPersonDubboData = contactPersonDubbo.getData(contactPersonDubboDto);
        //获取联系人数据（根据客户ID和手机号）
        ContactPersonDubboDto contactPersonDubboDto1 = new ContactPersonDubboDto();
        contactPersonDubboDto1.setSourceKey("scrm");
        contactPersonDubboDto1.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        contactPersonDubboDto1.setCustomerId("1KHWN2ZaJ0gqpw7wuc$hXE");
        contactPersonDubboDto1.setPhone("15201211665");
        DubboResult<List<ContactPersonDataDubboView>> contactPersonDubboData1 = contactPersonDubbo.getData(contactPersonDubboDto1);
        //打印结果
        log.info(JSON.toJSONString(contactPersonDubboCustomerData));
        log.info(JSON.toJSONString(contactPersonDubboData));
        log.info(JSON.toJSONString(contactPersonDubboData1));
    }

    @Test
    void testAddContactPersonData() {
        ContactPersonAddDubboDto contactPersonAddDubboDto = new ContactPersonAddDubboDto();
        contactPersonAddDubboDto.setSourceKey("scrm");
        contactPersonAddDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        contactPersonAddDubboDto.setContactPersonName("test-name1");
        contactPersonAddDubboDto.setGender(1);
        contactPersonAddDubboDto.setDepartment("财务");
        contactPersonAddDubboDto.setPosition("出纳");
        contactPersonAddDubboDto.setCertificatesType(1);
        contactPersonAddDubboDto.setCertificatesNumber("11122282828282828");
        contactPersonAddDubboDto.setProvinceCode("110000");
        contactPersonAddDubboDto.setCityCode("110100");
        contactPersonAddDubboDto.setDistrictCode("110101");
        contactPersonAddDubboDto.setAddress("数码庄园");
        contactPersonAddDubboDto.setRemarks("测试联系人");
        contactPersonAddDubboDto.setPhone("15911053513");
        contactPersonAddDubboDto.setEmail("<EMAIL>");
        contactPersonAddDubboDto.setWechat("1333333333311");
        contactPersonAddDubboDto.setWecome("1333333333311");
        contactPersonAddDubboDto.setQq("1333333333311");
        contactPersonAddDubboDto.setTelephone("010-1333333333311");
        contactPersonAddDubboDto.setOperator("84612");
        log.info(JSON.toJSONString(contactPersonDubbo.add(contactPersonAddDubboDto)));
    }

    @Test
    void testUpdateContactPersonData() {
        String param = "{\"certificatesNumber\":\"031235666这是证件\",\"certificatesType\":0,\"contactPersonId\":\"1663473450364174401\",\"contactPersonName\":\"陈先生03\",\"operator\":\"CEM10010508\",\"sourceKey\":\"record\",\"sourceSecret\":\"828743afea304732a8b3355455d99258\",\"telephone\":\"01012345610:01012345612\"}";
        ContactPersonUpdateDubboDto contactPersonUpdateDubboDto = JSON.parseObject(param, ContactPersonUpdateDubboDto.class);
        log.info(JSON.toJSONString(contactPersonDubbo.update(contactPersonUpdateDubboDto)));
    }

    @Test
    void testAddCustomerData() {
        CustomerAddDubboDto customerAddDubboDto = new CustomerAddDubboDto();
        customerAddDubboDto.setSourceKey("scrm");
        customerAddDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        customerAddDubboDto.setCustomerId("1658738889659039806");
        customerAddDubboDto.setCustomerName("王志军测试添加客户");
        customerAddDubboDto.setProvinceCode("110000");
        customerAddDubboDto.setCityCode("110100");
        customerAddDubboDto.setDistrictCode("110101");
        customerAddDubboDto.setOperator("84612");

        customerAddDubboDto.setOpenStartTime(new Date());
        customerAddDubboDto.setOpenEndTime(new Date());
        customerAddDubboDto.setCreator("72148");
        customerAddDubboDto.setCertificateType(1);
        customerAddDubboDto.setCertificateCode("1111111111111");
        customerAddDubboDto.setApproveDate(new Date());
        customerAddDubboDto.setCreateWay(1);
        customerAddDubboDto.setCustomerType(1);
        log.info(JSON.toJSONString(customerDubbo.add(customerAddDubboDto)));
    }

    @Test
    void testUpdateCustomerData() {
        CustomerUpdateDubboDto customerAddDubboDto = new CustomerUpdateDubboDto();
        customerAddDubboDto.setSourceKey("scrm");
        customerAddDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        customerAddDubboDto.setCustomerId("1661907521138364460");
        customerAddDubboDto.setCustomerName("王志军测试添加客1");
        customerAddDubboDto.setProvinceCode("110000");
        customerAddDubboDto.setCityCode("110100");
        customerAddDubboDto.setDistrictCode("110101");
        customerAddDubboDto.setOperator("84612");

        customerAddDubboDto.setOpenStartTime(new Date());
        customerAddDubboDto.setOpenEndTime(new Date());
        customerAddDubboDto.setCreator("72148");
        customerAddDubboDto.setCertificateType(1);
        customerAddDubboDto.setCertificateCode("2222222222");
        customerAddDubboDto.setApproveDate(new Date());
        customerAddDubboDto.setCreateWay(2);
        customerAddDubboDto.setCustomerType(1);
        log.info(JSON.toJSONString(customerDubbo.update(customerAddDubboDto)));
    }

    @Test
    void findByCondition() {
        CustomerConditionDubboDto customerConditionDubboDto = new CustomerConditionDubboDto();
        //customerConditionDubboDto.setMobile("13903862398");
        customerConditionDubboDto.setSourceKey("scrm");
        customerConditionDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        DubboResult<List<CustomerDubboView>> byCondition = customerDubbo.findByCondition(customerConditionDubboDto);
        System.out.println(JSON.toJSONString(byCondition));
    }
}
