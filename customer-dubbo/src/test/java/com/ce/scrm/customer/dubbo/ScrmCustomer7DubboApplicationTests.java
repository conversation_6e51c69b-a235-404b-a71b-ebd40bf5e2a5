package com.ce.scrm.customer.dubbo;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.ScrmCustomerDubboApplication;
import com.ce.scrm.customer.dubbo.api7.IContactPersonDubbo;
import com.ce.scrm.customer.dubbo.api7.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.entity7.base.SignData;
import com.ce.scrm.customer.dubbo.entity7.dto.*;
import com.ce.scrm.customer.dubbo.entity7.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity7.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity7.view.AreaDubboView;
import com.ce.scrm.customer.dubbo.entity7.view.ContactPersonAddDubboView;
import com.ce.scrm.customer.dubbo.entity7.view.ContactPersonDataDubboView;
import com.ce.scrm.customer.dubbo.entity7.view.CustomerDubboView;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@SpringBootTest(classes = ScrmCustomerDubboApplication.class)
class ScrmCustomer7DubboApplicationTests {
    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private IContactPersonDubbo contactPersonDubbo;
    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private ICustomerDubbo customerDubbo;

    @Test
    void testGetContactPersonData() {
        //获取客户下联系人数据
        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        customerDetailDubboDto.setSourceKey("scrm");
//        customerDetailDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        customerDetailDubboDto.setCustomerId("1KHWN2ZaJ0gqpw7wuc$hXE");
        DubboResult<List<ContactPersonDataDubboView>> contactPersonDubboCustomerData = contactPersonDubbo.getCustomerData(customerDetailDubboDto);
        //获取联系人数据（根据联系人ID）
        ContactPersonDubboDto contactPersonDubboDto = new ContactPersonDubboDto();
        contactPersonDubboDto.setSourceKey("scrm");
//        contactPersonDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        contactPersonDubboDto.setContactPersonId("1659039320993955853");
        DubboResult<List<ContactPersonDataDubboView>> contactPersonDubboData = contactPersonDubbo.getData(contactPersonDubboDto);
        //获取联系人数据（根据客户ID和手机号）
        ContactPersonDubboDto contactPersonDubboDto1 = new ContactPersonDubboDto();
        contactPersonDubboDto1.setSourceKey("scrm");
//        contactPersonDubboDto1.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        contactPersonDubboDto1.setCustomerId("1KHWN2ZaJ0gqpw7wuc$hXE");
        contactPersonDubboDto1.setPhone("15201211665");
        DubboResult<List<ContactPersonDataDubboView>> contactPersonDubboData1 = contactPersonDubbo.getData(contactPersonDubboDto1);
        //获取地区数据
        SignData signData = new SignData();
        signData.setSourceKey("scrm");
//        signData.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        DubboResult<List<AreaDubboView>> allAreaData = contactPersonDubbo.getAllAreaData(signData);
        //获取客户详情数据
        CustomerDetailDubboDto customerDetailDubboDto1 = new CustomerDetailDubboDto();
        customerDetailDubboDto1.setSourceKey("scrm");
//        customerDetailDubboDto1.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        customerDetailDubboDto1.setCustomerId("1KHWN2ZaJ0gqpw7wuc$hXE");
        DubboResult<CustomerDubboView> customerDubboViewDubboResult = customerDubbo.detail(customerDetailDubboDto1);
        //获取客户分页列表数据
        CustomerPageDubboDto customerPageDubboDto = new CustomerPageDubboDto();
        customerPageDubboDto.setSourceKey("scrm");
        customerPageDubboDto.setPageNum(1);
        customerPageDubboDto.setPageSize(2);
//        customerPageDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        DubboResult<DubboPageInfo<CustomerDubboView>> dubboPageInfoDubboResult = customerDubbo.pageList(customerPageDubboDto);
        //打印结果
        log.info(JSON.toJSONString(contactPersonDubboCustomerData));
        log.info(JSON.toJSONString(contactPersonDubboData));
        log.info(JSON.toJSONString(contactPersonDubboData1));
        log.info(JSON.toJSONString(allAreaData));
        log.info(JSON.toJSONString(customerDubboViewDubboResult));
        log.info(JSON.toJSONString(dubboPageInfoDubboResult));
    }

    @Test
    void testAddContactPersonData() {
        ContactPersonAddDubboDto contactPersonAddDubboDto = new ContactPersonAddDubboDto();
        contactPersonAddDubboDto.setSourceKey("member");
        contactPersonAddDubboDto.setSourceSecret("31b9763b659c49489d34987678561a64");
        contactPersonAddDubboDto.setCustomerId("1KHWN2ZaJ0gqpw7wuc$hXE");
        contactPersonAddDubboDto.setContactPersonName("test-name1");
        contactPersonAddDubboDto.setGender(1);
        contactPersonAddDubboDto.setDepartment("财务");
        contactPersonAddDubboDto.setPosition("出纳");
        contactPersonAddDubboDto.setCertificatesType(1);
        contactPersonAddDubboDto.setCertificatesNumber("11122282828282828");
        contactPersonAddDubboDto.setProvinceCode("110000");
        contactPersonAddDubboDto.setCityCode("110100");
        contactPersonAddDubboDto.setDistrictCode("110101");
        contactPersonAddDubboDto.setAddress("数码庄园");
        contactPersonAddDubboDto.setRemarks("测试联系人");
        contactPersonAddDubboDto.setPhone("15911053513");
        contactPersonAddDubboDto.setEmail("<EMAIL>");
        contactPersonAddDubboDto.setWechat("1333333333311");
        contactPersonAddDubboDto.setWecome("1333333333311");
        contactPersonAddDubboDto.setQq("1333333333311");
        contactPersonAddDubboDto.setTelephone("010-1333333333311");
//        contactPersonAddDubboDto.setOperator("84612");
        log.info(JSON.toJSONString(contactPersonDubbo.add(contactPersonAddDubboDto)));
    }

    @Test
    void testUpdateContactPersonData() {
        ContactPersonUpdateDubboDto contactPersonUpdateDubboDto = new ContactPersonUpdateDubboDto();
        contactPersonUpdateDubboDto.setSourceKey("member");
        contactPersonUpdateDubboDto.setSourceSecret("31b9763b659c49489d34987678561a64");
        contactPersonUpdateDubboDto.setContactPersonId("1663933418804621318");
        contactPersonUpdateDubboDto.setContactPersonName("test-name2");
        contactPersonUpdateDubboDto.setGender(2);
        contactPersonUpdateDubboDto.setDepartment("财务1");
        contactPersonUpdateDubboDto.setPosition("出纳1");
        contactPersonUpdateDubboDto.setCertificatesType(1);
        contactPersonUpdateDubboDto.setCertificatesNumber("111222828282828281");
        contactPersonUpdateDubboDto.setProvinceCode("110000");
        contactPersonUpdateDubboDto.setCityCode("110100");
        contactPersonUpdateDubboDto.setDistrictCode("110101");
        contactPersonUpdateDubboDto.setAddress("数码庄园1");
        contactPersonUpdateDubboDto.setRemarks("测试联系人1");
        contactPersonUpdateDubboDto.setPhone("13333333333:133333333334");
        contactPersonUpdateDubboDto.setEmail(":<EMAIL>");
        contactPersonUpdateDubboDto.setWechat(":13333333333");
        contactPersonUpdateDubboDto.setWecome(":13333333333");
        contactPersonUpdateDubboDto.setQq(":13333333333");
        contactPersonUpdateDubboDto.setTelephone("010-1333333333311:010-333333333");
//        contactPersonUpdateDubboDto.setOperator("84612");
        log.info(JSON.toJSONString(contactPersonDubbo.update(contactPersonUpdateDubboDto)));
    }

    @Test
    void testAddOrderDefaultFlag() {
        OrderDefaultFlagDubboDto orderDefaultFlagDubboDto = new OrderDefaultFlagDubboDto();
        orderDefaultFlagDubboDto.setSourceKey("scrm");
        orderDefaultFlagDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        orderDefaultFlagDubboDto.setContactPersonId("1663933418804621318");
        orderDefaultFlagDubboDto.setPhone("133333333334");
        orderDefaultFlagDubboDto.setEmail("<EMAIL>");
        orderDefaultFlagDubboDto.setOperator("84612");
        log.info(JSON.toJSONString(contactPersonDubbo.addDefaultFlag(orderDefaultFlagDubboDto)));
    }

    @Test
    void testUpdateCustomer() {
        CustomerUpdateDubboDto customerUpdateDubboDto = new CustomerUpdateDubboDto();
        customerUpdateDubboDto.setSourceKey("scrm");
//        customerUpdateDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        customerUpdateDubboDto.setCustomerId("1663940347497033731");
        customerUpdateDubboDto.setCustomerName("王志军测试添加客1");
        customerUpdateDubboDto.setProvinceCode("110000");
        customerUpdateDubboDto.setCityCode("110100");
        customerUpdateDubboDto.setDistrictCode("110101");
        customerUpdateDubboDto.setOperator("84612");

        customerUpdateDubboDto.setOpenStartTime(new Date());
        customerUpdateDubboDto.setOpenEndTime(new Date());
        customerUpdateDubboDto.setCreator("72148");
        customerUpdateDubboDto.setCertificateType(1);
        customerUpdateDubboDto.setCertificateCode("2222222222");
        customerUpdateDubboDto.setApproveDate(new Date());
        customerUpdateDubboDto.setCreateWay(2);
        customerUpdateDubboDto.setCustomerType(1);
        log.info(JSON.toJSONString(customerDubbo.update(customerUpdateDubboDto)));
    }

    @Test
    void testAddCustomer() {
        CustomerAddDubboDto customerAddDubboDto = new CustomerAddDubboDto();
        customerAddDubboDto.setSourceKey("scrm");
        customerAddDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        customerAddDubboDto.setCustomerId("1658738889659039806");
//        customerAddDubboDto.setCustomerName("王志军测试添加客户");
        customerAddDubboDto.setProvinceCode("110000");
        customerAddDubboDto.setCityCode("110100");
        customerAddDubboDto.setDistrictCode("110101");
        customerAddDubboDto.setOperator("84612");
        customerAddDubboDto.setOpenStartTime(new Date());
        customerAddDubboDto.setOpenEndTime(new Date());
        customerAddDubboDto.setCreator("72148");
        customerAddDubboDto.setCertificateType(1);
        customerAddDubboDto.setCertificateCode("1111111111111");
        customerAddDubboDto.setApproveDate(new Date());
        customerAddDubboDto.setCreateWay(1);
        customerAddDubboDto.setCustomerType(1);
        log.info(JSON.toJSONString(customerDubbo.add(customerAddDubboDto)));
    }

    @Test
    void testCustomerAndContactPersonData() {
        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        customerDetailDubboDto.setSourceKey("scrm");
        customerDetailDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        customerDetailDubboDto.setCustomerId("0NM1YUAf57IaG@vMY91md$");
        log.info(JSON.toJSONString(contactPersonDubbo.customerContactDetail(customerDetailDubboDto)));
    }

    @Test
    void testContactPersonBindUnionId() {
        ContactPersonBindUnionIdDubboDto contactPersonBindUnionIdDubboDto = new ContactPersonBindUnionIdDubboDto();
        contactPersonBindUnionIdDubboDto.setSourceKey("scrm");
        contactPersonBindUnionIdDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        contactPersonBindUnionIdDubboDto.setContactPersonId("1661669229495058490");
        contactPersonBindUnionIdDubboDto.setUnionId("sdijfsdi3");
        contactPersonBindUnionIdDubboDto.setOperator("localTest");
        log.info(JSON.toJSONString(contactPersonDubbo.bindUnionId(contactPersonBindUnionIdDubboDto)));
    }

    @Test
    void testContactPersonUnbindUnionId() {
        Arrays.asList("86683ba6df4649a68106c850c64b9c89", "d12f3d394ee241ef9a71527e21ce0bbc", "1661669229495058490").forEach(s -> {
            ContactPersonUnbindUnionIdDubboDto contactPersonUnbindUnionIdDubboDto = new ContactPersonUnbindUnionIdDubboDto();
            contactPersonUnbindUnionIdDubboDto.setSourceKey("scrm");
            contactPersonUnbindUnionIdDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
            contactPersonUnbindUnionIdDubboDto.setContactPersonId(s);
            contactPersonUnbindUnionIdDubboDto.setOperator("localTest1");
            log.info(JSON.toJSONString(contactPersonDubbo.unbindUnionId(contactPersonUnbindUnionIdDubboDto)));
        });
    }

    @Test
    void testGetByUnionId() {
        BindUnionIdQueryDubboDto bindUnionIdQueryDubboDto = new BindUnionIdQueryDubboDto();
        bindUnionIdQueryDubboDto.setSourceKey("scrm");
        bindUnionIdQueryDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        bindUnionIdQueryDubboDto.setUnionIdList(Arrays.asList("sdijfsdi1", "sdijfsdi2", "sdijfsdi3"));
        log.info(JSON.toJSONString(contactPersonDubbo.getContactPersonDataByUnionId(bindUnionIdQueryDubboDto)));
    }


    @Test
    void testUpdatePhoneFlag() {
        ContactPersonUpdateDubboDto contactPersonUpdateDubboDto = new ContactPersonUpdateDubboDto();
        contactPersonUpdateDubboDto.setSourceKey("scrm");
        contactPersonUpdateDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        contactPersonUpdateDubboDto.setContactPersonId("1731510781754736672");
        contactPersonUpdateDubboDto.setContactInfoId("1731510781754736673");
        contactPersonUpdateDubboDto.setOperator("86609");
        contactPersonUpdateDubboDto.setPhoneVerifiedFlag(1);
        DubboResult<Boolean> booleanDubboResult = contactPersonDubbo.updatePhoneVerifiedFlag(contactPersonUpdateDubboDto);
        log.info("=============="+JSON.toJSONString(booleanDubboResult));
    }

    @Test
    void testAddPhoneFlag() {
        ContactPersonAddDubboDto contactPersonUpdateDubboDto = new ContactPersonAddDubboDto();
        contactPersonUpdateDubboDto.setSourceKey("scrm");
        contactPersonUpdateDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        contactPersonUpdateDubboDto.setOperator("86609");
        contactPersonUpdateDubboDto.setPhoneVerifiedFlag(1);
        contactPersonUpdateDubboDto.setContactPersonName("王某人");
        contactPersonUpdateDubboDto.setCustomerId("1446113");
        contactPersonUpdateDubboDto.setEmail("");
        contactPersonUpdateDubboDto.setLegalPersonFlag(1);
        contactPersonUpdateDubboDto.setPhone("1892989329");
        DubboResult<ContactPersonAddDubboView> add = contactPersonDubbo.add(contactPersonUpdateDubboDto);
        log.info("=============="+JSON.toJSONString(add));
    }
}
