<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">


    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ce.scrm.customer</groupId>
    <artifactId>customer-dubbo-api</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>customer-dubbo-api</name>
    <description>customer-dubbo-api</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <lombok.version>1.18.24</lombok.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>


    <distributionManagement>
        <!-- SNAPSHOT组件发布到私服 mvn deploy -->
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://nexus.300.cn:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
        <!-- RELEASE组件发布到私服 mvn deploy -->
        <repository>
            <id>releases</id>
            <url>http://nexus.300.cn:8081/nexus/content/repositories/releases</url>
        </repository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>private-repos</id>
            <url>http://test-omo.aiyouyi.cn/nexus/repository/maven-public/</url>
        </repository>
        <repository>
            <id>snapshots</id>
            <url>http://nexus.300.cn:8081/nexus/content/repositories/snapshots</url>
        </repository>
        <repository>
            <id>releases</id>
            <url>http://nexus.300.cn:8081/nexus/content/repositories/releases</url>
        </repository>
    </repositories>
    <!--上传源码-->
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
