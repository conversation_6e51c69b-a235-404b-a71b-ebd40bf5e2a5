package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;

import java.io.Serializable;

/**
 * 联系人绑定unionId参数
 * <AUTHOR>
 * @date 2023/5/23 09:41
 * @version 1.0.0
 **/
public class ContactPersonBindUnionIdDubboDto extends SignData implements Serializable {
    /**
     * 联系人ID
     */
    private String contactPersonId;

    /**
     * 绑定类型：0、无，1、微信，2、企业微信
     */
    private Integer bindType;

    /**
     * 微信unionId
     */
    private String unionId;

    /**
     * 微信昵称
     */
    private String wechatNickName;

    /**
     * 操作人
     */
    private String operator;

    public String getContactPersonId() {
        return contactPersonId;
    }

    public void setContactPersonId(String contactPersonId) {
        this.contactPersonId = contactPersonId;
    }

    public Integer getBindType() {
        return bindType;
    }

    public void setBindType(Integer bindType) {
        this.bindType = bindType;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getWechatNickName() {
        return wechatNickName;
    }

    public void setWechatNickName(String wechatNickName) {
        this.wechatNickName = wechatNickName;
    }
}
