package com.ce.scrm.customer.dubbo.entity.view.abm;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 潜客营销规则配置表
 * @TableName potential_customer_marketing_rules
 */
@Data
public class PotentialCustomerMarketingRulesDetailDubboView implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 潜客leads等级 A+、A、B+、B、C、D
     */
    private String level;

    /**
     * 类别
     */
    private String type;

    /**
     * 潜客code
     */
    private String code;

    /**
     * leads来源
     */
    private String source;

    /**
     * leads来源说明
     */
    private String sourceDesc;

    /**
     * leads摘要（四要素）
     */
    private Integer leadsSummaries;

    /**
     * 更多详情
     */
    private String moreInfo;

    /**
     * 状态 0-暂无 1-新增 2-已有
     */
    private Integer state;

    /**
     * 标签分类id（D类标签对应的分类id）
     */
    private Long tagCategoryId;

    /**
     * 创建人ID
     */
    private String createBy;

    /**
     * 修改人ID
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;
}