package com.ce.scrm.customer.dubbo.entity.dto.abm;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description ABM 营销活动创建，分发SDR记录
 * <AUTHOR>
 * @Date 2025-07-09 14:45
 */
@Data
public class CustomerDistributeSdrRecordDubboDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 活动ID
	 */
	private Long activityId;

	/**
	 * 员工id
	 */
	private String empId;

	/**
	 * 员工姓名
	 */
	private String empName;

	/**
	 * 职位 1-SDR 2-CC
	 */
	private Integer positionType;

	/**
	 * 客户id
	 */
	private String customerId;
}
