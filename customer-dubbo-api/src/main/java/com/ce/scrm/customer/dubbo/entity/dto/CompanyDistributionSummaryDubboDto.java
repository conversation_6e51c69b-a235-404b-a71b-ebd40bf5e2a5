package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;

import java.io.Serializable;

/**
 * Description: 按地区统计 tag_menhu=1（【销管口径】门户客户）的客户数
 * @author: JiuDD
 * date: 2025/6/27 11:00
 */
public class CompanyDistributionSummaryDubboDto extends SignData implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * level 统计颗粒度:1=全国;2=省;3=市
     */
    private Integer level;

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
}