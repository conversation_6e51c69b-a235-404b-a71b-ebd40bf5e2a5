package com.ce.scrm.customer.dubbo.entity.response;

/**
 * dubbo返回码枚举
 * <AUTHOR>
 * @date 2023/4/6 13:55
 * @version 1.0.0
 **/
public enum DubboCodeMessageEnum {
    /**
     * 成功返回
     */
    REQUEST_SUCCESS("200", "请求成功"),

    /**
     * 服务器内部异常
     */
    SERVER_INTERNAL_EXCEPTION("10001", "网络超时，请稍后重试哦～"),

    /**
     * 来源渠道不存在
     */
    SOURCE_CHANNEL_NOT_EXIST("10002", "来源渠道异常，调用无效"),

    /**
     * 签名验证失败
     */
    SIGN_CHECK_FAIL("10003", "签名验证失败"),

    /**
     * 请求验证超时
     */
    SIGN_CHECK_TIMEOUT("10004", "当前请求验证超时"),

    /**
     * 返回结果为空
     */
    RETURN_NULL("10005", "返回结果为空"),

    /**
     * 操作失败
     */
    OPER_FAIL("10006", "操作失败"),

    /**
     * 无效参数
     */
    INVALID_PARAM ("10007", "无效参数"),

    /**
     * 出现异常
     */
    EXCEPTION("10008", "出现异常"),

    /**
     * 联系人手机号校验失败
     */
    CONTACT_PERSON_PHONE_CHECK_FAIL("11000", "当前手机号不能添加成为联系人"),

    /**
     * 联系人重复添加
     */
    CONTACT_PERSON_NOT_REPEAT_ADD("11001", "联系人已存在"),

    /**
     * 客户名称格式不正确
     */
    CUSTOMER_NAME_FORMAT_ERROR("11002", "客户名称格式不正确"),


    /**
     * 客户重复添加
     */
    CUSTOMER_NOT_REPEAT_ADD("11003", "客户已存在"),


    /**
     * 联系人unionId已绑定
     */
    CONTACT_PERSON_UNION_ID_ALREADY_BIND("11004", "联系人unionId已绑定，不能重复绑定"),


    /**
     * 当前unionId已绑定其他联系人
     */
    UNION_ID_ALREADY_USED("11005", "当前unionId已绑定其他联系人"),


    /**
     * 联系人unionId已解绑，无需重复解绑
     */
    CONTACT_PERSON_UNION_ID_ALREADY_UNBIND("11006", "联系人unionId已解绑，无需重复解绑"),


    /**
     * 解绑类型和联系人的绑定类型不一致
     */
    CONTACT_PERSON_UNBIND_TYPE_EXCEPTION("11007", "解绑类型和联系人的绑定类型不一致"),

    /**
     * 当前已经是正式签单联系人，不可修改
     */
    CONTACT_PERSON_ALREADY_FORMAL_SIGN_CONTACT("11008", "当前已经是正式签单联系人，不可修改"),

    ;

    private final String code;
    private final String message;

    DubboCodeMessageEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
