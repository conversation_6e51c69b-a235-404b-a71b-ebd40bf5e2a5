package com.ce.scrm.customer.dubbo.entity.response;

import java.io.Serializable;

/**
 * 返回包装体
 * <AUTHOR>
 * @date 2023/4/6 13:55
 * @version 1.0.0
 **/
public class DubboResult<T> implements Serializable {
    private static final long serialVersionUID = 6485342072648694248L;

    private String msg;
    private String code;
    private T data;

    private DubboResult(T data) {
        this.code = DubboCodeMessageEnum.REQUEST_SUCCESS.getCode();
        this.msg = DubboCodeMessageEnum.REQUEST_SUCCESS.getMessage();
        this.data = data;
    }

    private DubboResult(T data, String msg) {
        this.code = DubboCodeMessageEnum.REQUEST_SUCCESS.getCode();
        this.msg = msg;
        this.data = data;
    }

    private DubboResult(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private DubboResult(DubboCodeMessageEnum cm) {
        if (cm != null) {
            this.code = cm.getCode();
            this.msg = cm.getMessage();
        }
    }

    private DubboResult(T t, DubboCodeMessageEnum cm) {
        if (cm != null) {
            this.code = cm.getCode();
            this.msg = cm.getMessage();
        }
        data = t;
    }

    public static <T> DubboResult<T> success(T data) {
        return new DubboResult<>(data);
    }

    public static <T> DubboResult<T> success(T data, String msg) {
        return new DubboResult<>(data, msg);
    }

    public static <T> DubboResult<T> success() {
        return success(null);
    }

    public static <T> DubboResult<T> error(DubboCodeMessageEnum cm) {
        return new DubboResult<>(cm);
    }

    public static <T> DubboResult<T> error(DubboCodeMessageEnum cm, String msg) {
        return new DubboResult<>(cm.getCode(), msg);
    }

    public static <T> DubboResult<T> error(String code, String msg) {
        return new DubboResult<>(code, msg);
    }

    public static <T> DubboResult<T> error(T data, DubboCodeMessageEnum cm) {
        return new DubboResult<>(data, cm);
    }

    public static <T> DubboResult<T> error(DubboResult<?> dubboResult) {
        return new DubboResult<>(dubboResult.getCode(), dubboResult.getMsg());
    }

    public static <T> T parseResult(DubboResult<T> dubboResult) {
        if (dubboResult.checkSuccess() && dubboResult.getData() != null) {
            return dubboResult.getData();
        }
        return null;
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getMsg() {
        return this.msg;
    }

    public String getCode() {
        return this.code;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Boolean checkSuccess() {
        return DubboCodeMessageEnum.REQUEST_SUCCESS.getCode().equals(this.code);
    }
}
