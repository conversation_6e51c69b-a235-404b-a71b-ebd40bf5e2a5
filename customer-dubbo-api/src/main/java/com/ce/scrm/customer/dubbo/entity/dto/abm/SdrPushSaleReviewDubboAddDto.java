package com.ce.scrm.customer.dubbo.entity.dto.abm;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * sdr推送销售审核表
 */
@Data
public class SdrPushSaleReviewDubboAddDto extends SignData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    @NotBlank(message = "客户id不能为空")
    private String customerId;

    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空")
    private String customerName;

    /**
     * 联系人id
     */
    @NotBlank(message = "联系人不能为空")
    private String contactPersonId;

    /**
     * 省份
     */
    @NotBlank(message = "省份不能为空")
    private String province;

    /**
     * 城市
     */
    @NotBlank(message = "城市不能为空")
    private String city;

    /**
     * 地址
     */
    private String address;

    /**
     * 跟进详情
     */
    private String followDetails;

    /**
     * 创建者
     */
    @NotBlank(message = "创建人不能为空")
    private String createdId;

    /**
     * 创建时间
     */
    private Date createTime;

}