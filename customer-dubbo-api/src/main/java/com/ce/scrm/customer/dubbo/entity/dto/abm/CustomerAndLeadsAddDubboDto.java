package com.ce.scrm.customer.dubbo.entity.dto.abm;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 客户leads
 *
 * @TableName customer_leads
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerAndLeadsAddDubboDto extends SignData implements Serializable {

	/**
	 * 客户名
	 */
	private String customerName;

	/**
	 * 客户id
	 */
	private String customerId;

	private List<CustomerLeadsAddDubboDto> leads;


    private static final long serialVersionUID = 1L;
}