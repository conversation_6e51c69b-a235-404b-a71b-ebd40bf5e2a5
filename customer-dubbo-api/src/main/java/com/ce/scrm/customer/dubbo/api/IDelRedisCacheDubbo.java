package com.ce.scrm.customer.dubbo.api;

public interface IDelRedisCacheDubbo {
    /**
     * 删除客户缓存
     * @param customerId 客户id
     * <AUTHOR>
     * @date 2023/9/13 10:18
     */
    void delCustomerCacheById(String customerId);
    /**
     * 删除客户联系人缓存
     * @param customerId 客户id
     * <AUTHOR>
     * @date 2023/9/13 10:18
     */
    void delCustomerContactCacheById(String customerId);

    /**
     * 删除联系人缓存
     * @param contactPersonId 联系人id
     * <AUTHOR>
     * @date 2023/9/13 10:18
     */
    void delContactCacheById(String contactPersonId);
}
