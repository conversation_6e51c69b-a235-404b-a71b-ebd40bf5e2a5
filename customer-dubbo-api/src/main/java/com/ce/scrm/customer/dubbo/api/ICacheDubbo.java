package com.ce.scrm.customer.dubbo.api;

import java.util.List;
import java.util.Set;

/**
 * 缓存操作
 * <AUTHOR>
 * @date 2023/6/25 09:48
 * @version 1.0.0
 **/
public interface ICacheDubbo {

    /**
     * 获取单条缓存
     * @param key   缓存key
     * <AUTHOR>
     * @date 2023/6/25 09:50
     * @return java.lang.String
     **/
    String get(String key);

    /**
     * 删除单条缓存
     * @param key   缓存key
     * <AUTHOR>
     * @date 2023/6/25 09:54
     * @return java.lang.Boolean
     **/
    Boolean del(String key);

    /**
     * 模糊获取所有的key
     * @param prefix    查询条件
     * <AUTHOR>
     * @date 2023/6/25 09:51
     * @return java.util.Set<java.lang.String>
     **/
    Set<String> getKeys(String prefix);

    /**
     * 批量删除缓存
     * @param prefix    查询条件
     * <AUTHOR>
     * @date 2023/6/25 09:55
     * @return java.lang.Boolean
     **/
    Boolean delKeys(String prefix);

    /**
     * 获取缓存枚举类型
     * <AUTHOR>
     * @date 2023/8/21 10:30
     * @return java.util.List<java.lang.String>
     **/
    List<String> getCacheEnumType();

    /**
     * 获取缓存key
     * @param keyType   缓存指定类型
     * @param serviceId 业务ID
     * <AUTHOR>
     * @date 2023/8/21 10:37
     * @return java.lang.String
     **/
    String getKey(String keyType, String serviceId);
}