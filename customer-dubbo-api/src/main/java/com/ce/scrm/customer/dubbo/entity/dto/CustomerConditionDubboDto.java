package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;

import java.io.Serializable;
import java.util.Date;

/**
 * 添加客户数据dto
 * <AUTHOR>
 * @date 2023/4/12 16:48
 * @version 1.0.0
 */
public class CustomerConditionDubboDto extends SignData implements Serializable {

    /**
     * 客户联系方式
     */
     private String contactWay;

    /**
     * 客户联系方式类型
     */
    private Integer contactType;
    /**
     * 客户名称
     */
    private String customerName;

    public String getContactWay() {
        return contactWay;
    }

    public void setContactWay(String contactWay) {
        this.contactWay = contactWay;
    }

    public Integer getContactType() {
        return contactType;
    }

    public void setContactType(Integer contactType) {
        this.contactType = contactType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
}