package com.ce.scrm.customer.dubbo.entity.view;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 联系人数据
 * <AUTHOR>
 * @date 2023/4/12 17:37
 * @version 1.0.0
 */
public class ContactPersonDubboView implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 联系人id
     */
    private String contactPersonId;
    /**
     * 联系人姓名
     */
    private String contactPersonName;

    /**
     * 绑定类型：0、无，1、微信，2、企业微信
     */
    private Integer bindType;
    /**
     * 微信unionId
     */
    private String unionId;

    /**
     * unionId绑定时间
     */
    private LocalDateTime unionIdBindTime;
    /**
     * 性别：1、未知，2、男，3、女
     */
    private Integer gender;
    /**
     * 职位
     */
    private String position;

    /**
     * 职位名称
     */
    private String positionName;

    /**
     * 联系人来源key
     */
    private String sourceKey;
    /**
     * 联系人来源标签，逗号分隔
     */
    private String sourceTag;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区编码
     */
    private String districtCode;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 联系人部门
     */
    private String department;
    /**
     * 证件类型
     */
    private Integer certificatesType;
    /**
     * 证件号码
     */
    private String certificatesNumber;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 证件号码
     */
    private String remarks;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 会员号
     */
    private String memberCode;

    /**
     * 是否是法人 1是 0否
     */
    private Integer legalPersonFlag;

    /**
     * 签单联系人 0、不是，1、临时，2、正式
     */
    private Integer signingPersonFlag;

    /**
     * 签单联系人更新时间
     */
    private LocalDateTime signingPersonUpdate;


    /**
     * 联系方式列表
     */
    private List<ContactInfoDubboView> contactInfoList;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getContactPersonId() {
        return contactPersonId;
    }

    public void setContactPersonId(String contactPersonId) {
        this.contactPersonId = contactPersonId;
    }

    public String getContactPersonName() {
        return contactPersonName;
    }

    public void setContactPersonName(String contactPersonName) {
        this.contactPersonName = contactPersonName;
    }

    public Integer getBindType() {
        return bindType;
    }

    public void setBindType(Integer bindType) {
        this.bindType = bindType;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public LocalDateTime getUnionIdBindTime() {
        return unionIdBindTime;
    }

    public void setUnionIdBindTime(LocalDateTime unionIdBindTime) {
        this.unionIdBindTime = unionIdBindTime;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getSourceKey() {
        return sourceKey;
    }

    public void setSourceKey(String sourceKey) {
        this.sourceKey = sourceKey;
    }

    public String getSourceTag() {
        return sourceTag;
    }

    public void setSourceTag(String sourceTag) {
        this.sourceTag = sourceTag;
    }

    public List<ContactInfoDubboView> getContactInfoList() {
        return contactInfoList;
    }

    public void setContactInfoList(List<ContactInfoDubboView> contactInfoList) {
        this.contactInfoList = contactInfoList;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public Integer getCertificatesType() {
        return certificatesType;
    }

    public void setCertificatesType(Integer certificatesType) {
        this.certificatesType = certificatesType;
    }

    public String getCertificatesNumber() {
        return certificatesNumber;
    }

    public void setCertificatesNumber(String certificatesNumber) {
        this.certificatesNumber = certificatesNumber;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getMemberCode() {
        return memberCode;
    }

    public void setMemberCode(String memberCode) {
        this.memberCode = memberCode;
    }

    public Integer getLegalPersonFlag() {
        return legalPersonFlag;
    }

    public void setLegalPersonFlag(Integer legalPersonFlag) {
        this.legalPersonFlag = legalPersonFlag;
    }

    public Integer getSigningPersonFlag() {
        return signingPersonFlag;
    }

    public void setSigningPersonFlag(Integer signingPersonFlag) {
        this.signingPersonFlag = signingPersonFlag;
    }

    public LocalDateTime getSigningPersonUpdate() {
        return signingPersonUpdate;
    }

    public void setSigningPersonUpdate(LocalDateTime signingPersonUpdate) {
        this.signingPersonUpdate = signingPersonUpdate;
    }
}