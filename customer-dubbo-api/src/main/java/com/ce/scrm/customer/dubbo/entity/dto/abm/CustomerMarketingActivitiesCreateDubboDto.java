package com.ce.scrm.customer.dubbo.entity.dto.abm;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description ABM 营销活动创建
 * <AUTHOR>
 * @Date 2025-07-09 14:45
 */
@Data
public class CustomerMarketingActivitiesCreateDubboDto extends SignData implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 活动名称
	 */
	@NotBlank(message = "活动名称不能为空")
	private String activityName;

	/**
	 * 活动类型/触达方式 1-短信 2-邮件 3-电话 4-其他
	 */
	@NotNull(message = "活动触达方式不能为空")
	private Integer activityType;

	/**
	 * 模板id
	 */
	private String templateId;

	/**
	 * 活动内容/触达内容
	 */
	@NotBlank(message = "活动内容不能为空")
	private String activityContent;

	/**
	 * 关联活动链接
	 */
	@NotBlank(message = "活动链接不能为空")
	private String activityUrl;

	/**
	 * 客群id
	 */
	@NotBlank(message = "客群id不能为空")
	private String segmentId;

	/**
	 * 客群人数
	 */
	@NotNull(message = "客群人数不能为空")
	private Integer segmentCustCount;

	/**
	 * 客群名称
	 */
	@NotBlank(message = "客群名称不能为空")
	private String segmentName;

	/**
	 * 执行时间
	 */
	@NotNull(message = "执行时间不能为空")
	private Date executeTime;

	/**
	 * 创建人ID
	 */
	@NotBlank(message = "创建人id不能为空")
	private String createBy;


	/**
	 * 创建人名称
	 */
	@NotBlank(message = "创建人名称不能为空")
	private String createName;


	/**
	 * 电话触达方式对应的分发记录
	 */
	private List<CustomerDistributeSdrRecordDubboDto> distributeSdrRecordDubboDtos;
}
