package com.ce.scrm.customer.dubbo.entity.view;

import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerLeadsDubboView;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @project scrm-customer
 * <AUTHOR>
 * @date 2025/7/14 10:42:42
 * @version 1.0
 * 客户ES业务, 客户数据保持ES原始数据类型
 */
public class CustomerESDubboView implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 搜客宝公司唯一ID
     */
    private String pid;

    /**
     * 客户类型：1、国内企业，2、个人，3、国外及港澳台
     */
    private Integer customerType;

    /**
     * 客户/企业名称
     */

    private String customerName;

    /**
     * 证件类型：1、营业执照，2、身份证，3、军人身份证，4、护照，5、港澳通行证
     */
    private Integer certificateType;

    /**
     * 证件编码
     */
    private String certificateCode;

    /**
     * 登记状态
     */
    private String checkInState;

    /**
     * 成立日期
     */
    private LocalDate establishDate;

    /**
     * 注册资本
     */
    private String registerCapital;

    /**
     * 工商注册号
     */
    private String registerNo;

    /**
     * 企业类型
     */
    private String enterpriseType;

    /**
     * 营业开始时间
     */
    private LocalDate openStartTime;

    /**
     * 营业结束时间
     */
    private LocalDate openEndTime;

    /**
     * 核准日期
     */
    private LocalDate approveDate;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 登记机关
     */
    private String registrationOrg;

    /**
     * 一级国标行业编码
     */
    private String firstIndustryCode;

    /**
     * 一级国标行业名称
     */
    private String firstIndustryName;

    /**
     * 二级国标行业编码
     */
    private String secondIndustryCode;

    /**
     * 二级国标行业名称
     */
    private String secondIndustryName;


    /**
     * 三级国标行业编码
     */
    private String thirdIndustryCode;

    /**
     * 三级国标行业名称
     */
    private String thirdIndustryName;


    /**
     * 四级国标行业编码
     */
    private String fourthIndustryCode;

    /**
     * 四级国标行业名称
     */
    private String fourthIndustryName;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 客户当前阶段：1、线索，2、商机，3、保有客户，4、流失客户
     * 需要db-es转换
     */
    private Integer stage;

    /**
     * 客户创建方式：1、大数据，2、商务创建，3、市场商机
     */
    private Integer createWay;

    /**
     * 客户来源
     */
    private String labelFrom;

    /**
     * 删除标记：1、删除，0、未删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者凭证（废弃）
     */
    private String creatorKey;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人凭证（废弃）
     */
    private String operatorKey;

    /**
     * 更新人
     */
    private String operator;

    /**
     * 无效标记：1、无效，0、有效
     */
    private Integer invalidFlag;

    /**
     * 是否是外贸用户
     */
    private Integer tagWaimao;

    /**
     * 是否门户连带客户 0:否，1:是
     */
    private Integer tagMenhuRelated;

    /**
     * 是否生态转门户 0:否，1:是
     */
    private Integer tagEcoToMenhu;

    /**
     * 是否生态客户 0:否，1:是
     */
    private Integer tagEcoCust;

    /**
     * 是否数字版本门户 0:否，1:是
     */
    private Integer tagMenhuDigital;

    /**
     * 是否2023版本门户 0:否，1:是
     */
    private Integer tagMenhu2023;

    /**
     * 是否低版本门户 0:否，1:是
     * 需要db-es转换
     */
    private Integer tagMenhuLower;

    /**
     * 是否门户应升已升客户 0:否，1:是
     */
    private Integer tagMenhuUpgradeableUpgrade;

    /**
     * 是否门户应升级客户 0:否，1:是
     */
    private Integer tagMenhuUpgradeable;

    /**
     * 是否门户应续已续客户 0:否，1:是
     */
    private Integer tagMenhuRenewableRenew;

    /**
     * 是否门户应续客户 0:否，1:是
     */
    private Integer tagMenhuRenewable;

    /**
     * 是否交叉购买客户 0:否，1:是
     */
    private Integer tagCrossBuy;

    /**
     * 是否纯门户客户 0:否，1:是
     */
    private Integer tagPureMenhu;

    /**
     * 搜客宝 1:规模工业，2:规上服务业，3:规上建筑业，4:规上批发零售业，5:规上住宿餐饮业，6:规上房地产开发与经营业
     */
    private String tagFlag7;

    /**
     * 搜客宝 行业 1:律师，2:学校，3:医院
     */
    private String tagFlag8;

    /**
     * 科技型企业
     */
    private String tagTechcompany;

    /**
     * 门户新客户首次购买时间
     */
    private Date tagMenhuNewTime;

    /**
     * 门户新客户首次购买产品类别 0:低版本 1:门户2023 2:数字门户
     */
    private String tagMenhuNewCategory;

    /**
     * 是否是报价客户 0:否 1:是
     */
    private Integer tagQuoteCust;

    /**
     * 推荐客户的id
     */
    private String recommendCustId;

    /**
     * 推荐客户创建时间
     */
    private LocalDateTime recommendCustCreateTime;


    /**
     * 是否保有客户 0:否，1:是
     */
    private Integer tagRetainCust;

    /**
     * 到期时间
     */
    private Date tagRetainTime;

    /**
     * 是否流失客户 0:否，1:是
     */
    private Integer tagLostCust;

    /**
     * 流失时间
     */
    private Date tagLostTime;

    /**
     * 是否转介绍新客 0:否，1:是
     */
    private Integer tagInvitedNewCust;

    /**
     * 是否合格新客 0:否，1:是
     */
    private Integer tagQualifiedNewCust;

    /**
     * 是否合格老客0:否，1:是
     */
    private Integer tagQualifiedOldCust;

    /**
     * 是否低价值客户 0:否 1:是
     */
    private Integer tagLowValue;

    /**
     * 所属商务id
     * 需要db-es转换
     */
    private String salerId;

    /**
     * 部门id
     * 需要db-es转换
     */
    private String deptId;

    /**
     * 分公司ID
     * 需要db-es转换
     */
    private String subId;

    /**
     * 区域ID
     * 需要db-es转换
     */
    private String areaId;

    /**
     * 状态值（1、保护；2、总监；3、经理；4、客户池）
     * 需要db-es转换
     */
    private String status;

    /**
     * 客户阶段。(2：保护跟进；3：网站客户；4：非网站客户)
     * 需要db-es转换
     */
    private String tradeProductType;

    /**
     * 阶段性保护时间
     * 需要db-es转换
     */
    private Date protectTime;

    /**
     * 已保护天数(仅2023年以后数据)
     */
    private Integer protectDay;

    /**
     * 本月打卡次数
     * 需要db-es转换
     */
    private Integer clockCountMonth;

    /**
     * 累计打卡次数
     * 需要db-es转换
     */
    private Integer clockCount;

    /**
     * 客户订单首次支付时间
     * 需要db-es转换
     */
    private Date firstPaymentTime;

    /**
     * 成为合格新客时间
     */
    private Date tagQualifiedNewCustTime;

    private String tagFlag12;

    /**
     * 是否是大客户（ka）新
     */
    private Integer tagDakehu;

    /**
     * 客户分层
     */
    private Integer customerLayer;

    /**
     * 客户等级
     */
    private Integer customerGrade;

    /**
     * CDP标签user_tag_sfvwcjkh20250530 未成交客户是否是vip判断依据
     */
    private Integer tagWeichengjiaoVip;

    /**
     * 法人
     */
    private String legalPerson;

    /*
     * 分发时间：指跨境ABM项目，leads分发给SDR的时间
     */
    private Date distributeTime;

    /*
     * 保护释放时间
     */
    private Date releaseTime;

    /*
     * 线索产出时间
     */
    private Date leadsCreateTime;

    /*
     * 来源编码
     */
    private String leadsSourceCode;

    /*
     * 意愿编码
     */
    private String leadsIntentCode;

    /*
     * 事业部ID
     */
    private String buId;

    /*
     * 保护结束时间
     */
    private Date protectEndTime;

    /*
     * 最近拜访类型
     */
    private Integer lastVisitType;

    /*
     * 最近拜访时间
     */
    private Date lastFollowTime;

    /*
     * 最近上门时间
     */
    private Date lastSiteTime;

    /**
     * 绑定客户标识 0未绑定 1绑定
     */
    private Integer bindFlag;

    /**
     * 商机是否确认标记 0 正常（已经确认） 1 未确认 2 流转过一次-未确认
     */
    private Integer businessOpportunityConfirmationFlag;

    /**
     * 分发渠道
     */
    private Integer distributeChannel;

    /**
     * 推送销售审核状态
     */
    private Integer reviewStatus;

    /**
     * 跟进次数
     */
    private Integer followCount;
    /**
     * 上门次数
     */
    private Integer siteCount;

    /*
     * SDR跟进时间
     */
    private Date lastSdrFollowTime;

    /**
     * SDR跟进次数
     */
    private Integer sdrFollowCount;

    /**
     * 客户leads列表
     */
    private List<CustomerLeadsDubboView> customerLeadsList;

    /**
     * 联系人列表
     */
    private List<ContactPersonDataDubboView> contactPersonList;


    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }

    public String getCertificateCode() {
        return certificateCode;
    }

    public void setCertificateCode(String certificateCode) {
        this.certificateCode = certificateCode;
    }

    public String getCheckInState() {
        return checkInState;
    }

    public void setCheckInState(String checkInState) {
        this.checkInState = checkInState;
    }

    public LocalDate getEstablishDate() {
        return establishDate;
    }

    public void setEstablishDate(LocalDate establishDate) {
        this.establishDate = establishDate;
    }

    public String getRegisterCapital() {
        return registerCapital;
    }

    public void setRegisterCapital(String registerCapital) {
        this.registerCapital = registerCapital;
    }

    public String getRegisterNo() {
        return registerNo;
    }

    public void setRegisterNo(String registerNo) {
        this.registerNo = registerNo;
    }

    public String getEnterpriseType() {
        return enterpriseType;
    }

    public void setEnterpriseType(String enterpriseType) {
        this.enterpriseType = enterpriseType;
    }

    public LocalDate getOpenStartTime() {
        return openStartTime;
    }

    public void setOpenStartTime(LocalDate openStartTime) {
        this.openStartTime = openStartTime;
    }

    public LocalDate getOpenEndTime() {
        return openEndTime;
    }

    public void setOpenEndTime(LocalDate openEndTime) {
        this.openEndTime = openEndTime;
    }

    public LocalDate getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(LocalDate approveDate) {
        this.approveDate = approveDate;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public String getRegistrationOrg() {
        return registrationOrg;
    }

    public void setRegistrationOrg(String registrationOrg) {
        this.registrationOrg = registrationOrg;
    }

    public String getFirstIndustryCode() {
        return firstIndustryCode;
    }

    public void setFirstIndustryCode(String firstIndustryCode) {
        this.firstIndustryCode = firstIndustryCode;
    }

    public String getFirstIndustryName() {
        return firstIndustryName;
    }

    public void setFirstIndustryName(String firstIndustryName) {
        this.firstIndustryName = firstIndustryName;
    }

    public String getSecondIndustryCode() {
        return secondIndustryCode;
    }

    public void setSecondIndustryCode(String secondIndustryCode) {
        this.secondIndustryCode = secondIndustryCode;
    }

    public String getSecondIndustryName() {
        return secondIndustryName;
    }

    public void setSecondIndustryName(String secondIndustryName) {
        this.secondIndustryName = secondIndustryName;
    }

    public String getThirdIndustryCode() {
        return thirdIndustryCode;
    }

    public void setThirdIndustryCode(String thirdIndustryCode) {
        this.thirdIndustryCode = thirdIndustryCode;
    }

    public String getThirdIndustryName() {
        return thirdIndustryName;
    }

    public void setThirdIndustryName(String thirdIndustryName) {
        this.thirdIndustryName = thirdIndustryName;
    }

    public String getFourthIndustryCode() {
        return fourthIndustryCode;
    }

    public void setFourthIndustryCode(String fourthIndustryCode) {
        this.fourthIndustryCode = fourthIndustryCode;
    }

    public String getFourthIndustryName() {
        return fourthIndustryName;
    }

    public void setFourthIndustryName(String fourthIndustryName) {
        this.fourthIndustryName = fourthIndustryName;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public Integer getStage() {
        return stage;
    }

    public void setStage(Integer stage) {
        this.stage = stage;
    }

    public Integer getCreateWay() {
        return createWay;
    }

    public void setCreateWay(Integer createWay) {
        this.createWay = createWay;
    }

    public String getLabelFrom() {
        return labelFrom;
    }

    public void setLabelFrom(String labelFrom) {
        this.labelFrom = labelFrom;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreatorKey() {
        return creatorKey;
    }

    public void setCreatorKey(String creatorKey) {
        this.creatorKey = creatorKey;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperatorKey() {
        return operatorKey;
    }

    public void setOperatorKey(String operatorKey) {
        this.operatorKey = operatorKey;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Integer getInvalidFlag() {
        return invalidFlag;
    }

    public void setInvalidFlag(Integer invalidFlag) {
        this.invalidFlag = invalidFlag;
    }

    public Integer getTagWaimao() {
        return tagWaimao;
    }

    public void setTagWaimao(Integer tagWaimao) {
        this.tagWaimao = tagWaimao;
    }

    public Integer getTagMenhuRelated() {
        return tagMenhuRelated;
    }

    public void setTagMenhuRelated(Integer tagMenhuRelated) {
        this.tagMenhuRelated = tagMenhuRelated;
    }

    public Integer getTagEcoToMenhu() {
        return tagEcoToMenhu;
    }

    public void setTagEcoToMenhu(Integer tagEcoToMenhu) {
        this.tagEcoToMenhu = tagEcoToMenhu;
    }

    public Integer getTagEcoCust() {
        return tagEcoCust;
    }

    public void setTagEcoCust(Integer tagEcoCust) {
        this.tagEcoCust = tagEcoCust;
    }

    public Integer getTagMenhuDigital() {
        return tagMenhuDigital;
    }

    public void setTagMenhuDigital(Integer tagMenhuDigital) {
        this.tagMenhuDigital = tagMenhuDigital;
    }

    public Integer getTagMenhu2023() {
        return tagMenhu2023;
    }

    public void setTagMenhu2023(Integer tagMenhu2023) {
        this.tagMenhu2023 = tagMenhu2023;
    }

    public Integer getTagMenhuLower() {
        return tagMenhuLower;
    }

    public void setTagMenhuLower(Integer tagMenhuLower) {
        this.tagMenhuLower = tagMenhuLower;
    }

    public Integer getTagMenhuUpgradeableUpgrade() {
        return tagMenhuUpgradeableUpgrade;
    }

    public void setTagMenhuUpgradeableUpgrade(Integer tagMenhuUpgradeableUpgrade) {
        this.tagMenhuUpgradeableUpgrade = tagMenhuUpgradeableUpgrade;
    }

    public Integer getTagMenhuUpgradeable() {
        return tagMenhuUpgradeable;
    }

    public void setTagMenhuUpgradeable(Integer tagMenhuUpgradeable) {
        this.tagMenhuUpgradeable = tagMenhuUpgradeable;
    }

    public Integer getTagMenhuRenewableRenew() {
        return tagMenhuRenewableRenew;
    }

    public void setTagMenhuRenewableRenew(Integer tagMenhuRenewableRenew) {
        this.tagMenhuRenewableRenew = tagMenhuRenewableRenew;
    }

    public Integer getTagMenhuRenewable() {
        return tagMenhuRenewable;
    }

    public void setTagMenhuRenewable(Integer tagMenhuRenewable) {
        this.tagMenhuRenewable = tagMenhuRenewable;
    }

    public Integer getTagCrossBuy() {
        return tagCrossBuy;
    }

    public void setTagCrossBuy(Integer tagCrossBuy) {
        this.tagCrossBuy = tagCrossBuy;
    }

    public Integer getTagPureMenhu() {
        return tagPureMenhu;
    }

    public void setTagPureMenhu(Integer tagPureMenhu) {
        this.tagPureMenhu = tagPureMenhu;
    }

    public String getTagFlag7() {
        return tagFlag7;
    }

    public void setTagFlag7(String tagFlag7) {
        this.tagFlag7 = tagFlag7;
    }

    public String getTagFlag8() {
        return tagFlag8;
    }

    public void setTagFlag8(String tagFlag8) {
        this.tagFlag8 = tagFlag8;
    }

    public String getTagTechcompany() {
        return tagTechcompany;
    }

    public void setTagTechcompany(String tagTechcompany) {
        this.tagTechcompany = tagTechcompany;
    }

    public Date getTagMenhuNewTime() {
        return tagMenhuNewTime;
    }

    public void setTagMenhuNewTime(Date tagMenhuNewTime) {
        this.tagMenhuNewTime = tagMenhuNewTime;
    }

    public String getTagMenhuNewCategory() {
        return tagMenhuNewCategory;
    }

    public void setTagMenhuNewCategory(String tagMenhuNewCategory) {
        this.tagMenhuNewCategory = tagMenhuNewCategory;
    }

    public Integer getTagQuoteCust() {
        return tagQuoteCust;
    }

    public void setTagQuoteCust(Integer tagQuoteCust) {
        this.tagQuoteCust = tagQuoteCust;
    }

    public String getRecommendCustId() {
        return recommendCustId;
    }

    public void setRecommendCustId(String recommendCustId) {
        this.recommendCustId = recommendCustId;
    }

    public LocalDateTime getRecommendCustCreateTime() {
        return recommendCustCreateTime;
    }

    public void setRecommendCustCreateTime(LocalDateTime recommendCustCreateTime) {
        this.recommendCustCreateTime = recommendCustCreateTime;
    }

    public Integer getTagRetainCust() {
        return tagRetainCust;
    }

    public void setTagRetainCust(Integer tagRetainCust) {
        this.tagRetainCust = tagRetainCust;
    }

    public Date getTagRetainTime() {
        return tagRetainTime;
    }

    public void setTagRetainTime(Date tagRetainTime) {
        this.tagRetainTime = tagRetainTime;
    }

    public Integer getTagLostCust() {
        return tagLostCust;
    }

    public void setTagLostCust(Integer tagLostCust) {
        this.tagLostCust = tagLostCust;
    }

    public Date getTagLostTime() {
        return tagLostTime;
    }

    public void setTagLostTime(Date tagLostTime) {
        this.tagLostTime = tagLostTime;
    }

    public Integer getTagInvitedNewCust() {
        return tagInvitedNewCust;
    }

    public void setTagInvitedNewCust(Integer tagInvitedNewCust) {
        this.tagInvitedNewCust = tagInvitedNewCust;
    }

    public Integer getTagQualifiedNewCust() {
        return tagQualifiedNewCust;
    }

    public void setTagQualifiedNewCust(Integer tagQualifiedNewCust) {
        this.tagQualifiedNewCust = tagQualifiedNewCust;
    }

    public Integer getTagQualifiedOldCust() {
        return tagQualifiedOldCust;
    }

    public void setTagQualifiedOldCust(Integer tagQualifiedOldCust) {
        this.tagQualifiedOldCust = tagQualifiedOldCust;
    }

    public Integer getTagLowValue() {
        return tagLowValue;
    }

    public void setTagLowValue(Integer tagLowValue) {
        this.tagLowValue = tagLowValue;
    }

    public String getSalerId() {
        return salerId;
    }

    public void setSalerId(String salerId) {
        this.salerId = salerId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTradeProductType() {
        return tradeProductType;
    }

    public void setTradeProductType(String tradeProductType) {
        this.tradeProductType = tradeProductType;
    }

    public Date getProtectTime() {
        return protectTime;
    }

    public void setProtectTime(Date protectTime) {
        this.protectTime = protectTime;
    }

    public Integer getProtectDay() {
        return protectDay;
    }

    public void setProtectDay(Integer protectDay) {
        this.protectDay = protectDay;
    }

    public Integer getClockCountMonth() {
        return clockCountMonth;
    }

    public void setClockCountMonth(Integer clockCountMonth) {
        this.clockCountMonth = clockCountMonth;
    }

    public Integer getClockCount() {
        return clockCount;
    }

    public void setClockCount(Integer clockCount) {
        this.clockCount = clockCount;
    }

    public Date getFirstPaymentTime() {
        return firstPaymentTime;
    }

    public void setFirstPaymentTime(Date firstPaymentTime) {
        this.firstPaymentTime = firstPaymentTime;
    }

    public Date getTagQualifiedNewCustTime() {
        return tagQualifiedNewCustTime;
    }

    public void setTagQualifiedNewCustTime(Date tagQualifiedNewCustTime) {
        this.tagQualifiedNewCustTime = tagQualifiedNewCustTime;
    }

    public String getTagFlag12() {
        return tagFlag12;
    }

    public void setTagFlag12(String tagFlag12) {
        this.tagFlag12 = tagFlag12;
    }

    public Integer getTagDakehu() {
        return tagDakehu;
    }

    public void setTagDakehu(Integer tagDakehu) {
        this.tagDakehu = tagDakehu;
    }

    public Integer getCustomerLayer() {
        return customerLayer;
    }

    public void setCustomerLayer(Integer customerLayer) {
        this.customerLayer = customerLayer;
    }

    public Integer getCustomerGrade() {
        return customerGrade;
    }

    public void setCustomerGrade(Integer customerGrade) {
        this.customerGrade = customerGrade;
    }

    public Integer getTagWeichengjiaoVip() {
        return tagWeichengjiaoVip;
    }

    public void setTagWeichengjiaoVip(Integer tagWeichengjiaoVip) {
        this.tagWeichengjiaoVip = tagWeichengjiaoVip;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public List<CustomerLeadsDubboView> getCustomerLeadsList() {
        return customerLeadsList;
    }

    public void setCustomerLeadsList(List<CustomerLeadsDubboView> customerLeadsList) {
        this.customerLeadsList = customerLeadsList;
    }

    public Date getDistributeTime() {
        return distributeTime;
    }

    public void setDistributeTime(Date distributeTime) {
        this.distributeTime = distributeTime;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    public Date getLeadsCreateTime() {
        return leadsCreateTime;
    }

    public void setLeadsCreateTime(Date leadsCreateTime) {
        this.leadsCreateTime = leadsCreateTime;
    }

    public String getLeadsSourceCode() {
        return leadsSourceCode;
    }

    public void setLeadsSourceCode(String leadsSourceCode) {
        this.leadsSourceCode = leadsSourceCode;
    }

    public String getLeadsIntentCode() {
        return leadsIntentCode;
    }

    public void setLeadsIntentCode(String leadsIntentCode) {
        this.leadsIntentCode = leadsIntentCode;
    }

    public String getBuId() {
        return buId;
    }

    public void setBuId(String buId) {
        this.buId = buId;
    }

    public Date getProtectEndTime() {
        return protectEndTime;
    }

    public void setProtectEndTime(Date protectEndTime) {
        this.protectEndTime = protectEndTime;
    }

    public Integer getLastVisitType() {
        return lastVisitType;
    }

    public void setLastVisitType(Integer lastVisitType) {
        this.lastVisitType = lastVisitType;
    }

    public Date getLastFollowTime() {
        return lastFollowTime;
    }

    public void setLastFollowTime(Date lastFollowTime) {
        this.lastFollowTime = lastFollowTime;
    }

    public Date getLastSiteTime() {
        return lastSiteTime;
    }

    public void setLastSiteTime(Date lastSiteTime) {
        this.lastSiteTime = lastSiteTime;
    }

    public List<ContactPersonDataDubboView> getContactPersonList() {
        return contactPersonList;
    }

    public void setContactPersonList(List<ContactPersonDataDubboView> contactPersonList) {
        this.contactPersonList = contactPersonList;
    }

    public Integer getDistributeChannel() {
        return distributeChannel;
    }

    public void setDistributeChannel(Integer distributeChannel) {
        this.distributeChannel = distributeChannel;
    }

    public Integer getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public Integer getFollowCount() {
        return followCount;
    }

    public void setFollowCount(Integer followCount) {
        this.followCount = followCount;
    }

    public Integer getSiteCount() {
        return siteCount;
    }

    public void setSiteCount(Integer siteCount) {
        this.siteCount = siteCount;
    }

    public Date getLastSdrFollowTime() {
        return lastSdrFollowTime;
    }

    public void setLastSdrFollowTime(Date lastSdrFollowTime) {
        this.lastSdrFollowTime = lastSdrFollowTime;
    }

    public Integer getSdrFollowCount() {
        return sdrFollowCount;
    }

    public void setSdrFollowCount(Integer sdrFollowCount) {
        this.sdrFollowCount = sdrFollowCount;
    }

    public Integer getBusinessOpportunityConfirmationFlag() {
        return businessOpportunityConfirmationFlag;
    }

    public void setBusinessOpportunityConfirmationFlag(Integer businessOpportunityConfirmationFlag) {
        this.businessOpportunityConfirmationFlag = businessOpportunityConfirmationFlag;
    }

    public Integer getBindFlag() {
        return bindFlag;
    }

    public void setBindFlag(Integer bindFlag) {
        this.bindFlag = bindFlag;
    }
}
