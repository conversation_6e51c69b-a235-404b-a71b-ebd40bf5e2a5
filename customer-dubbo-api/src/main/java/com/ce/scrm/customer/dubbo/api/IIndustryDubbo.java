package com.ce.scrm.customer.dubbo.api;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.IndustryDubboView;

import java.util.List;

/**
 * 客户dubbo接口
 * <AUTHOR>
 * @date 2023/4/12 16:54
 * @version 1.0.0
 */
public interface IIndustryDubbo {

    /**
     * 获取所有行业数据
     * <AUTHOR>
     * @date 2023/4/12 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.IndustryDubboView>
     **/
    DubboResult<List<IndustryDubboView>> getAllData();

}