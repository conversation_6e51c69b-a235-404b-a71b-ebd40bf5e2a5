package com.ce.scrm.customer.dubbo.api;

import com.ce.scrm.customer.dubbo.entity.dto.ContactInfoDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.ContactInfoOperDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.ContactPersonOperDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.ContactInfoDubboView;
import com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboView;

import java.util.List;

/**
 * 联系人联系方式dubbo接口
 * <AUTHOR>
 * @date 2023/4/12 16:54
 * @version 1.0.0
 */
public interface IContactInfoDubbo {

    /**
     * 根据获取联系人联系方式
     * @param contactInfoDubboDto 参数
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<List<com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboDto>>
     **/
    DubboResult<ContactPersonDubboView> detailByWay(ContactInfoDubboDto contactInfoDubboDto);

    /**
     * 根据获取联系人联系方式
     * @param contactInfoDubboDto 参数
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<List<com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboDto>>
     **/
    DubboResult<List<ContactInfoDubboView>> detailList(ContactInfoDubboDto contactInfoDubboDto);


    /**
     * 根据联系方式列表查询联系人
     * @param contactInfoDubboDto
     * <AUTHOR>
     * @date 2023/9/12 14:15
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboView>>
     **/
    DubboResult<List<ContactInfoDubboView>> contactPersonByContactWayList(ContactInfoDubboDto contactInfoDubboDto);
}