package com.ce.scrm.customer.dubbo.entity.view;

import java.io.Serializable;

/**
 * description: 存续企业分布汇总表
 * @author: DD.Jiu
 * date: 2025/6/27.
 */
public class CompanyDistributionSummaryDubboView implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 地区编码
     */
    private String areaCode;
    /**
     * 客户数量
     */
    private Integer total;

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}
