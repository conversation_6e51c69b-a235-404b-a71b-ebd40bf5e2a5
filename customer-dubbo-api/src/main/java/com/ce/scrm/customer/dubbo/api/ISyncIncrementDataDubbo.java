package com.ce.scrm.customer.dubbo.api;

import com.ce.scrm.customer.dubbo.entity.dto.IncrementSyncDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;

/**
 * 同步客户联系人数据dubbo接口
 * <AUTHOR>
 * @date 2023/4/20 16:54
 * @version 1.0.0
 */
public interface ISyncIncrementDataDubbo {
    /**
     * 删除数据
     * @param dataType  数据类型：1、根据联系人ID删除联系方式，2、根据联系方式ID删除联系方式
     * @param data  同步数据
     * <AUTHOR>
     * @date 2023/4/26 14:16
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> delete(Integer dataType, String data);

    /**
     * 增量同步
     * @param dataType  数据类型：1、客户，2、联系人、3、联系方式
     * @param data  同步数据
     * <AUTHOR>
     * @date 2023/4/26 14:17
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> sync(Integer dataType, String data);


    /**
     * 处理实时增量同步参数
     * @param incrementSyncDto  数据同步参数
     * <AUTHOR>
     * @date 2023/5/4 10:10
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> handleSyncParam(IncrementSyncDto incrementSyncDto);
}