package com.ce.scrm.customer.dubbo.entity.dto.abm;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * sdr推送销售审核表
 */
@Data
public class SdrPushSaleReviewDubboUpdateDto extends SignData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull(message = "审核id不能为空")
    private Long id;

    /**
     * 审核状态 0:待审核 1:审核通过
     */
    @NotNull(message = "审核结论不能为空")
    private Integer reviewStatus;

    /**
     * 审核人id
     */
    @NotBlank(message = "审核人不能为空")
    private String reviewId;

}