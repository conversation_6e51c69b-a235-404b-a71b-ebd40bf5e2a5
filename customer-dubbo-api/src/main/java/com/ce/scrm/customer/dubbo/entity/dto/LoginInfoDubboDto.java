package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;

import java.io.Serializable;

public class LoginInfoDubboDto extends SignData implements Serializable {

    /**
     * 职位
     */
    private String loginPosition;

    /**
     * 区域ID
     */
    private String loginAreaId;

    /**
     * 分司ID
     */
    private String loginSubId;

    /**
     * 部门ID
     */
    private String loginOrgId;

    /**
     * 员工ID
     */
    private String loginEmployeeId;

    public String getLoginPosition() {
        return loginPosition;
    }

    public void setLoginPosition(String loginPosition) {
        this.loginPosition = loginPosition;
    }

    public String getLoginAreaId() {
        return loginAreaId;
    }

    public void setLoginAreaId(String loginAreaId) {
        this.loginAreaId = loginAreaId;
    }

    public String getLoginSubId() {
        return loginSubId;
    }

    public void setLoginSubId(String loginSubId) {
        this.loginSubId = loginSubId;
    }

    public String getLoginOrgId() {
        return loginOrgId;
    }

    public void setLoginOrgId(String loginOrgId) {
        this.loginOrgId = loginOrgId;
    }

    public String getLoginEmployeeId() {
        return loginEmployeeId;
    }

    public void setLoginEmployeeId(String loginEmployeeId) {
        this.loginEmployeeId = loginEmployeeId;
    }
}
