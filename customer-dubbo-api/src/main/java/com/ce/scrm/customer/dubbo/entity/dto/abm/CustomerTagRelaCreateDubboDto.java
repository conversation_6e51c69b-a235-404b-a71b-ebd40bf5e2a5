package com.ce.scrm.customer.dubbo.entity.dto.abm;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description ABM 营销活动创建
 * <AUTHOR>
 * @Date 2025-07-09 14:45
 */
@Data
public class CustomerTagRelaCreateDubboDto extends SignData implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 客户id
	 */
	@NotBlank(message = "客户id不能为空")
	private String customerId;

	/**
	 * 创建人
	 */
	@NotBlank
	private String createBy;

	/**
	 * 标签列表
	 */
	@Valid
	@NotEmpty(message = "客户的标签列表不能为空")
	private List<TagInfo> tagInfoList;

	@Data
	public static class TagInfo implements Serializable {
		/**
		 * tagId
		 */
		@NotNull(message = "标签id不能为空")
		private Long tagId;

		/**
		 * 标签名称
		 */
		private String tagName;

		/**
		 * 标签积分
		 */
		@NotBlank(message = "标签积分不能为空")
		private BigDecimal weight;
	}

}
