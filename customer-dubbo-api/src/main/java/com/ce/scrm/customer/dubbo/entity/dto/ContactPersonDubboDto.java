package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;

import java.io.Serializable;
import java.util.List;

/**
 * 联系人数据
 * <AUTHOR>
 * @date 2023/4/12 17:37
 * @version 1.0.0
 */
public class ContactPersonDubboDto extends SignData implements Serializable {

    /**
     * 联系人id（此参数与下面的客户ID和手机号，存在互斥关系，优先获取联系人ID）
     */
    private String contactPersonId;

    /**
     * 客户ID（仅业务系统可用）
     */
    private String customerId;

    /**
     * 手机号（仅业务系统可用）
     */
    private String phone;

    /**
     * 联系人id
     */
    private List<String> contactPersonIdList;

    public String getContactPersonId() {
        return contactPersonId;
    }

    public void setContactPersonId(String contactPersonId) {
        this.contactPersonId = contactPersonId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public List<String> getContactPersonIdList() {
        return contactPersonIdList;
    }

    public void setContactPersonIdList(List<String> contactPersonIdList) {
        this.contactPersonIdList = contactPersonIdList;
    }
}