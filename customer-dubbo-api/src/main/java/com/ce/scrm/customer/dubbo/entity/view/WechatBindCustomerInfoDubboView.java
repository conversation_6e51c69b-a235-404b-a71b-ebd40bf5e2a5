package com.ce.scrm.customer.dubbo.entity.view;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/25
 */

public class WechatBindCustomerInfoDubboView implements Serializable {

    /**
     * 联系人id
     */
    private String contactPersonId;
    /**
     * 联系人姓名
     */
    private String contactPersonName;

    /**
     * 绑定类型：0、无，1、微信，2、企业微信
     */
    private Integer bindType;

    /**
     * 微信unionId
     */
    private String unionId;

    /**
     * 微信昵称
     */
    private String wechatNickName;

    /**
     * unionId绑定时间
     */
    private LocalDateTime unionIdBindTime;
    /**
     * 客户ID
     */
    private String customerId;
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 操作人
     */
    private String operator;

    public String getContactPersonId() {
        return contactPersonId;
    }

    public void setContactPersonId(String contactPersonId) {
        this.contactPersonId = contactPersonId;
    }

    public String getContactPersonName() {
        return contactPersonName;
    }

    public void setContactPersonName(String contactPersonName) {
        this.contactPersonName = contactPersonName;
    }

    public Integer getBindType() {
        return bindType;
    }

    public void setBindType(Integer bindType) {
        this.bindType = bindType;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public LocalDateTime getUnionIdBindTime() {
        return unionIdBindTime;
    }

    public void setUnionIdBindTime(LocalDateTime unionIdBindTime) {
        this.unionIdBindTime = unionIdBindTime;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getWechatNickName() {
        return wechatNickName;
    }

    public void setWechatNickName(String wechatNickName) {
        this.wechatNickName = wechatNickName;
    }
}
