package com.ce.scrm.customer.dubbo.entity.view;

import java.io.Serializable;
import java.util.List;

/**
 * 行业数据
 * <AUTHOR>
 * @date 2023/4/12 17:37
 * @version 1.0.0
 */
public class IndustryDubboView implements Serializable {

    /**
     * code
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 父code
     */
    private String parentCode;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }
}