package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;

import java.io.Serializable;

/**
 * 查询联系人，获取客户名下“正式、临时 签单标签”
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/9/5
 **/
public class SigningPersonDetailDubboDto extends SignData implements Serializable {
    /**
     * 姓名 必传
     */
    private String contactPersonName;
    /**
     * 手机号 必传
     */
    private String phone;

    public String getContactPersonName() {
        return contactPersonName;
    }

    public void setContactPersonName(String contactPersonName) {
        this.contactPersonName = contactPersonName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
