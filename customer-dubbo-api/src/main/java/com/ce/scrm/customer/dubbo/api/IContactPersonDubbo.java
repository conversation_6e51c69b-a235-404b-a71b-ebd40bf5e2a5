package com.ce.scrm.customer.dubbo.api;

import com.ce.scrm.customer.dubbo.entity.dto.*;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.*;

import java.util.List;

/**
 * 联系人dubbo接口
 * <AUTHOR>
 * @date 2023/4/12 16:54
 * @version 1.0.0
 */
public interface IContactPersonDubbo {

    /**
     * 获取联系人
     * @param contactPersonDubboDto  联系人
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboDto>>
     **/
    DubboResult<ContactPersonDubboView> detail(ContactPersonDubboDto contactPersonDubboDto);

    /**
     * 获取客户联系人
     * @param customerDetailDubboDto  客户查询dto
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboView>>
     **/
    DubboResult<CustomerDubboView> customerContactDetail(CustomerDetailDubboDto customerDetailDubboDto);

    /**
     * 获取联系人
     * @param contactPersonDubboDto  联系人
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboDto>>
     **/
    DubboResult<List<ContactPersonDubboView>> detailList(ContactPersonDubboDto contactPersonDubboDto);

    /**
     * 获取联系人根据memberCode
     * @param customerDetailDubboDto
     * <AUTHOR>
     * @date 2023/5/5 14:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDubboDto>>
     **/
    DubboResult<List<CustomerDubboView>> customerContactDetailByMemberCodes(CustomerDetailDubboDto customerDetailDubboDto);

    /**
     * 获取联系人数据，给其他系统提供
     * @param contactPersonDubboDto 查询参数
     * <AUTHOR>
     * @date 2023/5/15 10:14
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView>>
     **/
    DubboResult<List<ContactPersonDataDubboView>> getData(ContactPersonDubboDto contactPersonDubboDto);

    /**
     * 获取客户联系人数据，给其他系统提供
     * @param customerDetailDubboDto 查询参数
     * <AUTHOR>
     * @date 2023/5/15 10:14
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView>>
     **/
    DubboResult<List<ContactPersonDataDubboView>> getCustomerData(CustomerDetailDubboDto customerDetailDubboDto);

    /**
     * 添加联系人，给其他系统提供
     * @param contactPersonAddDubboDto  联系人数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.ContactPersonAddDubboView>
     **/
    DubboResult<ContactPersonAddDubboView> add(ContactPersonAddDubboDto contactPersonAddDubboDto);

    /**
     * 更新联系人，给其他系统提供
     * @param contactPersonUpdateDubboDto  联系人数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> update(ContactPersonUpdateDubboDto contactPersonUpdateDubboDto);

    /**
     * 更新联系人，给其他系统提供
     * @param contactPersonUpdateDubboDto  联系人数据
     * <AUTHOR>
     * @date 2023/5/15 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> updatePhoneVerifiedFlag(ContactPersonUpdateDubboDto contactPersonUpdateDubboDto);

    /**
     * 修改签单人标签 0：不是签单联系人 1：临时签单联系人 2：正式签单联系人
     * @param signingPersonUpdateDubboDto    签单人参数
     * <AUTHOR>
     * @date 2024/9/5
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> updateSigningPersonFlag(SigningPersonUpdateDubboDto signingPersonUpdateDubboDto);

    /**
     * 获取签单联系人数据
     * @param signingPersonDetailDubboDto  查询参数
     * <AUTHOR>
     * @date 2024/9/5
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.SigningPersonDetailDataDubboView>>
     **/
    DubboResult<List<CustomerDubboView>> getSigningPersonDetailData(SigningPersonDetailDubboDto signingPersonDetailDubboDto);

    /**
     * 添加默认标记
     * @param orderDefaultFlagDubboDto  打标参数
     * <AUTHOR>
     * @date 2023/5/23 09:43
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> addDefaultFlag(OrderDefaultFlagDubboDto orderDefaultFlagDubboDto);

    /**
     * 联系人绑定微信unionId
     * @param contactPersonBindUnionIdDubboDto  绑定参数
     * <AUTHOR>
     * @date 2023/6/16 09:51
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> bindUnionId(ContactPersonBindUnionIdDubboDto contactPersonBindUnionIdDubboDto);

    /**
     * 联系人解绑微信unionId
     * @param contactPersonUnbindUnionIdDubboDto    解绑参数
     * <AUTHOR>
     * @date 2023/6/16 09:54
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> unbindUnionId(ContactPersonUnbindUnionIdDubboDto contactPersonUnbindUnionIdDubboDto);

    /**
     * 根据绑定unionId获取联系人客户数据
     * @param bindUnionIdQueryDubboDto  查询参数
     * <AUTHOR>
     * @date 2023/6/16 10:28
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List < com.ce.scrm.customer.dubbo.entity.view.ContactPersonCustomerDataDubboView>>
     **/
    DubboResult<List<ContactPersonCustomerDataDubboView>> getContactPersonDataByUnionId(BindUnionIdQueryDubboDto bindUnionIdQueryDubboDto);

    /**
     * 删除联系人
     * @param contactPersonId   联系人ID
     * <AUTHOR>
     * @date 2024/1/15 13:58
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> del(String contactPersonId);

    /***
     * 微信绑定信息查询
     * @param bindUnionIdQueryDubboDto
     * <AUTHOR>
     * @date 2024/11/25 19:37
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.customer.dubbo.entity.view.WechatBindCustomerInfoDubboView>>
    **/
    public DubboResult<List<WechatBindCustomerInfoDubboView>> getWechatBindCustomerInfoUnionId(BindUnionIdQueryDubboDto bindUnionIdQueryDubboDto);

    /***
     * TODO 
     * @param bindUnionIdQueryDubboDto
     * <AUTHOR>
     * @date 2024/11/25 20:55
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo<com.ce.scrm.customer.dubbo.entity.view.WechatBindCustomerInfoDubboView>>
    **/
    public DubboResult<DubboPageInfo<WechatBindCustomerInfoDubboView>> getWechatBindCustomerInfoEmpId(BindUnionIdQueryDubboDto bindUnionIdQueryDubboDto);
}