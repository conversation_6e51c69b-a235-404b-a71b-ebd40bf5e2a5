package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import com.ce.scrm.customer.dubbo.entity.view.CustomerESTagDubboView;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @project scrm-customer
 * <AUTHOR>
 * @date 2025/7/14 09:12:21
 * @version 1.0
 * 根据查询条件查询Elasticsearch中客户数据
 * 查询条件还会继续增加
 * 由于采用统一的ES查询方式，查询的字段都采用String，尽量不采用Integer等数据类型,Date等时间类型!!!
 */
public class CustomerESPageDubboDto extends SignData implements Serializable {

    /**
     * 成立日期
     */
    private String establishDate;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 搜客宝公司唯一ID
     */
    private String pid;

    /**
     * 客户类型：1、国内企业，2、个人，3、国外及港澳台
     */
    private String customerType;

    /**
     * 客户/企业名称
     */
    private String customerName;

    /**
     * 证件类型：1、营业执照，2、身份证，3、军人身份证，4、护照，5、港澳通行证
     */
    private String certificateType;

    /**
     * 证件编码
     */
    private String certificateCode;

    /**
     * 登记状态
     */
    private String checkInState;

    /**
     * 注册资本
     */
    private String registerCapital;

    /**
     * 工商注册号
     */
    private String registerNo;

    /**
     * 企业类型
     */
    private String enterpriseType;

    /**
     * 营业开始时间
     */
    private String openStartTime;

    /**
     * 营业结束时间
     */
    private String openEndTime;

    /**
     * 核准日期
     */
    private String approveDate;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 登记机关
     */
    private String registrationOrg;

    /**
     * 一级国标行业编码
     */
    private String firstIndustryCode;

    /**
     * 一级国标行业名称
     */
    private String firstIndustryName;

    /**
     * 二级国标行业编码
     */
    private String secondIndustryCode;

    /**
     * 二级国标行业名称
     */
    private String secondIndustryName;


    /**
     * 三级国标行业编码
     */
    private String thirdIndustryCode;

    /**
     * 三级国标行业名称
     */
    private String thirdIndustryName;


    /**
     * 四级国标行业编码
     */
    private String fourthIndustryCode;

    /**
     * 四级国标行业名称
     */
    private String fourthIndustryName;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 客户当前阶段：1、线索，2、商机，3、保有客户，4、流失客户
     * 需要db-es转换
     */
    private String stage;

    /**
     * 客户创建方式：1、大数据，2、商务创建，3、市场商机
     */
    private String createWay;

    /**
     * 客户来源
     */
    private String labelFrom;

    /**
     * 删除标记：1、删除，0、未删除
     */
    private String deleteFlag;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建者凭证（废弃）
     */
    private String creatorKey;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人凭证（废弃）
     */
    private String operatorKey;

    /**
     * 更新人
     */
    private String operator;

    /**
     * 无效标记：1、无效，0、有效
     */
    private String invalidFlag;

    /**
     * 是否是外贸用户
     */
    private String tagWaimao;

    /**
     * 是否门户连带客户 0:否，1:是
     */
    private String tagMenhuRelated;

    /**
     * 是否生态转门户 0:否，1:是
     */
    private String tagEcoToMenhu;

    /**
     * 是否生态客户 0:否，1:是
     */
    private String tagEcoCust;

    /**
     * 是否数字版本门户 0:否，1:是
     */
    private String tagMenhuDigital;

    /**
     * 是否2023版本门户 0:否，1:是
     */
    private String tagMenhu2023;

    /**
     * 是否低版本门户 0:否，1:是
     * 需要db-es转换
     */
    private String tagMenhuLower;

    /**
     * 是否门户应升已升客户 0:否，1:是
     */
    private String tagMenhuUpgradeableUpgrade;

    /**
     * 是否门户应升级客户 0:否，1:是
     */
    private String tagMenhuUpgradeable;

    /**
     * 是否门户应续已续客户 0:否，1:是
     */
    private String tagMenhuRenewableRenew;

    /**
     * 是否门户应续客户 0:否，1:是
     */
    private String tagMenhuRenewable;

    /**
     * 是否交叉购买客户 0:否，1:是
     */
    private String tagCrossBuy;

    /**
     * 是否纯门户客户 0:否，1:是
     */
    private String tagPureMenhu;

    /**
     * 搜客宝 1:规模工业，2:规上服务业，3:规上建筑业，4:规上批发零售业，5:规上住宿餐饮业，6:规上房地产开发与经营业
     */
    private String tagFlag7;

    /**
     * 搜客宝 行业 1:律师，2:学校，3:医院
     */
    private String tagFlag8;

    /**
     * 科技型企业
     */
    private String tagTechcompany;

    /**
     * 门户新客户首次购买时间
     */
    private String tagMenhuNewTime;

    /**
     * 门户新客户首次购买产品类别 0:低版本 1:门户2023 2:数字门户
     */
    private String tagMenhuNewCategory;

    /**
     * 是否是报价客户 0:否 1:是
     */
    private String tagQuoteCust;

    /**
     * 推荐客户的id
     */
    private String recommendCustId;

    /**
     * 推荐客户创建时间
     */
    private String recommendCustCreateTime;


    /**
     * 是否保有客户 0:否，1:是
     */
    private String tagRetainCust;

    /**
     * 到期时间
     */
    private String tagRetainTime;

    /**
     * 是否流失客户 0:否，1:是
     */
    private String tagLostCust;

    /**
     * 流失时间
     */
    private String tagLostTime;

    /**
     * 是否转介绍新客 0:否，1:是
     */
    private String tagInvitedNewCust;

    /**
     * 是否合格新客 0:否，1:是
     */
    private String tagQualifiedNewCust;

    /**
     * 是否合格老客0:否，1:是
     */
    private String tagQualifiedOldCust;

    /**
     * 是否低价值客户 0:否 1:是
     */
    private String tagLowValue;

    /**
     * 所属商务id
     * 需要db-es转换
     */
    private String salerId;

    /**
     * 部门id
     * 需要db-es转换
     */
    private String deptId;

    /**
     * 分公司ID
     * 需要db-es转换
     */
    private String subId;

    /**
     * 区域ID
     * 需要db-es转换
     */
    private String areaId;

    /**
     * 状态值（1、保护；2、总监；3、经理；4、客户池）
     * 需要db-es转换
     */
    private String status;

    /**
     * 客户阶段。(2：保护跟进；3：网站客户；4：非网站客户)
     * 需要db-es转换
     */
    private String tradeProductType;

    /**
     * 阶段性保护时间
     * 需要db-es转换
     */
    private String protectTime;

    /**
     * 已保护天数(仅2023年以后数据)
     */
    private String protectDay;

    /**
     * 本月打卡次数
     * 需要db-es转换
     */
    private String clockCountMonth;

    /**
     * 累计打卡次数
     * 需要db-es转换
     */
    private String clockCount;

    /**
     * 客户订单首次支付时间
     * 需要db-es转换
     */
    private String firstPaymentTime;

    /**
     * 成为合格新客时间
     */
    private String tagQualifiedNewCustTime;

    private String tagFlag12;

    /**
     * 是否是大客户（ka）新
     */
    private String tagDakehu;

    /**
     * 客户分层
     */
    private String customerLayer;

    /**
     * 客户等级
     */
    private String customerGrade;

    /**
     * CDP标签user_tag_sfvwcjkh20250530 未成交客户是否是vip判断依据
     */
    private String tagWeichengjiaoVip;

    /**
     * 法人
     */
    private String legalPerson;

    /*
     * 分发时间：指跨境ABM项目，leads分发给SDR的时间
     */
    private String distributeTime;

    /*
     * 保护释放时间
     */
    private String releaseTime;

    /*
     * 线索产出时间
     */
    private String leadsCreateTime;

    /*
     * 来源编码
     */
    private String leadsSourceCode;

    /*
     * 意愿编码
     */
    private String leadsIntentCode;

    /*
     * 事业部ID
     */
    private String buId;

    /*
     * 保护结束时间
     */
    private String protectEndTime;

    /*
     * 最近拜访类型
     */
    private String lastVisitType;

    /*
     * 最近拜访时间
     */
    private String lastFollowTime;

    /*
     * 最近上门时间
     */
    private String lastSiteTime;

    /**
     * 分发渠道
     */
    private String distributeChannel;

    /**
     * 推送销售审核状态
     */
    private String reviewStatus;

    /**
     * 跟进次数
     */
    private String followCount;
    /**
     * 上门次数
     */
    private String siteCount;

    /*
     * SDR跟进时间
     */
    private String lastSdrFollowTime;

    /**
     * SDR跟进次数
     */
    private String sdrFollowCount;

    /**
     * 回执终止时间
     */
    private String receiptEndTime;

    /**
     * 标签得分
     */
    private String tagScore;

    /**
     * 客户标签列表
     */
    private String tagList;

    /**
     * 页号
     */
    private Integer pageNum = 1;

    /**
     * 页码
     */
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 是否正序
     */
    private boolean ascFlag;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public String getCertificateCode() {
        return certificateCode;
    }

    public void setCertificateCode(String certificateCode) {
        this.certificateCode = certificateCode;
    }

    public String getCheckInState() {
        return checkInState;
    }

    public void setCheckInState(String checkInState) {
        this.checkInState = checkInState;
    }

    public String getRegisterCapital() {
        return registerCapital;
    }

    public void setRegisterCapital(String registerCapital) {
        this.registerCapital = registerCapital;
    }

    public String getRegisterNo() {
        return registerNo;
    }

    public void setRegisterNo(String registerNo) {
        this.registerNo = registerNo;
    }

    public String getEnterpriseType() {
        return enterpriseType;
    }

    public void setEnterpriseType(String enterpriseType) {
        this.enterpriseType = enterpriseType;
    }

    public String getOpenStartTime() {
        return openStartTime;
    }

    public void setOpenStartTime(String openStartTime) {
        this.openStartTime = openStartTime;
    }

    public String getOpenEndTime() {
        return openEndTime;
    }

    public void setOpenEndTime(String openEndTime) {
        this.openEndTime = openEndTime;
    }

    public String getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(String approveDate) {
        this.approveDate = approveDate;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public String getRegistrationOrg() {
        return registrationOrg;
    }

    public void setRegistrationOrg(String registrationOrg) {
        this.registrationOrg = registrationOrg;
    }

    public String getFirstIndustryCode() {
        return firstIndustryCode;
    }

    public void setFirstIndustryCode(String firstIndustryCode) {
        this.firstIndustryCode = firstIndustryCode;
    }

    public String getFirstIndustryName() {
        return firstIndustryName;
    }

    public void setFirstIndustryName(String firstIndustryName) {
        this.firstIndustryName = firstIndustryName;
    }

    public String getSecondIndustryCode() {
        return secondIndustryCode;
    }

    public void setSecondIndustryCode(String secondIndustryCode) {
        this.secondIndustryCode = secondIndustryCode;
    }

    public String getSecondIndustryName() {
        return secondIndustryName;
    }

    public void setSecondIndustryName(String secondIndustryName) {
        this.secondIndustryName = secondIndustryName;
    }

    public String getThirdIndustryCode() {
        return thirdIndustryCode;
    }

    public void setThirdIndustryCode(String thirdIndustryCode) {
        this.thirdIndustryCode = thirdIndustryCode;
    }

    public String getThirdIndustryName() {
        return thirdIndustryName;
    }

    public void setThirdIndustryName(String thirdIndustryName) {
        this.thirdIndustryName = thirdIndustryName;
    }

    public String getFourthIndustryCode() {
        return fourthIndustryCode;
    }

    public void setFourthIndustryCode(String fourthIndustryCode) {
        this.fourthIndustryCode = fourthIndustryCode;
    }

    public String getFourthIndustryName() {
        return fourthIndustryName;
    }

    public void setFourthIndustryName(String fourthIndustryName) {
        this.fourthIndustryName = fourthIndustryName;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getCreateWay() {
        return createWay;
    }

    public void setCreateWay(String createWay) {
        this.createWay = createWay;
    }

    public String getLabelFrom() {
        return labelFrom;
    }

    public void setLabelFrom(String labelFrom) {
        this.labelFrom = labelFrom;
    }

    public String getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreatorKey() {
        return creatorKey;
    }

    public void setCreatorKey(String creatorKey) {
        this.creatorKey = creatorKey;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperatorKey() {
        return operatorKey;
    }

    public void setOperatorKey(String operatorKey) {
        this.operatorKey = operatorKey;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getInvalidFlag() {
        return invalidFlag;
    }

    public void setInvalidFlag(String invalidFlag) {
        this.invalidFlag = invalidFlag;
    }

    public String getTagWaimao() {
        return tagWaimao;
    }

    public void setTagWaimao(String tagWaimao) {
        this.tagWaimao = tagWaimao;
    }

    public String getTagMenhuRelated() {
        return tagMenhuRelated;
    }

    public void setTagMenhuRelated(String tagMenhuRelated) {
        this.tagMenhuRelated = tagMenhuRelated;
    }

    public String getTagEcoToMenhu() {
        return tagEcoToMenhu;
    }

    public void setTagEcoToMenhu(String tagEcoToMenhu) {
        this.tagEcoToMenhu = tagEcoToMenhu;
    }

    public String getTagEcoCust() {
        return tagEcoCust;
    }

    public void setTagEcoCust(String tagEcoCust) {
        this.tagEcoCust = tagEcoCust;
    }

    public String getTagMenhuDigital() {
        return tagMenhuDigital;
    }

    public void setTagMenhuDigital(String tagMenhuDigital) {
        this.tagMenhuDigital = tagMenhuDigital;
    }

    public String getTagMenhu2023() {
        return tagMenhu2023;
    }

    public void setTagMenhu2023(String tagMenhu2023) {
        this.tagMenhu2023 = tagMenhu2023;
    }

    public String getTagMenhuLower() {
        return tagMenhuLower;
    }

    public void setTagMenhuLower(String tagMenhuLower) {
        this.tagMenhuLower = tagMenhuLower;
    }

    public String getTagMenhuUpgradeableUpgrade() {
        return tagMenhuUpgradeableUpgrade;
    }

    public void setTagMenhuUpgradeableUpgrade(String tagMenhuUpgradeableUpgrade) {
        this.tagMenhuUpgradeableUpgrade = tagMenhuUpgradeableUpgrade;
    }

    public String getTagMenhuUpgradeable() {
        return tagMenhuUpgradeable;
    }

    public void setTagMenhuUpgradeable(String tagMenhuUpgradeable) {
        this.tagMenhuUpgradeable = tagMenhuUpgradeable;
    }

    public String getTagMenhuRenewableRenew() {
        return tagMenhuRenewableRenew;
    }

    public void setTagMenhuRenewableRenew(String tagMenhuRenewableRenew) {
        this.tagMenhuRenewableRenew = tagMenhuRenewableRenew;
    }

    public String getTagMenhuRenewable() {
        return tagMenhuRenewable;
    }

    public void setTagMenhuRenewable(String tagMenhuRenewable) {
        this.tagMenhuRenewable = tagMenhuRenewable;
    }

    public String getTagCrossBuy() {
        return tagCrossBuy;
    }

    public void setTagCrossBuy(String tagCrossBuy) {
        this.tagCrossBuy = tagCrossBuy;
    }

    public String getTagPureMenhu() {
        return tagPureMenhu;
    }

    public void setTagPureMenhu(String tagPureMenhu) {
        this.tagPureMenhu = tagPureMenhu;
    }

    public String getTagFlag7() {
        return tagFlag7;
    }

    public void setTagFlag7(String tagFlag7) {
        this.tagFlag7 = tagFlag7;
    }

    public String getTagFlag8() {
        return tagFlag8;
    }

    public void setTagFlag8(String tagFlag8) {
        this.tagFlag8 = tagFlag8;
    }

    public String getTagTechcompany() {
        return tagTechcompany;
    }

    public void setTagTechcompany(String tagTechcompany) {
        this.tagTechcompany = tagTechcompany;
    }

    public String getTagMenhuNewTime() {
        return tagMenhuNewTime;
    }

    public void setTagMenhuNewTime(String tagMenhuNewTime) {
        this.tagMenhuNewTime = tagMenhuNewTime;
    }

    public String getTagMenhuNewCategory() {
        return tagMenhuNewCategory;
    }

    public void setTagMenhuNewCategory(String tagMenhuNewCategory) {
        this.tagMenhuNewCategory = tagMenhuNewCategory;
    }

    public String getTagQuoteCust() {
        return tagQuoteCust;
    }

    public void setTagQuoteCust(String tagQuoteCust) {
        this.tagQuoteCust = tagQuoteCust;
    }

    public String getRecommendCustId() {
        return recommendCustId;
    }

    public void setRecommendCustId(String recommendCustId) {
        this.recommendCustId = recommendCustId;
    }

    public String getRecommendCustCreateTime() {
        return recommendCustCreateTime;
    }

    public void setRecommendCustCreateTime(String recommendCustCreateTime) {
        this.recommendCustCreateTime = recommendCustCreateTime;
    }

    public String getTagRetainCust() {
        return tagRetainCust;
    }

    public void setTagRetainCust(String tagRetainCust) {
        this.tagRetainCust = tagRetainCust;
    }

    public String getTagRetainTime() {
        return tagRetainTime;
    }

    public void setTagRetainTime(String tagRetainTime) {
        this.tagRetainTime = tagRetainTime;
    }

    public String getTagLostCust() {
        return tagLostCust;
    }

    public void setTagLostCust(String tagLostCust) {
        this.tagLostCust = tagLostCust;
    }

    public String getTagLostTime() {
        return tagLostTime;
    }

    public void setTagLostTime(String tagLostTime) {
        this.tagLostTime = tagLostTime;
    }

    public String getTagInvitedNewCust() {
        return tagInvitedNewCust;
    }

    public void setTagInvitedNewCust(String tagInvitedNewCust) {
        this.tagInvitedNewCust = tagInvitedNewCust;
    }

    public String getTagQualifiedNewCust() {
        return tagQualifiedNewCust;
    }

    public void setTagQualifiedNewCust(String tagQualifiedNewCust) {
        this.tagQualifiedNewCust = tagQualifiedNewCust;
    }

    public String getTagQualifiedOldCust() {
        return tagQualifiedOldCust;
    }

    public void setTagQualifiedOldCust(String tagQualifiedOldCust) {
        this.tagQualifiedOldCust = tagQualifiedOldCust;
    }

    public String getTagLowValue() {
        return tagLowValue;
    }

    public void setTagLowValue(String tagLowValue) {
        this.tagLowValue = tagLowValue;
    }

    public String getSalerId() {
        return salerId;
    }

    public void setSalerId(String salerId) {
        this.salerId = salerId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTradeProductType() {
        return tradeProductType;
    }

    public void setTradeProductType(String tradeProductType) {
        this.tradeProductType = tradeProductType;
    }

    public String getProtectTime() {
        return protectTime;
    }

    public void setProtectTime(String protectTime) {
        this.protectTime = protectTime;
    }

    public String getProtectDay() {
        return protectDay;
    }

    public void setProtectDay(String protectDay) {
        this.protectDay = protectDay;
    }

    public String getClockCountMonth() {
        return clockCountMonth;
    }

    public void setClockCountMonth(String clockCountMonth) {
        this.clockCountMonth = clockCountMonth;
    }

    public String getClockCount() {
        return clockCount;
    }

    public void setClockCount(String clockCount) {
        this.clockCount = clockCount;
    }

    public String getFirstPaymentTime() {
        return firstPaymentTime;
    }

    public void setFirstPaymentTime(String firstPaymentTime) {
        this.firstPaymentTime = firstPaymentTime;
    }

    public String getTagQualifiedNewCustTime() {
        return tagQualifiedNewCustTime;
    }

    public void setTagQualifiedNewCustTime(String tagQualifiedNewCustTime) {
        this.tagQualifiedNewCustTime = tagQualifiedNewCustTime;
    }

    public String getTagFlag12() {
        return tagFlag12;
    }

    public void setTagFlag12(String tagFlag12) {
        this.tagFlag12 = tagFlag12;
    }

    public String getTagDakehu() {
        return tagDakehu;
    }

    public void setTagDakehu(String tagDakehu) {
        this.tagDakehu = tagDakehu;
    }

    public String getCustomerLayer() {
        return customerLayer;
    }

    public void setCustomerLayer(String customerLayer) {
        this.customerLayer = customerLayer;
    }

    public String getCustomerGrade() {
        return customerGrade;
    }

    public void setCustomerGrade(String customerGrade) {
        this.customerGrade = customerGrade;
    }

    public String getTagWeichengjiaoVip() {
        return tagWeichengjiaoVip;
    }

    public void setTagWeichengjiaoVip(String tagWeichengjiaoVip) {
        this.tagWeichengjiaoVip = tagWeichengjiaoVip;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public boolean isAscFlag() {
        return ascFlag;
    }

    public void setAscFlag(boolean ascFlag) {
        this.ascFlag = ascFlag;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getEstablishDate() {
        return establishDate;
    }

    public void setEstablishDate(String establishDate) {
        this.establishDate = establishDate;
    }

    public String getDistributeTime() {
        return distributeTime;
    }

    public void setDistributeTime(String distributeTime) {
        this.distributeTime = distributeTime;
    }

    public String getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(String releaseTime) {
        this.releaseTime = releaseTime;
    }

    public String getLeadsCreateTime() {
        return leadsCreateTime;
    }

    public void setLeadsCreateTime(String leadsCreateTime) {
        this.leadsCreateTime = leadsCreateTime;
    }

    public String getLeadsSourceCode() {
        return leadsSourceCode;
    }

    public void setLeadsSourceCode(String leadsSourceCode) {
        this.leadsSourceCode = leadsSourceCode;
    }

    public String getLeadsIntentCode() {
        return leadsIntentCode;
    }

    public void setLeadsIntentCode(String leadsIntentCode) {
        this.leadsIntentCode = leadsIntentCode;
    }

    public String getBuId() {
        return buId;
    }

    public void setBuId(String buId) {
        this.buId = buId;
    }

    public String getProtectEndTime() {
        return protectEndTime;
    }

    public void setProtectEndTime(String protectEndTime) {
        this.protectEndTime = protectEndTime;
    }

    public String getLastVisitType() {
        return lastVisitType;
    }

    public void setLastVisitType(String lastVisitType) {
        this.lastVisitType = lastVisitType;
    }

    public String getLastFollowTime() {
        return lastFollowTime;
    }

    public void setLastFollowTime(String lastFollowTime) {
        this.lastFollowTime = lastFollowTime;
    }

    public String getLastSiteTime() {
        return lastSiteTime;
    }

    public void setLastSiteTime(String lastSiteTime) {
        this.lastSiteTime = lastSiteTime;
    }

    public String getDistributeChannel() {
        return distributeChannel;
    }

    public void setDistributeChannel(String distributeChannel) {
        this.distributeChannel = distributeChannel;
    }

    public String getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(String reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getFollowCount() {
        return followCount;
    }

    public void setFollowCount(String followCount) {
        this.followCount = followCount;
    }

    public String getSiteCount() {
        return siteCount;
    }

    public void setSiteCount(String siteCount) {
        this.siteCount = siteCount;
    }

    public String getLastSdrFollowTime() {
        return lastSdrFollowTime;
    }

    public void setLastSdrFollowTime(String lastSdrFollowTime) {
        this.lastSdrFollowTime = lastSdrFollowTime;
    }

    public String getSdrFollowCount() {
        return sdrFollowCount;
    }

    public void setSdrFollowCount(String sdrFollowCount) {
        this.sdrFollowCount = sdrFollowCount;
    }

    public String getReceiptEndTime() {
        return receiptEndTime;
    }

    public void setReceiptEndTime(String receiptEndTime) {
        this.receiptEndTime = receiptEndTime;
    }

    public String getTagList() {
        return tagList;
    }

    public void setTagList(String tagList) {
        this.tagList = tagList;
    }

    public String getTagScore() {
        return tagScore;
    }

    public void setTagScore(String tagScore) {
        this.tagScore = tagScore;
    }
}