package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;

import java.io.Serializable;

/**
 * 客户保存dubbo参数
 * <AUTHOR>
 * @date 2024/2/29 09:55
 * @version 1.0.0
 */
public class EnterpriseCustomerSaveDubboDto extends SignData implements Serializable {
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户当前阶段：1、线索，2、商机，3、保有客户，4、流失客户
     */
    private Integer presentStage;

    /**
     * 客户创建方式：1、大数据，2、商务创建，3、市场商机，4、会销
     */
    private Integer createWay;

    /**
     * 操作人ID
     */
    private String operator;

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getPresentStage() {
        return presentStage;
    }

    public void setPresentStage(Integer presentStage) {
        this.presentStage = presentStage;
    }

    public Integer getCreateWay() {
        return createWay;
    }

    public void setCreateWay(Integer createWay) {
        this.createWay = createWay;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}