package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;

import java.io.Serializable;

/**
 * @version 1.0
 * @Description: 客户绑定微信unionId dto
 * @Author: lijinpeng
 * @Date: 2025/2/11 14:21
 */
public class CustomerBindUnionIdDubboDto extends SignData implements Serializable {

    /**
     * PID
     */
    private String pid;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 绑定类型：0、无，1、微信，2、企业微信
     */
    private Integer bindType;

    /**
     * 微信unionId
     */
    private String unionId;

    /**
     * 微信昵称
     */
    private String wechatNickName;

    /**
     * 操作人
     */
    private String operator;

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public Integer getBindType() {
        return bindType;
    }

    public void setBindType(Integer bindType) {
        this.bindType = bindType;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getWechatNickName() {
        return wechatNickName;
    }

    public void setWechatNickName(String wechatNickName) {
        this.wechatNickName = wechatNickName;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
}
