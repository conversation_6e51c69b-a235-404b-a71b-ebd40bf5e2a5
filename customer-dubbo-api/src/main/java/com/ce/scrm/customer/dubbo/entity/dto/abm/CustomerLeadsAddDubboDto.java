package com.ce.scrm.customer.dubbo.entity.dto.abm;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 客户leads
 *
 * @TableName customer_leads
 */
@Data
public class CustomerLeadsAddDubboDto extends SignData implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 数据来源 0:crm原有渠道  1:营销活动
     */
    @NotNull(message = "数据来源不能为空")
    private Integer dataFromSource;

    /**
     * 客户id
     */
    @NotBlank(message = "客户id不能为空")
    private String customerId;

    /**
     * leads类别
     */
    @NotBlank(message = "leads类别不能为空")
    private String leadsType;

    /**
     * leads code
     */
    @NotBlank(message = "leads code不能为空")
    private String leadsCode;

    /**
     * leads 来源
     */
    @NotBlank(message = "leads来源不能为空")
    private String leadsSource;

    /**
     * leads url
     */
    private String leadsUrl;

    /**
     * Leads来源说明
     */
    private String leadsDesc;

    /**
     * 创建者
     */
    private String createdId;

    /**
     * 创建时间
     */
    private Date createTime;

    private static final long serialVersionUID = 1L;
}