package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 添加联系人参数
 * <AUTHOR>
 * @date 2023/5/15 11:23
 * @version 1.0.0
 **/
public class CustomerShareQueryDubboDto extends SignData implements Serializable {

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * ge筛选
     */
    private Date startTime;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
}
