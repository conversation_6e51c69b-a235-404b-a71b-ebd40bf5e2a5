package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;

import java.io.Serializable;

/**
 * 签单联系人更新参数 所有参数都要传
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/9/5
 **/
public class SigningPersonUpdateDubboDto extends SignData implements Serializable {
    /**
     * 联系人ID 必传
     */
    private String contactPersonId;
    /**
     * 客户ID 必传
     */
    private String customerId;
    /**
     * 手机号 必传
     */
    private String phone;
    /**
     * 操作人 必传
     */
    private String operator;
    /**
     * 签单联系人 0、不是，1、临时，2、正式  必传
     */
    private Integer signingPersonFlag;

    public String getContactPersonId() {
        return contactPersonId;
    }

    public void setContactPersonId(String contactPersonId) {
        this.contactPersonId = contactPersonId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Integer getSigningPersonFlag() {
        return signingPersonFlag;
    }

    public void setSigningPersonFlag(Integer signingPersonFlag) {
        this.signingPersonFlag = signingPersonFlag;
    }
}
