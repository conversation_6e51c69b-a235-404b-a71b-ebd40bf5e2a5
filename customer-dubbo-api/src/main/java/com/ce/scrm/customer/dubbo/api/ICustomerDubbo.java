package com.ce.scrm.customer.dubbo.api;

import com.ce.scrm.customer.dubbo.entity.dto.*;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.*;

import java.util.List;

/**
 * 客户dubbo接口
 * <AUTHOR>
 * @date 2023/4/12 16:54
 * @version 1.0.0
 */
public interface ICustomerDubbo {

    /**
     * 获取客户
     * @param customerDetailDubboDto  客户查询参数
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
     **/
    DubboResult<CustomerDubboView> detail(CustomerDetailDubboDto customerDetailDubboDto);

    /**
     * 分页查询客户信息
     * @param customerPageDubboDto 客户分页参数
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
     **/
    DubboResult<DubboPageInfo<CustomerDubboView>> pageList(CustomerPageDubboDto customerPageDubboDto);

    /**
     * 根据条件查询客户信息列表
     * @param customerConditionDubboDto
     * @return
     */
    DubboResult<List<CustomerDubboView>> findByCondition(CustomerConditionDubboDto customerConditionDubboDto);

    /**
     * 添加客户
     * @param customerAddDubboDto 客户数据
     * <AUTHOR>
     * @date 2023/5/25 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerAddDubboView>
     **/
    DubboResult<CustomerAddDubboView> add(CustomerAddDubboDto customerAddDubboDto);

    /**
     * 保存企业客户
     * @param enterpriseCustomerSaveDubboDto  企业客户保存参数
     * <AUTHOR>
     * @date 2024/2/29 10:00
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerAddDubboView>
     **/
    DubboResult<CustomerAddDubboView> saveEnterpriseCustomer(EnterpriseCustomerSaveDubboDto enterpriseCustomerSaveDubboDto);

    /**
     * 修改客户
     * @param customerUpdateDubboDto  客户
     * <AUTHOR>
     * @date 2023/5/25 11:26
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     **/
    DubboResult<Boolean> update(CustomerUpdateDubboDto customerUpdateDubboDto);

    /**
     * elasticsearch 模糊查询
     * @author: wangshoufang
     * @date: 2023/9/11 09:02
     * @param customerPageDubboDto
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo < com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>>
     */
    DubboResult<DubboPageInfo<CustomerDubboView>> elasticsearchMatchByCondition(CustomerPageDubboDto customerPageDubboDto);

    /**
     * <AUTHOR>
     * @date 2025/7/14 09:39:15
     * @return
     * @desc 从customer es库根据条件分页查询客户数据
     */
    DubboResult<DubboPageInfo<CustomerESDubboView>> customerESMatchByCondition(CustomerESPageDubboDto customerESPageDubboDto);

    /**
     * elasticsearch 精确查询
     * @author: wangshoufang
     * @date: 2023/9/11 09:02
     * @param customerPageDubboDto
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
     */
    DubboResult<CustomerDubboView> elasticsearchExactByCondition(CustomerPageDubboDto customerPageDubboDto);

    /**
     * 判断一个客户的联系人，是否是法人
     *
     *  @param customerId 客户ID
     *  @param contactName 联系人姓名
     *  <AUTHOR>
     *  @date 2024/9/10
     *  @return boolean 是否法人
     */
    DubboResult<Boolean> isLegalPerson(String customerId, String contactName);

    /**
     * 获取首页拜访率
     * @param loginInfo
     * @return
     */
    DubboResult<HomePageCallRateDubboView> getCallRate(LoginInfoDubboDto loginInfo);

    /**
     * 获取打卡内页列表
     * @param callDetailsDubboDto
     * @return
     */
    DubboResult<DubboPageInfo<CustomerDubboView>> getCallDetails(CallDetailsDubboDto callDetailsDubboDto);

    /**
     * 监听保护关系表binlog 然后同步customer
     * @param
     * @return
     */
    DubboResult<Boolean> monitorProtectInfoConsumer(CustomerUpdateDubboDto updateDubboDto);

    /**
     * 更新客户表数据 只更新CustType、bindFlag、businessOpportunityConfirmationFlag
     * @param
     * @return
     */
    DubboResult<Boolean> updateCustomerCustType(CustomerUpdateDubboDto updateDubboDto);

    /***
     * 查询合格新客
     * @param customerPageDubboDto
     * <AUTHOR>
     * @date 2025/1/6 20:44
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>>
    **/
    DubboResult<DubboPageInfo<CustomerDubboView>> qualifiedNewCustomerList(CustomerPageDubboDto customerPageDubboDto);

    /*
     * @Description 客户绑定微信unionId
     * <AUTHOR>
     * @date 2025/2/11 14:25
     * @param customerBindUnionIdDubboDto
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Boolean>
     */
    DubboResult<Boolean> bindUnionId(CustomerBindUnionIdDubboDto customerBindUnionIdDubboDto);

    DubboResult<Boolean> bindUnionIdDataHandle(CustomerBindUnionIdDubboDto customerBindUnionIdDubboDto);

    /**
     * 根据客户id 获取 关于客户的分享数据
     * @param queryDubboDto
     * @return
     */
    DubboResult<List<CustomerShareTemporaryDubboView>> getCustomerShareTemporaryByCustomerId(CustomerShareQueryDubboDto queryDubboDto);

    /**
     * Description: 按地区统计 tag_menhu=1（【销管口径】门户客户）的客户数
     *              国家无地区码，用"000000”指代国家码
     * @author: JiuDD
     * @param dubboDto 统计颗粒度:1=全国;2=省;3=市
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.CompanyDistributionSummaryBusinessView>
     * date: 2025/6/27 11:00
     */
    DubboResult<List<CompanyDistributionSummaryDubboView>> zqCompanyDistributionSummary(CompanyDistributionSummaryDubboDto dubboDto);

}