package com.ce.scrm.customer.dubbo.api;

import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerLeadsAddDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerLeadsPageDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerLeadsDubboView;

import java.util.List;

/**
 * 客户Leads dubbo接口
 * <AUTHOR>
 * @date 2023/4/12 16:54
 * @version 1.0.0
 */
public interface ICustomerLeadsDubbo {

    /***
     * 添加单条leads
     * @param customerLeadsAddDubboDto
     * <AUTHOR>
     * @date 2025/7/17 10:44
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.Long>
     **/
    DubboResult<Long> addCustomerLead(CustomerLeadsAddDubboDto customerLeadsAddDubboDto);

    /***
     * 获取集合
     * @param customerId
     * <AUTHOR>
     * @date 2025/7/17 10:54
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.customer.dubbo.entity.view.abm.CustomerLeadsDubboView>>
     **/
    DubboResult<List<CustomerLeadsDubboView>> getListByCustomerId(String customerId);

	/**
	 * 获取leads分页
	 * @param customerLeadsPageDubboDto 客户id及分页信息
	 * <AUTHOR>
	 * @date 2025/7/17 10:54
	 * @version 1.0.0
	 * @return DubboResult<DubboPageInfo<CustomerLeadsDubboView>>
	 **/
	DubboResult<DubboPageInfo<CustomerLeadsDubboView>> getPageByCustomerId(CustomerLeadsPageDubboDto customerLeadsPageDubboDto);
}
