package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;

import java.io.Serializable;
import java.util.List;

/**
 * 联系方式数据
 * <AUTHOR>
 * @date 2023/4/12 17:37
 * @version 1.0.0
 */
public class ContactInfoDubboDto extends SignData implements Serializable {

    /**
     * 联系方式id
     */
    private String contactInfoId;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 联系类型：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
     */
    private Integer contactType;

    /**
     * 联系类型列表：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
     */
    private List<Integer> contactTypeList;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 联系方式
     */
    private List<String> contactWayList;

    /**
     * 手机号标识：1、正常，2、黑名单，3、空号
     */
    private Integer phoneFlag;

    /**
     * 是否是签单人手机号：1、是，0、否
     */
    private Integer signatoryFlag;

    /**
     * 是否是300会员手机号：1、是，0、否
     */
    private Integer memberFlag;

    public Integer getContactType() {
        return contactType;
    }

    public void setContactType(Integer contactType) {
        this.contactType = contactType;
    }

    public String getContactWay() {
        return contactWay;
    }

    public void setContactWay(String contactWay) {
        this.contactWay = contactWay;
    }

    public Integer getPhoneFlag() {
        return phoneFlag;
    }

    public void setPhoneFlag(Integer phoneFlag) {
        this.phoneFlag = phoneFlag;
    }

    public Integer getSignatoryFlag() {
        return signatoryFlag;
    }

    public void setSignatoryFlag(Integer signatoryFlag) {
        this.signatoryFlag = signatoryFlag;
    }

    public Integer getMemberFlag() {
        return memberFlag;
    }

    public void setMemberFlag(Integer memberFlag) {
        this.memberFlag = memberFlag;
    }

    public String getContactInfoId() {
        return contactInfoId;
    }

    public void setContactInfoId(String contactInfoId) {
        this.contactInfoId = contactInfoId;
    }

    public String getContactPersonId() {
        return contactPersonId;
    }

    public void setContactPersonId(String contactPersonId) {
        this.contactPersonId = contactPersonId;
    }

    public List<Integer> getContactTypeList() {
        return contactTypeList;
    }

    public void setContactTypeList(List<Integer> contactTypeList) {
        this.contactTypeList = contactTypeList;
    }

    public List<String> getContactWayList() {
        return contactWayList;
    }

    public void setContactWayList(List<String> contactWayList) {
        this.contactWayList = contactWayList;
    }
}