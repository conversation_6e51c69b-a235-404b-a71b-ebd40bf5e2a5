package com.ce.scrm.customer.dubbo.entity.dto;

import com.ce.scrm.customer.dubbo.entity.base.SignData;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 绑定unionId查询参数
 * <AUTHOR>
 * @date 2023/5/23 09:41
 * @version 1.0.0
 **/
public class BindUnionIdQueryDubboDto extends SignData implements Serializable {

    /**
     * 员工ID
     */
    private List<String> empIdList;

    /**
     * 微信unionId
     */
    private List<String> unionIdList;


    /**
     * unionId绑定开始时间
     */
    private LocalDateTime unionIdBindBeginTime;


    /**
     * unionId绑定结束时间
     */
    private LocalDateTime unionIdBindEndTime;

    private Integer pageNum;

    private Integer pageSize;

    public List<String> getEmpIdList() {
        return empIdList;
    }

    public void setEmpIdList(List<String> empIdList) {
        this.empIdList = empIdList;
    }

    public List<String> getUnionIdList() {
        return unionIdList;
    }

    public void setUnionIdList(List<String> unionIdList) {
        this.unionIdList = unionIdList;
    }

    public LocalDateTime getUnionIdBindBeginTime() {
        return unionIdBindBeginTime;
    }

    public void setUnionIdBindBeginTime(LocalDateTime unionIdBindBeginTime) {
        this.unionIdBindBeginTime = unionIdBindBeginTime;
    }

    public LocalDateTime getUnionIdBindEndTime() {
        return unionIdBindEndTime;
    }

    public void setUnionIdBindEndTime(LocalDateTime unionIdBindEndTime) {
        this.unionIdBindEndTime = unionIdBindEndTime;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
