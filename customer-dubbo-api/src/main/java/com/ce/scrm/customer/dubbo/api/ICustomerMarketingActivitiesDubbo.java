package com.ce.scrm.customer.dubbo.api;

import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerMarketingActivitiesCreateDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerMarketingActivitiesPageDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.CustomerMarketingActivitiesUpdateDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerMarketingActivitiesCreatorSelectDubboView;
import com.ce.scrm.customer.dubbo.entity.view.abm.CustomerMarketingActivitiesDubboView;

import java.util.List;

/**
 * 跨境ABM 客户营销活动dubbo接口
 * <AUTHOR>
 * @date 2025/7/09 16:54
 * @version 1.0.0
 */
public interface ICustomerMarketingActivitiesDubbo {

	/**
	 * 创建营销活动
	 * @param customerDetailDubboDto 创建营销活动参数
	 * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
	 * <AUTHOR>
	 * @date 2025/7/9 15:39
	 **/
	DubboResult<?> createActivity(CustomerMarketingActivitiesCreateDubboDto customerDetailDubboDto);

	/**
	 * 修改营销活动
	 * @param updateDubboDto 修改营销活动参数
	 * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
	 * <AUTHOR>
	 * @date 2025/7/9 15:39
	 **/
	DubboResult<?> updateActivity(CustomerMarketingActivitiesUpdateDubboDto updateDubboDto);

	/**
	 * 营销活动列表
	 * @param pageDubboDto 查询参数
	 * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>>
	 * <AUTHOR>
	 * @date 2025/7/9 17:25
	 */
	DubboResult<DubboPageInfo<CustomerMarketingActivitiesDubboView>> activitiesPages(CustomerMarketingActivitiesPageDubboDto pageDubboDto);

	/**
	 * 营销活动详情
	 * @param activitiesId 营销活动id
	 * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
	 * <AUTHOR>
	 * @date 2025/7/9 17:25
	 */
	DubboResult<CustomerMarketingActivitiesDubboView> activityDetail(Long activitiesId);

	/**
	 * 下发后，修改下发状态
	 * @param customerId 客户id
	 * @return 是否下发成功
	 */
	DubboResult<Boolean> updateDistributeState(String customerId);

	/**
	 * 所有活动创建人的下拉框
	 */
	DubboResult<List<CustomerMarketingActivitiesCreatorSelectDubboView>> creatorSelect();


}