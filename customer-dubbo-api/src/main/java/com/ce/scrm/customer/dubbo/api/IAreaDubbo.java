package com.ce.scrm.customer.dubbo.api;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.AreaDubboView;

import java.util.List;

/**
 * 客户dubbo接口
 * <AUTHOR>
 * @date 2023/4/12 16:54
 * @version 1.0.0
 */
public interface IAreaDubbo {

    /**
     * 根据所有区域数据
     * @param signData 签名数据
     * <AUTHOR>
     * @date 2023/4/7 20:59
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.AreaDubboView>
     **/
    DubboResult<List<AreaDubboView>> getAllData(SignData signData);

}