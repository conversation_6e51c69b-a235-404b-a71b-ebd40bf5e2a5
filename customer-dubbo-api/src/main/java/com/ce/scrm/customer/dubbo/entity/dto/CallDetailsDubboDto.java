package com.ce.scrm.customer.dubbo.entity.dto;

import java.io.Serializable;

public class CallDetailsDubboDto extends LoginInfoDubboDto implements Serializable {

    /**
     * 筛选天
     */
    private Integer dayEnum;

    /**
     * 本月打卡次数筛选
     */
    private Integer currentMonthClockCountEnum;

    /**
     * 打卡次数筛选
     */
    private Integer clockCountEnum;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;


    public Integer getDayEnum() {
        return dayEnum;
    }

    public void setDayEnum(Integer dayEnum) {
        this.dayEnum = dayEnum;
    }

    public Integer getCurrentMonthClockCountEnum() {
        return currentMonthClockCountEnum;
    }

    public void setCurrentMonthClockCountEnum(Integer currentMonthClockCountEnum) {
        this.currentMonthClockCountEnum = currentMonthClockCountEnum;
    }

    public Integer getClockCountEnum() {
        return clockCountEnum;
    }

    public void setClockCountEnum(Integer clockCountEnum) {
        this.clockCountEnum = clockCountEnum;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getSalerId() {
        return salerId;
    }

    public void setSalerId(String salerId) {
        this.salerId = salerId;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
