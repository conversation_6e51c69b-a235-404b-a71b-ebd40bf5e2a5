package com.ce.scrm.customer.dubbo.entity.dto.abm;

import com.ce.scrm.customer.dubbo.entity.base.SignData;
import lombok.Data;

import java.io.Serializable;

/**
 * 客户leads
 *
 * @TableName customer_leads
 */
@Data
public class PotentialCustomerMarketingRulesDubboDto extends SignData implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 等级code A、B、C、D
	 */
	private String intentCode;

	/**
	 * 类别名称
	 */
	private String intentName;

	/**
	 * 二级来源code
	 */
	private String sourceCode;

	/**
	 * 来源名称
	 */
	private String sourceName;

	/**
	 * 分配角色: SDR、CC、用户id、不分配
	 */
	private String allocateRole;

	/**
	 * leads来源说明
	 */
	private String sourceDesc;

	/**
	 * 字典名称
	 */
	private Integer dictName;

	/**
	 * 字典类型 1-线索类型 2-线索渠道
	 */
	private Integer dictType;

	/**
	 * 识别码
	 */
	private String identifierCode;

	/**
	 * 网页位置标签
	 */
	private String locateTag;

	/**
	 * leads摘要（四要素）
	 */
	private Integer leadsSummaries;

	/**
	 * 更多详情
	 */
	private String moreInfo;

	/**
	 * 状态 0-暂无 1-新增 2-已有
	 */
	private Integer state;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 标签分类id（D类标签对应的分类id）
	 */
	private Long tagCategoryId;

}