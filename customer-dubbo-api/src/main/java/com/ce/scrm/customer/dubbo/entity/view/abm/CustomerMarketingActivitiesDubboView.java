package com.ce.scrm.customer.dubbo.entity.view.abm;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户营销活动业务数据
 * <AUTHOR>
 * @date 2025/7/9 16:11
 * @version 1.0.0
 */
@Data
public class CustomerMarketingActivitiesDubboView implements Serializable {

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 活动编码
	 */
	private String activityCode;

	/**
	 * 活动名称
	 */
	private String activityName;

	/**
	 * 活动类型/触达方式 1-短信 2-邮件 3-电话 4-其他
	 */
	private Integer activityType;

	/**
	 * 模板id
	 */
	private String templateId;

	/**
	 * 活动内容/触达内容
	 */
	private String activityContent;

	/**
	 * 关联活动链接
	 */
	private String activityUrl;

	/**
	 * 客群id
	 */
	private String segmentId;

	/**
	 * 客群人数
	 */
	private Integer segmentCustCount;

	/**
	 * 客群名称
	 */
	private String segmentName;

	/**
	 * 执行状态 0-待执行 1-执行中 2-执行成功 3-执行失败
	 */
	private Integer executeState;

	/**
	 * 执行时间
	 */
	private Date executeTime;

	/**
	 * 创建人ID
	 */
	private String createBy;

	/**
	 * 创建人名称
	 */
	private String createName;

	/**
	 * 修改人ID
	 */
	private String updateBy;

	/**
	 * 创建时间
	 */
	private Date createdTime;

	/**
	 * 更新时间
	 */
	private Date updatedTime;
}