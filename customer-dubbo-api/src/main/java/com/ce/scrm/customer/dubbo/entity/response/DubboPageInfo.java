package com.ce.scrm.customer.dubbo.entity.response;

import java.io.Serializable;
import java.util.List;

/**
 * web返回分页信息
 * <AUTHOR>
 * @date 2021/6/2 上午11:02
 * @version 1.0.0
 **/
public class DubboPageInfo<T> implements Serializable {

    private static final long serialVersionUID = -6455234157678276815L;
    protected long total;
    private long pageNum;
    private long pageSize;
    private long pages;

    protected List<T> list;


    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public long getPageNum() {
        return pageNum;
    }

    public void setPageNum(long pageNum) {
        this.pageNum = pageNum;
    }

    public long getPageSize() {
        return pageSize;
    }

    public void setPageSize(long pageSize) {
        this.pageSize = pageSize;
    }

    public long getPages() {
        return pages;
    }

    public void setPages(long pages) {
        this.pages = pages;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
}
