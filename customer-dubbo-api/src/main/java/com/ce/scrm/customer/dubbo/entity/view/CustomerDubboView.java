package com.ce.scrm.customer.dubbo.entity.view;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 客户数据
 * <AUTHOR>
 * @date 2023/4/12 16:48
 * @version 1.0.0
 */
public class CustomerDubboView implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 源数据ID
     */
    private String sourceDataId;

    /**
     * 客户类型：1、国内企业，2、个人，3、国外及港澳台
     */
    private Integer customerType;

    /**
     * 客户/企业名称
     */
    private String customerName;

    /**
     * 证件类型：1、营业执照，2、身份证，3、军人身份证，4、护照，5、港澳通行证
     */
    private Integer certificateType;

    /**
     * 证件编码
     */
    private String certificateCode;

    /**
     * 登记状态：1、在营/存续，2、迁入/迁出，3、吊销/撤销，4、注销，5、停业，6、其他
     */
    private String checkInState;

    /**
     * 成立日期
     */
    private Date establishDate;

    /**
     * 注册资本
     */
    private String registerCapital;

    /**
     * 实缴资本
     */
    private String paidInCapital;

    /**
     * 组织机构代码
     */
    private String organizationCode;

    /**
     * 工商注册号 
     */
    private String registerNo;

    /**
     * 纳税人识别号
     */
    private String taxpayerNo;

    /**
     * 企业类型：1、民营，2、国有企业，3、外资、中外合资，4、港、澳、台投资，5、工商个体户，6、其他企业，7、社会组织
     */
    private String enterpriseType;

    /**
     * 营业开始时间
     */
    private Date openStartTime;

    /**
     * 营业结束时间
     */
    private Date openEndTime;

    /**
     * 纳税人资质
     */
    private String taxpayerQualification;

    /**
     * 人员规模
     */
    private String staffScale;

    /**
     * 参保人数
     */
    private String insuredNum;

    /**
     * 核准日期
     */
    private Date approveDate;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 登记机关
     */
    private String registrationAuthority;

    /**
     * 进出口企业代码
     */
    private String importExportEnterpriseCode;

    /**
     * 一级国标行业编码
     */
    private String firstIndustryCode;

    /**
     * 一级国标行业名称
     */
    private String firstIndustryName;

    /**
     * 二级国标行业编码
     */
    private String secondIndustryCode;

    /**
     * 二级国标行业名称
     */
    private String secondIndustryName;

    /**
     * 三级国标行业编码
     */
    private String thirdIndustryCode;
    /**
     * 三级国标行业名称
     */
    private String thirdIndustryName;
    /**
     * 四级国标行业编码
     */
    private String fourthIndustryCode;
    /**
     * 四级国标行业名称
     */
    private String fourthIndustryName;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 客户当前阶段：1、线索，2、商机，3、保有客户，4、流失客户
     */
    private Integer presentStage;

    /**
     * 客户创建方式
     */
    private Integer createWay;

    /**
     * 删除标记：1、删除，0、未删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者凭证
     */
    private String creatorKey;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人凭证
     */
    private String operatorKey;

    /**
     * 更新人
     */
    private String operator;

    /**
     * 来源
     */
    private String labelFrom;

    /**
     * 联系人列表
     */
    List<ContactPersonDubboView> contactPersonDubboViewList;

    /**
     * 是否是ka用户
     */
    private Integer tagKa;

    /**
     * 是否是外贸用户
     */
    private Integer tagWaimao;

    /**
     * 是否门户连带客户 0:否，1:是
     */
    private Integer tagMenhuRelated;

    /**
     * 是否生态转门户 0:否，1:是
     */
    private Integer tagEcoToMenhu;

    /**
     * 是否生态客户 0:否，1:是
     */
    private Integer tagEcoCust;

    /**
     * 是否数字版本门户 0:否，1:是
     */
    private Integer tagMenhuDigital;

    /**
     * 是否2023版本门户 0:否，1:是
     */
    private Integer tagMenhu2023;

    /**
     * 是否低版本门户 0:否，1:是
     */
    private Integer tagMenhuLowver;

    /**
     * 是否门户应升已升客户 0:否，1:是
     */
    private Integer tagMenhuUpgradeableUpgrade;

    /**
     * 是否门户应升级客户 0:否，1:是
     */
    private Integer tagMenhuUpgradeable;

    /**
     * 是否门户应续已续客户 0:否，1:是
     */
    private Integer tagMenhuRenewableRenew;

    /**
     * 是否门户应续客户 0:否，1:是
     */
    private Integer tagMenhuRenewable;

    /**
     * 是否交叉购买客户 0:否，1:是
     */
    private Integer tagCrossBuy;

    /**
     * 是否纯门户客户 0:否，1:是
     */
    private Integer tagPureMenhu;

    /**
     * 搜客宝 1:规模工业，2:规上服务业，3:规上建筑业，4:规上批发零售业，5:规上住宿餐饮业，6:规上房地产开发与经营业
     */
    private String tagFlag7;

    /**
     * 搜客宝 行业 1:律师，2:学校，3:医院
     */
    private String tagFlag8;

    /**
     * 搜客宝 科技型企业
     */
    private String tagTechcompany;
    /**
     * 是否是报价客户 0:否 1:是
     */
    private Integer tagQuoteCust;

    /**
     * 推荐客户的id
     */
    private String recommendCustId;

    /**
     * 是否保有客户 0:否，1:是
     */
    private Integer tagRetainCust;

    /**
     * 到期时间
     */
    private Date tagRetainTime;

    /**
     * 是否流失客户 0:否，1:是
     */
    private Integer tagLostCust;

    /**
     * 流失时间
     */
    private Date tagLostTime;

    /**
     * 是否转介绍新客 0:否，1:是
     */
    private Integer tagInvitedNewCust;

    /**
     * 是否合格新客 0:否，1:是
     */
    private Integer tagQualifiedNewCust;

    /**
     * 是否合格老客0:否，1:是
     */
    private Integer tagQualifiedOldCust;

    /**
     * 是否低价值客户 0:否 1:是
     */
    private Integer tagLowValue;

    /**
     * 所属商务id
     */
    private String protectSalerId;

    /**
     * 部门id
     */
    private String protectBussdeptId;

    /**
     * 分公司ID
     */
    private String protectSubcompanyId;

    /**
     * 区域ID
     */
    private String protectAreaId;

    /**
     * 状态值（1、保护；2、总监；3、经理；4、客户池）
     */
    private String protectStatus;

    /**
     * 客户阶段。(2：保护跟进；3：网站客户；4：非网站客户)
     */
    private String protectCustType;

    /**
     * 阶段性保护时间
     */
    private Date protectProtectTime;

    /**
     * 已保护天数(仅2023年以后数据)
     */
    private Integer cdpProtectDay;

    /**
     * 本月打卡次数
     */
    private Integer cdpCurrentMonthClockCount;

    /**
     * 累计打卡次数
     */
    private Integer cdpClockCount;

    /**
     * 客户订单首次支付时间
     */
    private Date cdpFirstPaymentTime;

    /**
     * 是否是上市企业 1 是 0否
     */
    private Integer tagFlag12;

    /**
     * 成为合格新客时间
     */
    private Date tagQualifiedNewCustTime;

    /**
     * 是否是大客户（ka）新
     */
    private Integer tagDakehu;

    /**
     * 客户分层
     */
    private Integer customerLayer;

    /**
     * 客户等级
     */
    private Integer customerGrade;

    /**
     * 首次签单时间 2024.8.1后面才会有数据 客户流转会使用 计算流转周期
     */
    private Date firstSignTime;

    /**
     * CDP标签user_tag_sfvwcjkh20250530 未成交客户是否是vip判断依据
     */
    private Integer tagWeichengjiaoVip;

    /**
     * 【销管口径】门户客户 0:否,1:是
     */
    private Integer tagMenhu;

    public Integer getTagFlag12() {
        return tagFlag12;
    }

    public void setTagFlag12(Integer tagFlag12) {
        this.tagFlag12 = tagFlag12;
    }

    public String getProtectSalerId() {
        return protectSalerId;
    }

    public void setProtectSalerId(String protectSalerId) {
        this.protectSalerId = protectSalerId;
    }

    public String getProtectBussdeptId() {
        return protectBussdeptId;
    }

    public void setProtectBussdeptId(String protectBussdeptId) {
        this.protectBussdeptId = protectBussdeptId;
    }

    public String getProtectSubcompanyId() {
        return protectSubcompanyId;
    }

    public void setProtectSubcompanyId(String protectSubcompanyId) {
        this.protectSubcompanyId = protectSubcompanyId;
    }

    public String getProtectAreaId() {
        return protectAreaId;
    }

    public void setProtectAreaId(String protectAreaId) {
        this.protectAreaId = protectAreaId;
    }

    public String getProtectStatus() {
        return protectStatus;
    }

    public void setProtectStatus(String protectStatus) {
        this.protectStatus = protectStatus;
    }

    public String getProtectCustType() {
        return protectCustType;
    }

    public void setProtectCustType(String protectCustType) {
        this.protectCustType = protectCustType;
    }

    public Date getProtectProtectTime() {
        return protectProtectTime;
    }

    public void setProtectProtectTime(Date protectProtectTime) {
        this.protectProtectTime = protectProtectTime;
    }

    public Integer getCdpProtectDay() {
        return cdpProtectDay;
    }

    public void setCdpProtectDay(Integer cdpProtectDay) {
        this.cdpProtectDay = cdpProtectDay;
    }

    public Integer getCdpCurrentMonthClockCount() {
        return cdpCurrentMonthClockCount;
    }

    public void setCdpCurrentMonthClockCount(Integer cdpCurrentMonthClockCount) {
        this.cdpCurrentMonthClockCount = cdpCurrentMonthClockCount;
    }

    public Integer getCdpClockCount() {
        return cdpClockCount;
    }

    public void setCdpClockCount(Integer cdpClockCount) {
        this.cdpClockCount = cdpClockCount;
    }

    public Date getCdpFirstPaymentTime() {
        return cdpFirstPaymentTime;
    }

    public void setCdpFirstPaymentTime(Date cdpFirstPaymentTime) {
        this.cdpFirstPaymentTime = cdpFirstPaymentTime;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getSourceDataId() {
        return sourceDataId;
    }

    public void setSourceDataId(String sourceDataId) {
        this.sourceDataId = sourceDataId;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }

    public String getCertificateCode() {
        return certificateCode;
    }

    public void setCertificateCode(String certificateCode) {
        this.certificateCode = certificateCode;
    }

    public String getCheckInState() {
        return checkInState;
    }

    public void setCheckInState(String checkInState) {
        this.checkInState = checkInState;
    }

    public Date getEstablishDate() {
        return establishDate;
    }

    public void setEstablishDate(Date establishDate) {
        this.establishDate = establishDate;
    }

    public String getRegisterCapital() {
        return registerCapital;
    }

    public void setRegisterCapital(String registerCapital) {
        this.registerCapital = registerCapital;
    }

    public String getPaidInCapital() {
        return paidInCapital;
    }

    public void setPaidInCapital(String paidInCapital) {
        this.paidInCapital = paidInCapital;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
    }

    public String getRegisterNo() {
        return registerNo;
    }

    public void setRegisterNo(String registerNo) {
        this.registerNo = registerNo;
    }

    public String getTaxpayerNo() {
        return taxpayerNo;
    }

    public void setTaxpayerNo(String taxpayerNo) {
        this.taxpayerNo = taxpayerNo;
    }

    public Date getOpenStartTime() {
        return openStartTime;
    }

    public void setOpenStartTime(Date openStartTime) {
        this.openStartTime = openStartTime;
    }

    public Date getOpenEndTime() {
        return openEndTime;
    }

    public void setOpenEndTime(Date openEndTime) {
        this.openEndTime = openEndTime;
    }

    public String getTaxpayerQualification() {
        return taxpayerQualification;
    }

    public void setTaxpayerQualification(String taxpayerQualification) {
        this.taxpayerQualification = taxpayerQualification;
    }

    public String getStaffScale() {
        return staffScale;
    }

    public void setStaffScale(String staffScale) {
        this.staffScale = staffScale;
    }

    public String getInsuredNum() {
        return insuredNum;
    }

    public void setInsuredNum(String insuredNum) {
        this.insuredNum = insuredNum;
    }

    public Date getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(Date approveDate) {
        this.approveDate = approveDate;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public String getRegistrationAuthority() {
        return registrationAuthority;
    }

    public void setRegistrationAuthority(String registrationAuthority) {
        this.registrationAuthority = registrationAuthority;
    }

    public String getImportExportEnterpriseCode() {
        return importExportEnterpriseCode;
    }

    public void setImportExportEnterpriseCode(String importExportEnterpriseCode) {
        this.importExportEnterpriseCode = importExportEnterpriseCode;
    }

    public String getFirstIndustryCode() {
        return firstIndustryCode;
    }

    public void setFirstIndustryCode(String firstIndustryCode) {
        this.firstIndustryCode = firstIndustryCode;
    }

    public String getFirstIndustryName() {
        return firstIndustryName;
    }

    public void setFirstIndustryName(String firstIndustryName) {
        this.firstIndustryName = firstIndustryName;
    }

    public String getSecondIndustryCode() {
        return secondIndustryCode;
    }

    public void setSecondIndustryCode(String secondIndustryCode) {
        this.secondIndustryCode = secondIndustryCode;
    }

    public String getSecondIndustryName() {
        return secondIndustryName;
    }

    public void setSecondIndustryName(String secondIndustryName) {
        this.secondIndustryName = secondIndustryName;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public Integer getPresentStage() {
        return presentStage;
    }

    public void setPresentStage(Integer presentStage) {
        this.presentStage = presentStage;
    }

    public Integer getCreateWay() {
        return createWay;
    }

    public void setCreateWay(Integer createWay) {
        this.createWay = createWay;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreatorKey() {
        return creatorKey;
    }

    public void setCreatorKey(String creatorKey) {
        this.creatorKey = creatorKey;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperatorKey() {
        return operatorKey;
    }

    public void setOperatorKey(String operatorKey) {
        this.operatorKey = operatorKey;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public List<ContactPersonDubboView> getContactPersonDubboViewList() {
        return contactPersonDubboViewList;
    }

    public void setContactPersonDubboViewList(List<ContactPersonDubboView> contactPersonDubboViewList) {
        this.contactPersonDubboViewList = contactPersonDubboViewList;
    }

    public String getEnterpriseType() {
        return enterpriseType;
    }

    public void setEnterpriseType(String enterpriseType) {
        this.enterpriseType = enterpriseType;
    }

    public String getLabelFrom() {
        return labelFrom;
    }

    public void setLabelFrom(String labelFrom) {
        this.labelFrom = labelFrom;
    }

    public Integer getTagKa() {
        return tagKa;
    }

    public void setTagKa(Integer tagKa) {
        this.tagKa = tagKa;
    }

    public Integer getTagWaimao() {
        return tagWaimao;
    }

    public void setTagWaimao(Integer tagWaimao) {
        this.tagWaimao = tagWaimao;
    }

    public Integer getTagMenhuRelated() {
        return tagMenhuRelated;
    }

    public void setTagMenhuRelated(Integer tagMenhuRelated) {
        this.tagMenhuRelated = tagMenhuRelated;
    }

    public Integer getTagEcoToMenhu() {
        return tagEcoToMenhu;
    }

    public void setTagEcoToMenhu(Integer tagEcoToMenhu) {
        this.tagEcoToMenhu = tagEcoToMenhu;
    }

    public Integer getTagEcoCust() {
        return tagEcoCust;
    }

    public void setTagEcoCust(Integer tagEcoCust) {
        this.tagEcoCust = tagEcoCust;
    }

    public Integer getTagMenhuDigital() {
        return tagMenhuDigital;
    }

    public void setTagMenhuDigital(Integer tagMenhuDigital) {
        this.tagMenhuDigital = tagMenhuDigital;
    }

    public Integer getTagMenhu2023() {
        return tagMenhu2023;
    }

    public void setTagMenhu2023(Integer tagMenhu2023) {
        this.tagMenhu2023 = tagMenhu2023;
    }

    public Integer getTagMenhuLowver() {
        return tagMenhuLowver;
    }

    public void setTagMenhuLowver(Integer tagMenhuLowver) {
        this.tagMenhuLowver = tagMenhuLowver;
    }

    public Integer getTagMenhuUpgradeableUpgrade() {
        return tagMenhuUpgradeableUpgrade;
    }

    public void setTagMenhuUpgradeableUpgrade(Integer tagMenhuUpgradeableUpgrade) {
        this.tagMenhuUpgradeableUpgrade = tagMenhuUpgradeableUpgrade;
    }

    public Integer getTagMenhuUpgradeable() {
        return tagMenhuUpgradeable;
    }

    public void setTagMenhuUpgradeable(Integer tagMenhuUpgradeable) {
        this.tagMenhuUpgradeable = tagMenhuUpgradeable;
    }

    public Integer getTagMenhuRenewableRenew() {
        return tagMenhuRenewableRenew;
    }

    public void setTagMenhuRenewableRenew(Integer tagMenhuRenewableRenew) {
        this.tagMenhuRenewableRenew = tagMenhuRenewableRenew;
    }

    public Integer getTagMenhuRenewable() {
        return tagMenhuRenewable;
    }

    public void setTagMenhuRenewable(Integer tagMenhuRenewable) {
        this.tagMenhuRenewable = tagMenhuRenewable;
    }

    public Integer getTagCrossBuy() {
        return tagCrossBuy;
    }

    public void setTagCrossBuy(Integer tagCrossBuy) {
        this.tagCrossBuy = tagCrossBuy;
    }

    public Integer getTagPureMenhu() {
        return tagPureMenhu;
    }

    public void setTagPureMenhu(Integer tagPureMenhu) {
        this.tagPureMenhu = tagPureMenhu;
    }

    public String getTagFlag7() {
        return tagFlag7;
    }

    public void setTagFlag7(String tagFlag7) {
        this.tagFlag7 = tagFlag7;
    }

    public String getTagFlag8() {
        return tagFlag8;
    }

    public void setTagFlag8(String tagFlag8) {
        this.tagFlag8 = tagFlag8;
    }

    public String getTagTechcompany() {
        return tagTechcompany;
    }

    public void setTagTechcompany(String tagTechcompany) {
        this.tagTechcompany = tagTechcompany;
    }

    public Integer getTagQuoteCust() {
        return tagQuoteCust;
    }

    public void setTagQuoteCust(Integer tagQuoteCust) {
        this.tagQuoteCust = tagQuoteCust;
    }

    public String getRecommendCustId() {
        return recommendCustId;
    }

    public void setRecommendCustId(String recommendCustId) {
        this.recommendCustId = recommendCustId;
    }

    public Integer getTagRetainCust() {
        return tagRetainCust;
    }

    public void setTagRetainCust(Integer tagRetainCust) {
        this.tagRetainCust = tagRetainCust;
    }

    public Date getTagRetainTime() {
        return tagRetainTime;
    }

    public void setTagRetainTime(Date tagRetainTime) {
        this.tagRetainTime = tagRetainTime;
    }

    public Integer getTagLostCust() {
        return tagLostCust;
    }

    public void setTagLostCust(Integer tagLostCust) {
        this.tagLostCust = tagLostCust;
    }

    public Date getTagLostTime() {
        return tagLostTime;
    }

    public void setTagLostTime(Date tagLostTime) {
        this.tagLostTime = tagLostTime;
    }

    public Integer getTagInvitedNewCust() {
        return tagInvitedNewCust;
    }

    public void setTagInvitedNewCust(Integer tagInvitedNewCust) {
        this.tagInvitedNewCust = tagInvitedNewCust;
    }

    public Integer getTagQualifiedNewCust() {
        return tagQualifiedNewCust;
    }

    public void setTagQualifiedNewCust(Integer tagQualifiedNewCust) {
        this.tagQualifiedNewCust = tagQualifiedNewCust;
    }

    public Integer getTagQualifiedOldCust() {
        return tagQualifiedOldCust;
    }

    public void setTagQualifiedOldCust(Integer tagQualifiedOldCust) {
        this.tagQualifiedOldCust = tagQualifiedOldCust;
    }

    public Integer getTagLowValue() {
        return tagLowValue;
    }

    public void setTagLowValue(Integer tagLowValue) {
        this.tagLowValue = tagLowValue;
    }

    public Date getTagQualifiedNewCustTime() {
        return tagQualifiedNewCustTime;
    }

    public void setTagQualifiedNewCustTime(Date tagQualifiedNewCustTime) {
        this.tagQualifiedNewCustTime = tagQualifiedNewCustTime;
    }

    public Integer getCustomerGrade() {
        return customerGrade;
    }

    public void setCustomerGrade(Integer customerGrade) {
        this.customerGrade = customerGrade;
    }

    public Integer getCustomerLayer() {
        return customerLayer;
    }

    public void setCustomerLayer(Integer customerLayer) {
        this.customerLayer = customerLayer;
    }

    public Integer getTagDakehu() {
        return tagDakehu;
    }

    public void setTagDakehu(Integer tagDakehu) {
        this.tagDakehu = tagDakehu;
    }

    public Date getFirstSignTime() {
        return firstSignTime;
    }

    public void setFirstSignTime(Date firstSignTime) {
        this.firstSignTime = firstSignTime;
    }

    public Integer getTagWeichengjiaoVip() {
        return tagWeichengjiaoVip;
    }

    public void setTagWeichengjiaoVip(Integer tagWeichengjiaoVip) {
        this.tagWeichengjiaoVip = tagWeichengjiaoVip;
    }

    public Integer getTagMenhu() {
        return tagMenhu;
    }

    public void setTagMenhu(Integer tagMenhu) {
        this.tagMenhu = tagMenhu;
    }

    public String getThirdIndustryCode() {
        return thirdIndustryCode;
    }

    public void setThirdIndustryCode(String thirdIndustryCode) {
        this.thirdIndustryCode = thirdIndustryCode;
    }

    public String getThirdIndustryName() {
        return thirdIndustryName;
    }

    public void setThirdIndustryName(String thirdIndustryName) {
        this.thirdIndustryName = thirdIndustryName;
    }

    public String getFourthIndustryCode() {
        return fourthIndustryCode;
    }

    public void setFourthIndustryCode(String fourthIndustryCode) {
        this.fourthIndustryCode = fourthIndustryCode;
    }

    public String getFourthIndustryName() {
        return fourthIndustryName;
    }

    public void setFourthIndustryName(String fourthIndustryName) {
        this.fourthIndustryName = fourthIndustryName;
    }
}