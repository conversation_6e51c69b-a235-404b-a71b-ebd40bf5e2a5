package com.ce.scrm.customer.dubbo.api;

import com.ce.scrm.customer.dubbo.entity.dto.abm.SdrPushSaleReviewDubboAddDto;
import com.ce.scrm.customer.dubbo.entity.dto.abm.SdrPushSaleReviewDubboUpdateDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.abm.SdrPushSaleReviewDubboView;

/**
 * 跨境ABM sdr 推送销售 审核接口
 */
public interface ISdrPushSaleReviewDubbo {

    /***
     * 添加审核记录
     * @param reviewDubboAddDto
     * <AUTHOR>
     * @date 2025/7/16 19:41
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.String>
     **/
    DubboResult<Long> add(SdrPushSaleReviewDubboAddDto reviewDubboAddDto);

    /***
     * 查询待审核记录
     * @param customerId
     * <AUTHOR>
     * @date 2025/7/16 19:41
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.abm.SdrPushSaleReviewDubboView>
     **/
    DubboResult<SdrPushSaleReviewDubboView> getToBeReview(String customerId);

    /***
     * 审核
     * @param reviewDubboUpdateDto
     * <AUTHOR>
     * @date 2025/7/16 19:42
     * @version 1.0.0
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.lang.String>
     **/
    DubboResult<String> review(SdrPushSaleReviewDubboUpdateDto reviewDubboUpdateDto);
}
