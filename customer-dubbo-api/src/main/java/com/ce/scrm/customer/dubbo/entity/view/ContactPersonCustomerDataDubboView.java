package com.ce.scrm.customer.dubbo.entity.view;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 联系人客户数据
 * <AUTHOR>
 * @date 2023/5/15 09:55
 * @version 1.0.0
 **/
public class ContactPersonCustomerDataDubboView implements Serializable {
    /**
     * 联系人id
     */
    private String contactPersonId;
    /**
     * 联系人姓名
     */
    private String contactPersonName;

    /**
     * 绑定类型：0、无，1、微信，2、企业微信
     */
    private Integer bindType;
    /**
     * 微信unionId
     */
    private String unionId;

    /**
     * unionId绑定时间
     */
    private LocalDateTime unionIdBindTime;
    /**
     * 性别：1、未知，2、男，3、女
     */
    private Integer gender;
    /**
     * 职位
     */
    private String position;

    /**
     * 联系人来源标签
     */
    private String sourceTag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 客户数据
     */
    private CustomerDataDubboView customerDataDubboView;

    /**
     * 联系方式数据
     */
    private List<ContactInfoDataDubboView> contactInfoDataDubboViewList;

    public static class CustomerDataDubboView implements Serializable {

        /**
         * 客户id：C、企业客户标识，P、个人客户标识，Q、其他客户标识（历史客户ID保持不变）
         */
        private String customerId;

        /**
         * 源数据ID
         */
        private String sourceDataId;

        /**
         * 客户类型：1、国内企业，2、个人，3、国外及港澳台
         */
        private Integer customerType;

        /**
         * 客户/企业名称
         */
        private String customerName;

        /**
         * 登记状态：1、在营/存续，2、迁入/迁出，3、吊销/撤销，4、注销，5、停业，6、其他
         */
        private String checkInState;

        public String getCustomerId() {
            return customerId;
        }

        public void setCustomerId(String customerId) {
            this.customerId = customerId;
        }

        public String getSourceDataId() {
            return sourceDataId;
        }

        public void setSourceDataId(String sourceDataId) {
            this.sourceDataId = sourceDataId;
        }

        public Integer getCustomerType() {
            return customerType;
        }

        public void setCustomerType(Integer customerType) {
            this.customerType = customerType;
        }

        public String getCustomerName() {
            return customerName;
        }

        public void setCustomerName(String customerName) {
            this.customerName = customerName;
        }

        public String getCheckInState() {
            return checkInState;
        }

        public void setCheckInState(String checkInState) {
            this.checkInState = checkInState;
        }
    }

    public static class ContactInfoDataDubboView implements Serializable {
        /**
         * 联系方式id
         */
        private  String contactInfoId;

        /**
         * 联系类型：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
         */
        private Integer contactType;

        /**
         * 联系方式
         */
        private String contactWay;

        public String getContactInfoId() {
            return contactInfoId;
        }

        public void setContactInfoId(String contactInfoId) {
            this.contactInfoId = contactInfoId;
        }

        public Integer getContactType() {
            return contactType;
        }

        public void setContactType(Integer contactType) {
            this.contactType = contactType;
        }

        public String getContactWay() {
            return contactWay;
        }

        public void setContactWay(String contactWay) {
            this.contactWay = contactWay;
        }
    }

    public String getContactPersonId() {
        return contactPersonId;
    }

    public void setContactPersonId(String contactPersonId) {
        this.contactPersonId = contactPersonId;
    }

    public String getContactPersonName() {
        return contactPersonName;
    }

    public void setContactPersonName(String contactPersonName) {
        this.contactPersonName = contactPersonName;
    }

    public Integer getBindType() {
        return bindType;
    }

    public void setBindType(Integer bindType) {
        this.bindType = bindType;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public LocalDateTime getUnionIdBindTime() {
        return unionIdBindTime;
    }

    public void setUnionIdBindTime(LocalDateTime unionIdBindTime) {
        this.unionIdBindTime = unionIdBindTime;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getSourceTag() {
        return sourceTag;
    }

    public void setSourceTag(String sourceTag) {
        this.sourceTag = sourceTag;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public CustomerDataDubboView getCustomerDataDubboView() {
        return customerDataDubboView;
    }

    public void setCustomerDataDubboView(CustomerDataDubboView customerDataDubboView) {
        this.customerDataDubboView = customerDataDubboView;
    }

    public List<ContactInfoDataDubboView> getContactInfoDataDubboViewList() {
        return contactInfoDataDubboViewList;
    }

    public void setContactInfoDataDubboViewList(List<ContactInfoDataDubboView> contactInfoDataDubboViewList) {
        this.contactInfoDataDubboViewList = contactInfoDataDubboViewList;
    }
}