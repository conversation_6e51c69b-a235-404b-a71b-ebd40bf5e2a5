package com.ce.scrm.customer.dubbo.entity.view;

import java.io.Serializable;
import java.util.Date;

public class CustomerShareTemporaryDubboView implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 商务代表ID
     */
    private String salerId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门ID
     */
    private String buId;

    /**
     * 分公司ID
     */
    private String subId;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 分享时间
     */
    private Date shareTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public CustomerShareTemporaryDubboView() {}

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getSalerId() {
        return salerId;
    }

    public void setSalerId(String salerId) {
        this.salerId = salerId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getBuId() {
        return buId;
    }

    public void setBuId(String buId) {
        this.buId = buId;
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public Date getShareTime() {
        return shareTime;
    }

    public void setShareTime(Date shareTime) {
        this.shareTime = shareTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
