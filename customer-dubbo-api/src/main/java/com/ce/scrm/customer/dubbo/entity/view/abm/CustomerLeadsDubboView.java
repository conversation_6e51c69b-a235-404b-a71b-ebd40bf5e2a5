package com.ce.scrm.customer.dubbo.entity.view.abm;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * sdr推送销售审核表
 */
@Data
public class CustomerLeadsDubboView implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 数据来源 0:crm原有渠道  1:营销活动
     */
    private Integer dataFromSource;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * leads类别
     */
    private String leadsType;

    /**
     * leads code
     */
    private String leadsCode;

    /**
     * leads 来源
     */
    private String leadsSource;

    /**
     * leads url
     */
    private String leadsUrl;

    /**
     * Leads来源说明
     */
    private String leadsDesc;

    /**
     * 创建者
     */
    private String createdId;

    /**
     * 创建时间
     */
    private Date createTime;

}