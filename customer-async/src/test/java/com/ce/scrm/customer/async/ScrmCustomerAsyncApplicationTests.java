package com.ce.scrm.customer.async;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.async.job.handler.CustomerJobHandler;
import com.ce.scrm.customer.async.job.handler.SyncCustomerFlagHandler;
import com.ce.scrm.customer.async.mq.consumer.SyncContactPersonConsumer;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@Slf4j
@SpringBootTest
class ScrmCustomerAsyncApplicationTests {

    @Resource
    private SyncCustomerFlagHandler syncCustomerFlagHandler;

    @Test
    void contextLoads() {
        ReturnT<String> stringReturnT = syncCustomerFlagHandler.syncCustomerKaFlagJobHandler(null);
        log.info("执行完成，结果为:{}", JSON.toJSONString(stringReturnT));
    }
}
