#!/bin/bash
cd `dirname $0`
BIN_DIR=`pwd`
cd ..
DEPLOY_DIR=`pwd`
LIB_JAR=$DEPLOY_DIR/lib/${project.build.finalName}.jar
SERVER_NAME=${project.build.finalName}
PIDS=`ps -ef | grep java | grep "$LIB_JAR" |awk '{print $2}'`
if [ -n "$PIDS" ]; then
    kill $PIDS
    sleep 10s
fi

PIDS=`ps -ef | grep java | grep "$LIB_JAR" |awk '{print $2}'`
if [ -n "$PIDS" ]; then
    kill -9 $PIDS
fi

LOGS_DIR=$DEPLOY_DIR/logs
if [ ! -d $LOGS_DIR ]; then
    mkdir $LOGS_DIR
fi
STDOUT_FILE=$LOGS_DIR/$SERVER_NAME.log

PROFILE_ACTIVE="--spring.profiles.active=${project.activeProfiles[0].id}"
if [ "$1" != "" ]; then
    PROFILE_ACTIVE="--spring.profiles.active=$1"
fi
if [ "$1" == "pre" ]; then
  JAVA_OPTS=" -server -Xms1024m -Xmx1024m "
else
  JAVA_OPTS=" -server -Xms${jvm.min} -Xmx${jvm.max}"
fi
JAVA_OPTS=" ${JAVA_OPTS} -XX:MaxDirectMemorySize=${jvm.MaxDirectMemorySize} -XX:+UseG1GC -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:${LOGS_DIR}/gc.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${LOGS_DIR}/heap.hprof "
JAVA_DEBUG_OPTS=" -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5017"
DUBBO_NETWORK=" -Ddubbo.network.interface.preferred=eth0 "
echo -e "Starting the $SERVER_NAME ...\c"
nohup java $DUBBO_NETWORK $JAVA_OPTS $JAVA_DEBUG_OPTS -jar $LIB_JAR $PROFILE_ACTIVE &>${STDOUT_FILE} &
echo "OK!"