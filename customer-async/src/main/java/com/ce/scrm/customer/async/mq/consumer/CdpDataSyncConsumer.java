package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.ce.scrm.customer.async.mq.entity.CustomerLabel;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.cache.handler.CustomerCacheHandler;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/***
 * cdp 同步数据 到scrm
 * <AUTHOR>
 * @date 2024/7/16 19:10
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CESUPPORT_SCRM_CUST_LABEL_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.CDP_SYNC_DATA_TO_SCRM_GROUP, consumeThreadMax = 5)
public class CdpDataSyncConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CustomerService customerService;

    @Resource
    private CustomerCacheHandler customerCacheHandler;

    @Override
    public void onMessage(MessageExt messageExt) {
        String param = new String(messageExt.getBody());
        if (StrUtil.isBlank(param)) {
            log.error("cdp 同步数据 到scrm 参数为空，消息ID为:{}", messageExt.getMsgId());
            return;
        }
        try {
            CustomerLabel customerLabel = JSONObject.parseObject(param, CustomerLabel.class);
            if (customerLabel == null || StringUtils.isEmpty(customerLabel.getCustId())) {
                return;
            }
            LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerLabel.getCustId());
            updateChainWrapper.set(Customer::getTagQuoteCust, customerLabel.getTagbjkh());
            updateChainWrapper.set(Customer::getTagRetainCust, customerLabel.getTagRetainCust());
            updateChainWrapper.set(Customer::getTagRetainTime, customerLabel.getTagRetainTime());
            updateChainWrapper.set(Customer::getTagLostCust, customerLabel.getTagLostCust());
            updateChainWrapper.set(Customer::getTagLostTime, customerLabel.getTagLostTime());
            updateChainWrapper.set(Customer::getTagInvitedNewCust, customerLabel.getTagInvitedNewCust());
            updateChainWrapper.set(Customer::getTagQualifiedNewCust, customerLabel.getTagQualifiedNewCust());
            updateChainWrapper.set(Customer::getTagQualifiedOldCust, customerLabel.getTagQualifiedOldCust());
            updateChainWrapper.set(Customer::getTagLowValue, customerLabel.getTagLowValue());
            updateChainWrapper.set(Customer::getTagWaimao, customerLabel.getTagWaiMao());
            updateChainWrapper.set(Customer::getCustomerLayer, customerLabel.getCustomerLayer());
            updateChainWrapper.set(Customer::getCustomerGrade, customerLabel.getCustomerGrade());
            updateChainWrapper.set(Customer::getTagWeichengjiaoVip, customerLabel.getTagWeichengjiaoVip());
            updateChainWrapper.set(Customer::getTagMenhu, customerLabel.getTagMenhu());
            if (customerLabel.getQualifiedNewCustTime() != null){
                updateChainWrapper.set(Customer::getTagQualifiedNewCustTime, customerLabel.getQualifiedNewCustTime());
            }
            if (StringUtils.isNotBlank(customerLabel.getFirstIndustryCode())){
                updateChainWrapper.set(Customer::getFirstIndustryCode, customerLabel.getFirstIndustryCode());
            }
            if (StringUtils.isNotBlank(customerLabel.getSecondIndustryCode())){
                updateChainWrapper.set(Customer::getSecondIndustryCode, customerLabel.getSecondIndustryCode());
            }
            if (StringUtils.isNotBlank(customerLabel.getThirdIndustryCode())){
                updateChainWrapper.set(Customer::getThirdIndustryCode, customerLabel.getThirdIndustryCode());
            }
            if (StringUtils.isNotBlank(customerLabel.getFourthIndustryCode())){
                updateChainWrapper.set(Customer::getFourthIndustryCode, customerLabel.getFourthIndustryCode());
            }
            if (StringUtils.isNotBlank(customerLabel.getFirstIndustryName())){
                updateChainWrapper.set(Customer::getFirstIndustryName, customerLabel.getFirstIndustryName());
            }
            if (StringUtils.isNotBlank(customerLabel.getSecondIndustryName())){
                updateChainWrapper.set(Customer::getSecondIndustryName, customerLabel.getSecondIndustryName());
            }
            if (StringUtils.isNotBlank(customerLabel.getThirdIndustryName())){
                updateChainWrapper.set(Customer::getThirdIndustryName, customerLabel.getThirdIndustryName());
            }
            if (StringUtils.isNotBlank(customerLabel.getFourthIndustryName())){
                updateChainWrapper.set(Customer::getFourthIndustryName, customerLabel.getFourthIndustryName());
            }
            if (Objects.equals(customerLabel.getProtectStatus(), "1")) {
                updateChainWrapper.set(Customer::getProtectSalerId, customerLabel.getProtectSalerId());
                updateChainWrapper.set(Customer::getProtectBussdeptId, customerLabel.getProtectBussdeptId());
                updateChainWrapper.set(Customer::getProtectSubcompanyId, customerLabel.getProtectSubcompanyId());
                updateChainWrapper.set(Customer::getProtectAreaId, customerLabel.getProtectAreaId());
                updateChainWrapper.set(Customer::getProtectStatus, customerLabel.getProtectStatus());
                updateChainWrapper.set(Customer::getProtectCustType, customerLabel.getProtectCustType());
                updateChainWrapper.set(Customer::getProtectProtectTime, customerLabel.getProtectProtectTime());
                if (customerLabel.getCdpProtectDay() == null) {
                    updateChainWrapper.set(Customer::getCdpProtectDay, 0);
                } else {
                    updateChainWrapper.set(Customer::getCdpProtectDay, customerLabel.getCdpProtectDay());
                }
                if (customerLabel.getCdpCurrentMonthClockCount() == null) {
                    updateChainWrapper.set(Customer::getCdpCurrentMonthClockCount, 0);
                } else {
                    updateChainWrapper.set(Customer::getCdpCurrentMonthClockCount, customerLabel.getCdpCurrentMonthClockCount());
                }
                if (customerLabel.getCdpClockCount() == null) {
                    updateChainWrapper.set(Customer::getCdpClockCount, 0);
                } else {
                    updateChainWrapper.set(Customer::getCdpClockCount, customerLabel.getCdpClockCount());
                }
                updateChainWrapper.set(Customer::getCdpFirstPaymentTime, customerLabel.getCdpFirstPaymentTime());
            } else if (Objects.equals(customerLabel.getProtectStatus(), "2")) {
                updateChainWrapper.set(Customer::getProtectSubcompanyId, customerLabel.getProtectSubcompanyId());
                updateChainWrapper.set(Customer::getProtectAreaId, customerLabel.getProtectAreaId());
                updateChainWrapper.set(Customer::getProtectStatus, customerLabel.getProtectStatus());
                updateChainWrapper.set(Customer::getProtectCustType, customerLabel.getProtectCustType());
                updateChainWrapper.set(Customer::getProtectProtectTime, null);
                updateChainWrapper.set(Customer::getCdpProtectDay, 0);
                updateChainWrapper.set(Customer::getCdpCurrentMonthClockCount, 0);
                updateChainWrapper.set(Customer::getCdpClockCount, 0);
                updateChainWrapper.set(Customer::getCdpFirstPaymentTime, null);

            } else if (Objects.equals(customerLabel.getProtectStatus(), "3")) {
                updateChainWrapper.set(Customer::getProtectBussdeptId, customerLabel.getProtectBussdeptId());
                updateChainWrapper.set(Customer::getProtectSubcompanyId, customerLabel.getProtectSubcompanyId());
                updateChainWrapper.set(Customer::getProtectAreaId, customerLabel.getProtectAreaId());
                updateChainWrapper.set(Customer::getProtectStatus, customerLabel.getProtectStatus());
                updateChainWrapper.set(Customer::getProtectCustType, customerLabel.getProtectCustType());
                updateChainWrapper.set(Customer::getProtectProtectTime, null);
                updateChainWrapper.set(Customer::getCdpProtectDay, 0);
                updateChainWrapper.set(Customer::getCdpCurrentMonthClockCount, 0);
                updateChainWrapper.set(Customer::getCdpClockCount, 0);
                updateChainWrapper.set(Customer::getCdpFirstPaymentTime, null);
            } else {
                updateChainWrapper.set(Customer::getProtectSalerId, null);
                updateChainWrapper.set(Customer::getProtectBussdeptId, null);
                updateChainWrapper.set(Customer::getProtectSubcompanyId, null);
                updateChainWrapper.set(Customer::getProtectAreaId, null);
                updateChainWrapper.set(Customer::getProtectStatus, null);
                updateChainWrapper.set(Customer::getProtectCustType, null);
                updateChainWrapper.set(Customer::getProtectProtectTime, null);
                updateChainWrapper.set(Customer::getCdpProtectDay, 0);
                updateChainWrapper.set(Customer::getCdpCurrentMonthClockCount, 0);
                updateChainWrapper.set(Customer::getCdpClockCount, 0);
                updateChainWrapper.set(Customer::getCdpFirstPaymentTime, null);
            }
            boolean result = updateChainWrapper.update();
            if (result) {
                customerCacheHandler.del(customerLabel.getCustId());
            }
        } catch (Exception e) {
            log.error("cdp 同步数据 到scrm失败,messageId={}", messageExt.getMsgId(), e);
        }
    }
}