package com.ce.scrm.customer.async.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.async.mq.entity.SegmentActivityMessageInfo;
import com.ce.scrm.customer.dao.entity.abm.CustomerMarketingActivities;
import com.ce.scrm.customer.dao.entity.abm.CustomerMarketingActivitiesRecord;
import com.ce.scrm.customer.dao.service.abm.CustomerMarketingActivitiesRecordService;
import com.ce.scrm.customer.dao.service.abm.CustomerMarketingActivitiesService;
import com.ce.scrm.customer.service.business.ICustomerContactBusiness;
import com.ce.scrm.customer.service.business.entity.dto.CustomerContactBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView;
import com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 分群客户信息同步到 customer_marketing_activities_record
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CUSTOMER_SEGMENT_CUSTOMER_INFO_TOPIC,
        consumerGroup = ServiceConstant.MqConstant.Group.CDP_CUSTOMER_SEGMENT_CUSTOMER_INFO_GROUP,
        consumeMode = ConsumeMode.CONCURRENTLY, consumeThreadMax = 5)
public class SegmentCustomerConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CustomerMarketingActivitiesRecordService recordService;
	@Resource
	private CustomerMarketingActivitiesService activitiesService;
	@Resource
	private ICustomerContactBusiness customerContactBusiness;
	// ContactInfoBusinessView.contactType
	final List<Integer> supportContactType = Lists.newArrayList(1, 3, 4);

    @Override
    public void onMessage(MessageExt messageExt) {
        String messageId = messageExt.getMsgId();
        String message = new String(messageExt.getBody());
        if (StringUtils.isBlank(message)) {
            return;
        }
        log.info("CDP_CUSTOMER_SEGMENT_CUSTOMER_INFO_TOPIC，msgId={},消息内容：{}", messageId, message);
        try {
	        SegmentActivityMessageInfo activityMessageInfo = JSON.parseObject(message, SegmentActivityMessageInfo.class);
	        if (StringUtils.isNotBlank(activityMessageInfo.getDistinctId()) && StringUtils.isNotBlank(activityMessageInfo.getGroupId())) {

		        List<ContactPersonBusinessView> contactPersonList = new ArrayList<>();
		        String customerId = activityMessageInfo.getDistinctId();
		        CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
		        customerContactBusinessDto.setCustomerId(customerId);
		        CustomerBusinessView customerBusinessView = customerContactBusiness.customerContactDetail(customerContactBusinessDto);
				if (customerBusinessView != null && !CollectionUtils.isEmpty(customerBusinessView.getContactPersonList())) {
					contactPersonList = customerBusinessView.getContactPersonList();
				}

				if (CollectionUtils.isEmpty(contactPersonList)) {
					log.error("SegmentCustomerConsumer#分群客户信息同步到customer_marketing_activities_record时获取客户联系人信息失败，customerId={}, customerBusinessView={}", customerId, JSON.toJSONString(customerBusinessView));
					return;
				}
				List<CustomerMarketingActivitiesRecord> recordEntities = new ArrayList<>();
		        contactPersonList.stream().filter(x -> !CollectionUtils.isEmpty(x.getContactInfoList())).flatMap(x -> x.getContactInfoList().stream())
			        .filter(x -> StringUtils.isNotBlank(x.getContactWay())).forEach(contactInfo -> {
			        CustomerMarketingActivitiesRecord record = new CustomerMarketingActivitiesRecord();
			        record.setCustomerId(customerId);
			        record.setActivityId(activityMessageInfo.getActivityId());

			        CustomerMarketingActivities marketingActivity = activitiesService.lambdaQuery().eq(CustomerMarketingActivities::getId, activityMessageInfo.getActivityId()).one();
			        if (Objects.isNull(marketingActivity)) {
				        log.info("活动id不存在， activityMessageInfo={}", JSON.toJSONString(activityMessageInfo));
				        return;
			        }
			        record.setActivityType(marketingActivity.getActivityType());
			        record.setActivityContent(marketingActivity.getActivityContent());
			        record.setCreateTime(new Date());
			        record.setIsExecute(0);
			        record.setIsDistribute(0);
			        record.setLeadsId(null); // 有营销效果时才会产生leads
			        if (!supportContactType.contains(contactInfo.getContactType())) {
						log.info("当前联系人类型contactType={}, 营销活动不支持该联系方式", contactInfo);
						return;
			        }
			        recordEntities.add(record);
				});
		        recordService.saveBatch(recordEntities);
	        }
        } catch (Exception e) {
			log.error("");
            throw new RuntimeException(e);
        }
    }
}