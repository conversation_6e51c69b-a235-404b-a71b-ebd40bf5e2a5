package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.async.service.ICustomerAsyncService;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.service.mq.entity.ValidCustomerCheckDto;
import com.ce.scrm.customer.service.third.invoke.NewCustomerThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 客户同步消费
 * <AUTHOR>
 * @date 2023/7/11 14:01
 * @version 1.0.0
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CUSTOMER_TOPIC, selectorExpression = ServiceConstant.MqConstant.Tag.CHECK_VALID_CUSTOMER_TAG, consumerGroup = ServiceConstant.MqConstant.Group.CHECK_VALID_CUSTOMER_GROUP)
public class ValidCustomerCheckConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private NewCustomerThirdService newCustomerThirdService;

    @Resource
    private ICustomerAsyncService customerAsyncService;

    @Override
    public void onMessage(MessageExt messageExt) {
//        String param = new String(messageExt.getBody());
//        if (StrUtil.isBlank(param)) {
//            log.error("有效客户校验mq，参数不能为空，消息ID为:{}", messageExt.getMsgId());
//            return;
//        }
//        ValidCustomerCheckDto validCustomerCheckDto = JSON.parseObject(param, ValidCustomerCheckDto.class);
//        Map<String, Boolean> customerValidFlag = customerAsyncService.getCustomerValidFlag(validCustomerCheckDto.getCustomerIdList());
//        validCustomerCheckDto.getCustomerIdList().forEach(customerId -> {
//            if (customerValidFlag.getOrDefault(customerId, true)) {
//                return;
//            }
//            boolean validFlag = CustomerJobHandler.VALID_CUSTOMER_ID.contains(customerId) || newCustomerThirdService.checkValidCustomer(customerId);
//            if (validFlag && !customerAsyncService.setValidCustomerFlag(customerId)) {
//                log.error("有效客户校验mq，当前客户为有效客户，设置有效客户失败，参数为:{}，客户ID为:{}", param, customerId);
//            }
//            if (!validFlag && (validCustomerCheckDto.getExactQueryFlag() == null || !validCustomerCheckDto.getExactQueryFlag())) {
//                log.error("有效客户校验mq，当前客户为无效客户，参数为:{}，客户ID为:{}", param, customerId);
//            }
//        });
    }
}