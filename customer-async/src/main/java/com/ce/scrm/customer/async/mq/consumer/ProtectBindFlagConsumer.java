package com.ce.scrm.customer.async.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.center.dubbo.entity.dto.AddProtectTimeEventDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.CustomerProtectDubboDto;
import com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew;
import com.ce.scrm.customer.async.mq.consumer.binlog.AbstractBinlogConsumer;
import com.ce.scrm.customer.async.mq.consumer.binlog.TableChangeEnum;
import com.ce.scrm.customer.dao.entity.CustomerBindWx;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.service.third.invoke.ProtectThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 当绑定客户的时候 维护保护关系表的绑定状态，并且触发增加保护时长的事件
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CUSTOMER_BIND_WX_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.SCRM_PROTECT_BIND_FLAG_GROUP)
public class ProtectBindFlagConsumer extends AbstractBinlogConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private ProtectThirdService protectThirdService;

    @Override
    public void dealEvent(String msgId, JSONObject beforeJson, JSONObject afterJson, String type) {
        if (!Objects.equals(type, TableChangeEnum.insert.getField())) {
            return;
        }
        CustomerBindWx customerBindWx = JSONObject.parseObject(afterJson.toJSONString(), CustomerBindWx.class);

        // 不是绑定客户 跳过不处理
        if (!Objects.equals(customerBindWx.getBindType(), 1)) {
            return;
        }

        if (customerBindWx.getCustomerId() == null) {
            log.error("customerId is null，afterJson={}", afterJson);
            return;
        }

        CustomerProtectDubboVew customerProtectDubboVew = protectThirdService.selectCustomerById(customerBindWx.getCustomerId());
        if (customerProtectDubboVew == null || customerProtectDubboVew.getStatus() != 1) {
            return;
        }

        // 绑定的人和保护商务必须是一个人在才能走后面逻辑
        if (!Objects.equals(customerProtectDubboVew.getSalerId(),customerBindWx.getCreateId())) {
            return;
        }

        if (Objects.equals(customerBindWx.getType(), 0)) {
            //绑定
            customerProtectDubboVew.setBindFlag(1);
            CustomerProtectDubboDto customerProtectDubboDto = new CustomerProtectDubboDto();
            BeanUtils.copyProperties(customerProtectDubboVew, customerProtectDubboDto);
            Integer i = protectThirdService.updateByCustId(customerProtectDubboDto);
            // 触发一下是否有效打卡流程
            if (i > 0) {
                protectThirdService.addProtectTimeByEvent(AddProtectTimeEventDubboDto.builder().customerId(customerBindWx.getCustomerId()).eventType(3).build());
            }
        } else if (Objects.equals(customerBindWx.getType(), 1)) {
            // 解绑
            customerProtectDubboVew.setBindFlag(0);
            CustomerProtectDubboDto customerProtectDubboDto = new CustomerProtectDubboDto();
            BeanUtils.copyProperties(customerProtectDubboVew, customerProtectDubboDto);
            Integer i = protectThirdService.updateByCustId(customerProtectDubboDto);
        }
    }

    @Override
    public void onMessage(MessageExt messageExt) {
        super.dealBinlogMqMessage(messageExt);
    }

}
