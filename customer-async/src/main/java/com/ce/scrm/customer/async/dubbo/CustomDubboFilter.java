package com.ce.scrm.customer.async.dubbo;

import cn.hutool.core.util.StrUtil;
import com.ce.scrm.customer.support.init.InitConsole;
import com.ce.scrm.customer.util.constant.UtilConstant;
import org.apache.dubbo.rpc.*;
import org.slf4j.MDC;

/**
 * 自定义dubbo过滤器
 * <AUTHOR>
 * @date 2023/4/6 14:02
 * @version 1.0.0
 **/
public class CustomDubboFilter implements Filter {

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        UtilConstant.Mdc.MDC_LIST.forEach((key, value) -> {
            if (RpcContext.getContext().isProviderSide()) {
                String rpcValue = RpcContext.getContext().getAttachment(key);
                if (StrUtil.isBlank(rpcValue)) {
                    rpcValue = value.get();
                }
                MDC.put(key, rpcValue);
            } else {
                RpcContext.getContext().setAttachment(key, MDC.get(key));
            }
        });
        if (RpcContext.getContext().isConsumerSide()) {
            RpcContext.getContext().setRemoteApplicationName(InitConsole.APPLICATION_NAME);
        }
        return invoker.invoke(invocation);
    }
}
