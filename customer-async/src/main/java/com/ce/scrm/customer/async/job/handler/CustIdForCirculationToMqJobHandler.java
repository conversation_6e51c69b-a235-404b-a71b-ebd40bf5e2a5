package com.ce.scrm.customer.async.job.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.support.mq.RocketMqOperate;
import com.ce.scrm.customer.support.redis.RedisOperator;
import com.ce.scrm.customer.support.redis.RedissonLock;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 客户id放入MQ，用于将待流转客户insert到待流转表
 * @author: JiuDD
 * date: 2024/9/5
 */
@Slf4j
@Component
public class CustIdForCirculationToMqJobHandler {
    /**
     * 从 customer 表加载的客户id数量
     */
    private int customerIdCou = 0;
    /**
     * 每批次从 customer 表加载客户id数量
     */
    private final int pageSize = 2000;
    /**
     * 任务锁。获取到锁的 xxljob 可以从 customer 表加载客户id 并放入MQ
     */
    private final String lockName = "CUST_ID_FOR_CIRCULATION_TO_MQ";
    /**
     * 记录当前任务已放入MQ的最后一个customerId对应的customer表的id 的Redis key. 即customerId 放入MQ的进度，用于断点续传。
     */
    private static final String CURR_ID_REDIS_KEY = "CustomerCurrIdForCirculationRedisKey";

    @Resource
    private RedisOperator redisOperator;
    @Resource
    private RedissonLock redissonLock;
    @Resource
    private RocketMqOperate rocketMqOperate;
    @Resource
    private CustomerService customerService;

    /**
     * Description: 客户id放入MQ，用于将待流转客户insert到待流转表。
     * @author: JiuDD
     * @param param
     *      1.customerId 可指定多个，以英文逗号分割，此时，只将给定的customerId放入MQ
     *      2.force      是否强制跑全量. 如果是，则不会启用断点续传功能，会从customer表的最小id开始查询customerId放入MQ
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * date: 2024/9/5 10:27
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.CUSTOMER_ID_TO_MQ_FOR_CIRCULATION)
    public ReturnT<String> putCustomerIdToMqForCirculation(String param){
        //每次触发job前 数量初始化为0
        customerIdCou = 0;
        //是否强制跑全量. 如果是，则不会启用断点续传功能，会从customer表的最小id开始查询customerId放入MQ
        boolean forceExecute = false;

        try {
            List<String> customerIdList = new ArrayList<>();
            if (StringUtils.isNotBlank(param)) {
                JSONObject jsonObject = JSONObject.parseObject(param);
                String customerIds = jsonObject.getString("customerId");
                if (StringUtils.isNotBlank(customerIds)) {
                    customerIdList= Arrays.asList(customerIds.split(","));
                }
                String force = jsonObject.getString("force");
                if (Objects.equals("true", force)) {
                    forceExecute = true;
                }
            }

            if (CollectionUtil.isNotEmpty(customerIdList)) {
                log.info("指定customerId放入MQ用于将待流转客户insert到待流转表. customerIdList={}", JSONObject.toJSONString(customerIdList));
                putCusId2Mq(customerIdList);
            } else {
                log.info("全量customerId放入MQ用于将待流转客户insert到待流转表");
                //获取锁
                boolean tryLock = redissonLock.tryLock(lockName, 5, 60 * 60 * 4);
                if (!tryLock) {
                    log.error("获取锁失败：全量customerId放入MQ用于将待流转客户insert到待流转表.");
                    return ReturnT.FAIL;
                }

                log.info("Start put customer_id to mq for circulation.");
                Long minId = getCurrMinId(CURR_ID_REDIS_KEY, forceExecute);
                while (true) {
                    List<String> customerList = getCusIdFromCrmCustomer(minId);
                    if (CollectionUtils.isEmpty(customerList)) {
                        // 当前id往后取 pageSize 个，如果没取到数据，则有可能是取完了，也有可能是由于某些原因导致id之间不连续，且不连续的间隔大于了 pageSize，导致此批次没取到数据。
                        Long nextMinId = customerService.getNextMinId(minId + pageSize);
                        if (Objects.isNull(nextMinId)) {
                            log.error("所有id已经全部取完[用于将待流转客户insert到待流转表]，当前id区间为[{}, 正无穷]",  minId + pageSize);
                            break;
                        } else {
                            minId = nextMinId;
                            redisOperator.set(CURR_ID_REDIS_KEY, minId.toString());
                            continue;
                        }
                    }
                    putCusId2Mq(customerList);
                    //下一轮的初始id放入Redis（断点续传）
                    minId += pageSize;
                    redisOperator.set(CURR_ID_REDIS_KEY, minId.toString());
                }
                log.error("从scrm_customer.customer表获取客户id并放入MQ完成[用于将待流转客户insert到待流转表]. 累计数量={}", customerIdCou);
                //全量客户放入MQ完成后，删除Redis缓存
                redisOperator.del(CURR_ID_REDIS_KEY);
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("从scrm_customer.customer表获取客户id并放入MQ失败[用于将待流转客户insert到待流转表]", e);
        } finally {
            //释放锁,允许其它 xxljob 执行customerId放入MQ的操作
            redissonLock.unLock(lockName);
        }
        return ReturnT.FAIL;
    }

    /**
     * Description: 获取当前已放入MQ的最后一个customerId对应的customer表的id
     * @author: JiuDD
     * @param currIdRedisKey 记录当前任务已放入MQ的最后一个customerId对应的customer表的id 的Redis key. 即customerId 放入MQ的进度，用于断点续传。
     * @param forceExecute 是否强制跑全量. 如果是，则不会启用断点续传功能，会从customer表的最小id开始查询customerId放入MQ
     * @return java.lang.Long
     * date: 2024/9/5 13:46
     */
    private Long getCurrMinId(String currIdRedisKey, Boolean forceExecute) {
        if (forceExecute) {
            log.error("强制跑全量. 不启用断点续传功能，从customer表的最小id开始查询customerId放入MQ");
            return getMinIdOfCrmCustomer();
        }
        //首先从Redis获取当前已放入MQ的最后一个customerId对应的customer表的id
        String currIdRedis = redisOperator.get(currIdRedisKey);
        if (StringUtils.isNotEmpty(currIdRedis)) {
            log.error("启用断点续传功能，从redis获取的最小id为: {}", currIdRedis);
            return Long.parseLong(currIdRedis);
        }
        //从MySQL取最小的id
        Long minIdOfCrmCustomer = getMinIdOfCrmCustomer();
        log.error("从customer表获取的最小id为: {}", minIdOfCrmCustomer);
        return minIdOfCrmCustomer;
    }

    /**
     * Description: 从scrm_customer.customer表获取customerId
     * @author: JiuDD
     * @param startId   起始id
     * @return java.util.List<java.lang.String> customerId列表
     * date: 2024/9/5 13:12
     */
    private List<String> getCusIdFromCrmCustomer(long startId) {
        // 数据量太大，不建议使用Page翻页方式，排序效率太低。 使用 ID between rangeA and rangeB
        log.info("Start get customer_id from scrm_customer.customer. startId={}, pageSize={}", startId, pageSize);
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(
                Customer::getCustomerId
        );
        queryWrapper.ge(Customer::getId, startId);
        queryWrapper.lt(Customer::getId, startId + pageSize);
        List<Customer> customerList = customerService.list(queryWrapper);
        return customerList.stream().map(x -> x.getCustomerId()).collect(Collectors.toList());
    }

    /**
     * Description: 获取 scrm_customer.customer表的最小id
     * @author: JiuDD
     * @return java.lang.Long scrm_customer.customer表的最小id
     * date: 2024/9/5 10:29
     */
    private Long getMinIdOfCrmCustomer() {
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(
                Customer::getId
        );
        queryWrapper.orderByAsc(Customer::getId);
        queryWrapper.last("LIMIT 1");
        Customer one = customerService.getOne(queryWrapper);
        if (Objects.isNull(one)) {
            return 1L;
        }
        Long id = one.getId();
        return id;
    }

    /**
     * customerIds 发送mq
     * @author: JiuDD
     * @param customerIds 客户id列表
     * @return void
     * date: 2024/9/5 11:08
     */
    private void putCusId2Mq(List<String> customerIds) {
        Optional.ofNullable(customerIds).orElse(Lists.newArrayList()).stream().forEach(
                e -> {
                    log.info("客户id放入MQ用于将待流转客户insert到待流转表: customerId={}", e);
                    try {
                        rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CUST_ID_FOR_CIRCULATION_TOPIC, e);
                    } catch (Exception ex) {
                        // 出现异常，如RocketMQ broker busy，则重试一次，如果还是失败，则抛出异常
                        try {
                            Thread.sleep(5000);
                        } catch (InterruptedException exc) {
                            throw new RuntimeException(exc);
                        }
                        rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CUST_ID_FOR_CIRCULATION_TOPIC, e);
                    }
                    customerIdCou ++;
                }
        );
    }

}
