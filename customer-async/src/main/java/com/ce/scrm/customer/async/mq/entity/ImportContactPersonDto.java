package com.ce.scrm.customer.async.mq.entity;

import lombok.Getter;

import java.util.Date;

@Getter
public class ImportContactPersonDto {
    private String customerId;
    private String contactPersonName;
    private Integer gender;
    private String department;
    private String position;
    private String certificatesNumber;
    private String provinceCode;
    private String cityCode;
    private String districtCode;
    private String address;
    private String sourceKey;
    private String remarks;
    private String phone;
    private String email;
    private String wechat;
    private String wecome;
    private String qq;
    private String telephone;
    private Date createTime;
    private String creator;
    private Date updateTime;
    private String operator;

    public void setCustomer_id(String customerId) {
        this.customerId = customerId;
    }

    public void setContact_person_name(String contactPersonName) {
        this.contactPersonName = contactPersonName;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public void setCertificates_number(String certificatesNumber) {
        this.certificatesNumber = certificatesNumber;
    }

    public void setProvince_code(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public void setCity_code(String cityCode) {
        this.cityCode = cityCode;
    }

    public void setDistrict_code(String districtCode) {
        this.districtCode = districtCode;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setSource_key(String sourceKey) {
        this.sourceKey = sourceKey;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public void setWecome(String wecome) {
        this.wecome = wecome;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public void setCreate_time(Date createTime) {
        this.createTime = createTime;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public void setUpdate_time(Date updateTime) {
        this.updateTime = updateTime;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
