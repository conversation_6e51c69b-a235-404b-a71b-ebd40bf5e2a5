package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.async.mq.entity.MenhuInstanceTagDto;
import com.ce.scrm.customer.dao.entity.CustomerTagHistory;
import com.ce.scrm.customer.dao.service.CustomerTagHistoryService;
import com.ce.scrm.customer.service.business.ICustomerBusiness;
import com.ce.scrm.customer.service.business.entity.dto.CustomerTagUpdateBusinessDto;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 门户实例标签同步
 * <AUTHOR>
 * @date 2023/8/24 10:41
 * @version 1.0.0
 **/
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CUSTOMER_TAG_UPDATE_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.CDP_CUSTOMER_TAG_UPDATE_GROUP,consumeThreadMax = 5)
public class MenhuInstanceFlagSyncConsumer implements RocketMQListener<MessageExt> {
    @Resource
    private ICustomerBusiness customerBusiness;

    @Resource
    private CustomerTagHistoryService customerTagHistoryService;

    @Override
    public void onMessage(MessageExt messageExt) {
        String param = new String(messageExt.getBody());
        if (StrUtil.isBlank(param)) {
            log.error("搜客宝标签同步参数为空，消息ID为:{}", messageExt.getMsgId());
            return;
        }
        try {
            MenhuInstanceTagDto menhuInstanceTagDto = JSON.parseObject(param, MenhuInstanceTagDto.class);
            if (menhuInstanceTagDto == null || StringUtils.isEmpty(menhuInstanceTagDto.getCustomerId())){
                return;
            }
            CustomerTagUpdateBusinessDto customerTagUpdateBusinessDto = new CustomerTagUpdateBusinessDto();
            customerTagUpdateBusinessDto.setCustomerId(menhuInstanceTagDto.getCustomerId());
            customerTagUpdateBusinessDto.setTagMenhuRelated(menhuInstanceTagDto.getTagMenhuRelated());
            customerTagUpdateBusinessDto.setTagEcoToMenhu(menhuInstanceTagDto.getTagEcoToMenhu());
            customerTagUpdateBusinessDto.setTagEcoCust(menhuInstanceTagDto.getTagEcoCust());
            customerTagUpdateBusinessDto.setTagMenhuDigital(menhuInstanceTagDto.getTagMenhuDigital());
            customerTagUpdateBusinessDto.setTagMenhu2023(menhuInstanceTagDto.getTagMenhu2023());
            customerTagUpdateBusinessDto.setTagMenhuLowver(menhuInstanceTagDto.getTagMenhuLowver());
            customerTagUpdateBusinessDto.setTagMenhuUpgradeableUpgrade(menhuInstanceTagDto.getTagMenhuUpgradeableUpgrade());
            customerTagUpdateBusinessDto.setTagMenhuUpgradeable(menhuInstanceTagDto.getTagMenhuUpgradeable());
            customerTagUpdateBusinessDto.setTagMenhuRenewableRenew(menhuInstanceTagDto.getTagMenhuRenewableRenew());
            customerTagUpdateBusinessDto.setTagMenhuRenewable(menhuInstanceTagDto.getTagMenhuRenewable());
            customerTagUpdateBusinessDto.setTagCrossBuy(menhuInstanceTagDto.getTagCrossBuy());
            customerTagUpdateBusinessDto.setTagPureMenhu(menhuInstanceTagDto.getTagPureMenhu());
            customerTagUpdateBusinessDto.setTagMenhuNewTime(menhuInstanceTagDto.getTagMenhuNewTime());
            customerTagUpdateBusinessDto.setTagMenhuNewCategory(menhuInstanceTagDto.getTagMenhuNewCategory());
            customerBusiness.updateCustomerInstanceTag(customerTagUpdateBusinessDto);
            //写入履历表
            CustomerTagHistory customerTagHistory = new CustomerTagHistory();
            customerTagHistory.setCustomerId(menhuInstanceTagDto.getCustomerId());
            customerTagHistory.setSalerId(menhuInstanceTagDto.getSalerId());
            customerTagHistory.setDeptId(menhuInstanceTagDto.getDeptId());
            customerTagHistory.setSubId(menhuInstanceTagDto.getSubId());
            customerTagHistory.setAreaId(menhuInstanceTagDto.getAreaId());
            customerTagHistory.setCustomerId(menhuInstanceTagDto.getCustomerId());
            customerTagHistory.setTagMenhuRelated(menhuInstanceTagDto.getTagMenhuRelated());
            customerTagHistory.setTagEcoToMenhu(menhuInstanceTagDto.getTagEcoToMenhu());
            customerTagHistory.setTagEcoCust(menhuInstanceTagDto.getTagEcoCust());
            customerTagHistory.setTagMenhuDigital(menhuInstanceTagDto.getTagMenhuDigital());
            customerTagHistory.setTagMenhu2023(menhuInstanceTagDto.getTagMenhu2023());
            customerTagHistory.setTagMenhuLowver(menhuInstanceTagDto.getTagMenhuLowver());
            customerTagHistory.setTagMenhuUpgradeableUpgrade(menhuInstanceTagDto.getTagMenhuUpgradeableUpgrade());
            customerTagHistory.setTagMenhuUpgradeable(menhuInstanceTagDto.getTagMenhuUpgradeable());
            customerTagHistory.setTagMenhuRenewableRenew(menhuInstanceTagDto.getTagMenhuRenewableRenew());
            customerTagHistory.setTagMenhuRenewable(menhuInstanceTagDto.getTagMenhuRenewable());
            customerTagHistory.setTagCrossBuy(menhuInstanceTagDto.getTagCrossBuy());
            customerTagHistory.setTagPureMenhu(menhuInstanceTagDto.getTagPureMenhu());
            customerTagHistory.setTagMenhuNewTime(menhuInstanceTagDto.getTagMenhuNewTime());
            customerTagHistory.setTagMenhuNewCategory(menhuInstanceTagDto.getTagMenhuNewCategory());
            customerTagHistoryService.save(customerTagHistory);
        }catch (Exception e){
            log.error("搜客宝标签同步失败",e);
        }
    }
}