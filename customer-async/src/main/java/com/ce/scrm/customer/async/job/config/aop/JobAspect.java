package com.ce.scrm.customer.async.job.config.aop;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.support.log.LogObject;
import com.ce.scrm.customer.support.log.MdcUtil;
import com.ce.scrm.customer.util.constant.UtilConstant;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.xxl.job.core.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * 定时任务切面
 * <AUTHOR>
 * @date 2023/4/6 20:20
 * @version 1.0.0
 **/
@Slf4j
@Aspect
@Component
public class JobAspect {

    /**
     * 执行成功消息
     */
    private final static String EXECUTE_SUCCESS = "success";

    private final static String JOB_INVOKER_NAME = "xxlJob";

    /**
     * 定义job切点
     * <AUTHOR>
     * @date 2023/4/6 20:20
     **/
    @Pointcut("execution(public * com.ce.scrm.customer.async.job.handler.*.*(..))")
    public void requestJobConfigPointCut() {
    }

    /**
     * 增强处理
     * 定义环绕类型的Advise（增强器）
     * @param joinPoint 切面连接点（被代理方法的相关封装）
     * <AUTHOR>
     * @date 2023/4/6 20:20
     * @return java.lang.Object
     **/
    @Around("requestJobConfigPointCut()")
    public Object aroundJob(ProceedingJoinPoint joinPoint) throws Throwable {
        //初始化MDC数据
        MdcUtil.initMdc();
        //添加cat监控
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        String requestFlag = className + UtilConstant.CAT_SEPARATOR + methodName;
        Object proceed = null;
        LogObject logObject = new LogObject().setInvokerName(JOB_INVOKER_NAME)
                .setInvokerIp(IpUtil.getIp())
                .setEventName(requestFlag)
                .setTraceId(MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME))
                .setRequest(JSON.toJSONString(String.valueOf(joinPoint.getArgs()[0])));
        Transaction t = Cat.newTransaction("SERVICE", requestFlag);
        t.setStatus(Transaction.SUCCESS);
        String msg = EXECUTE_SUCCESS;
        try {
            //执行方法
            proceed = joinPoint.proceed();
        } catch (Throwable e) {
            msg = e.getMessage();
            t.setStatus(e);
            Cat.logError(e);
            log.error(requestFlag + "方法异常:", e);
            throw e;
        } finally {
            t.complete();
            logObject.setResponse(JSON.toJSONString(proceed))
                    .setMsg(msg)
                    .setCostTime(System.currentTimeMillis() - Long.parseLong(MDC.get(UtilConstant.Mdc.START_TIME)));
            log.info(JSON.toJSONString(logObject));
            MdcUtil.clearMdc();
        }
        return proceed;
    }
}
