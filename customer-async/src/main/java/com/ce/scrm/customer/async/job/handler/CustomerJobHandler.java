package com.ce.scrm.customer.async.job.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.async.job.entity.CustomerCleanDto;
import com.ce.scrm.customer.async.service.ICustomerAsyncService;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.support.mq.RocketMqOperate;
import com.ce.scrm.customer.util.thread.core.TaskManager;
import com.ce.scrm.customer.util.thread.core.ThreadFactoryManager;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 客户数据任务处理器
 * <AUTHOR>
 * @date 2023/6/25 16:42
 * @version 1.0.0
 **/
@Slf4j
@Component
public class CustomerJobHandler {

    @Resource
    private Environment environment;

    /**
     * 需要清洗的客户数据
     */
    private final static Deque<Long> NEED_HANDLER_CUSTOMER_ID_DEQUE = new ConcurrentLinkedDeque<>();

    /**
     * 获取无效客户线程
     */
    private static Thread getInvalidCustomerTread;

    /**
     * 清洗无效客户线程
     */
    private static Thread cleanInvalidCustomerTread;

    /**
     * 分页查询结束标记
     */
    private volatile boolean endFlag;

    /**
     * 清洗无效客户总数量
     */
    private static AtomicLong cleanInvalidCustomerTotal;

    /**
     * 有效的客户ID
     */
    public static Set<String> VALID_CUSTOMER_ID = new HashSet<>(2700000);

    @Resource
    private ICustomerAsyncService customerAsyncService;

    @Resource
    private RocketMqOperate rocketMqOperate;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private static final String REFRESH_CUSTOMER_NAME_INDEX_KEY = "REFRESH_CUSTOMER_NAME_INDEX_KEY:";

    @PostConstruct
    public void init() {
        long startTime = System.currentTimeMillis();
        String filePath = "valid_customer_id_" + environment.getActiveProfiles()[0] + ".txt";
        String s;
        try {
            org.springframework.core.io.Resource resource = new ClassPathResource(filePath);
            InputStream is = resource.getInputStream();
            InputStreamReader isr = new InputStreamReader(is);
            BufferedReader br = new BufferedReader(isr);
            while ((s = br.readLine()) != null) {
                VALID_CUSTOMER_ID.add(s);
            }
        } catch (IOException e) {
            log.error("{} 文件未找到", filePath);
            throw new RuntimeException(filePath + " 文件未找到");
        }
        log.info("VALID_CUSTOMER_ID加载成功,数据量为:{},加载耗时为:{}", VALID_CUSTOMER_ID.size(), System.currentTimeMillis() - startTime);

    }

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.CLEAN_CUSTOMER_JOB_HANDLER)
    public ReturnT<String> cleanCustomerJobHandler(String param) throws InterruptedException {
        if (StrUtil.isBlank(param)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "参数不能为空");
        }
        if (CollectionUtil.isEmpty(VALID_CUSTOMER_ID)) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "项目未配置有效的客户ID数据");
        }
        CustomerCleanDto customerCleanDto = JSON.parseObject(param, CustomerCleanDto.class);
        if (customerCleanDto.getDeadline() == null) {
            return new ReturnT<>(ReturnT.FAIL_CODE, "清洗数据的截止时间不能为空");
        }
        cleanInvalidCustomerTotal = new AtomicLong(0);
        createGetInvalidCustomerThread(customerCleanDto);
        createCleanInvalidCustomerTread();
        getInvalidCustomerTread.join();
        cleanInvalidCustomerTread.join();
        log.error("处理成功, 总数量为:{}", cleanInvalidCustomerTotal.get());
        return new ReturnT<>(ReturnT.SUCCESS_CODE, "处理成功, 总数量为:" + cleanInvalidCustomerTotal.get());
    }

    /**
     * 获取无效客户数据
     * @param customerCleanDto  清洗数据参数
     * <AUTHOR>
     * @date 2023/6/27 16:18
     **/
    private void createGetInvalidCustomerThread(CustomerCleanDto customerCleanDto) {
        ThreadFactoryManager threadFactoryManager = new ThreadFactoryManager("get-invalid-customer-thread-", true);
        getInvalidCustomerTread = threadFactoryManager.newThread(new TaskManager("get-invalid-customer-task", () -> {
            long startId = 0;
            long pageSize = 10000;
            while (true) {
                try {
                    TimeUnit.MILLISECONDS.sleep(1);
                } catch (InterruptedException e) {
                    log.error("获取无效客户线程发生中断，停止任务");
                    Thread.currentThread().interrupt();
                    break;
                }
                Page<Customer> page;
                try {
                    page = customerAsyncService.page(customerCleanDto.getCustomerId(), customerCleanDto.getDeadline(), 1, pageSize, startId);
                } catch (Exception e) {
                    if (e.getMessage().contains("java.sql.SQLException: interrupt")) {
                        log.warn("获取无效客户异常", e);
                        Thread.currentThread().interrupt();
                        continue;
                    }
                    throw e;
                }
                List<Customer> customerList = page.getRecords();
                if (CollectionUtil.isEmpty(customerList)) {
                    break;
                }
                startId = customerList.get(customerList.size() - 1).getId();
                List<Long> invalidCustomerList = customerList.stream().filter(customer -> !VALID_CUSTOMER_ID.contains(customer.getCustomerId())).map(Customer::getId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(invalidCustomerList)) {
                    NEED_HANDLER_CUSTOMER_ID_DEQUE.addAll(invalidCustomerList);
                }
            }
            endFlag = true;
        }));
        getInvalidCustomerTread.start();
    }

    /**
     * 异步清洗无效客户数据
     * <AUTHOR>
     * @date 2023/6/27 16:20
     **/
    private void createCleanInvalidCustomerTread() {
        ThreadFactoryManager threadFactoryManager = new ThreadFactoryManager("clean-invalid-customer-", true);
        cleanInvalidCustomerTread = threadFactoryManager.newThread(new TaskManager("clean-invalid-customer-task", () -> {
            while (!endFlag || !NEED_HANDLER_CUSTOMER_ID_DEQUE.isEmpty()) {
                try {
                    TimeUnit.MILLISECONDS.sleep(1);
                } catch (InterruptedException e) {
                    log.error("设置客户无效标记线程发生中断，停止任务");
                    Thread.currentThread().interrupt();
                    break;
                }
                List<Long> invalidCustomerIdList = new ArrayList<>();
                if (NEED_HANDLER_CUSTOMER_ID_DEQUE.isEmpty()) {
                    try {
                        TimeUnit.SECONDS.sleep(1);
                        continue;
                    } catch (InterruptedException e) {
                        log.error("设置客户无效标记线程发生中断，停止任务");
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
                int size = Math.min(NEED_HANDLER_CUSTOMER_ID_DEQUE.size(), 20000);
                for (int i = 0; i < size; i++) {
                    invalidCustomerIdList.add(NEED_HANDLER_CUSTOMER_ID_DEQUE.remove());
                }
                try {
                    customerAsyncService.batchSetInvalidCustomer(invalidCustomerIdList);
                } catch (Exception e) {
                    if (e.getMessage().contains("java.sql.SQLException: interrupt")) {
                        log.warn("设置客户无效标记异常", e);
                        Thread.currentThread().interrupt();
                        continue;
                    }
                    throw e;
                }
                cleanInvalidCustomerTotal.addAndGet(invalidCustomerIdList.size());
            }
        }));
        cleanInvalidCustomerTread.start();
    }

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.STOP_CLEAN_CUSTOMER_JOB_HANDLER)
    public ReturnT<String> stopCleanCustomerJobHandler(String param) throws InterruptedException {
        if (getInvalidCustomerTread.isAlive()) {
            log.error("开始停止获取无效客户任务线程");
            getInvalidCustomerTread.interrupt();
        }
        if (cleanInvalidCustomerTread.isAlive()) {
            log.error("开始停止设置无效客户任务线程");
            cleanInvalidCustomerTread.interrupt();
        }
        while (getInvalidCustomerTread.isAlive() || cleanInvalidCustomerTread.isAlive()) {
            TimeUnit.MILLISECONDS.sleep(10);
        }
        getInvalidCustomerTread = null;
        cleanInvalidCustomerTread = null;
        log.error("清洗任务停止成功");
        return new ReturnT<>(ReturnT.SUCCESS_CODE, "清洗任务停止成功");
    }

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.TRANSFER_INVALID_CUSTOMER_JOB_HANDLER)
    public ReturnT<String> transferInvalidCustomerJobHandler(String param) {
        int size;
        if (StrUtil.isBlank(param) || (size = Integer.parseInt(param)) <= 0) {
            size = 100;
        }
        //无效客户打标
        List<Long> invalidCustomerTableIdList = customerAsyncService.setInvalidCustomerDealFlag(size);
        if (CollectionUtil.isEmpty(invalidCustomerTableIdList)) {
            return new ReturnT<>(ReturnT.SUCCESS_CODE, "迁移无效客户结束");
        }
        List<List<Long>> invalidCustomerTableIdsList = IntStream.range(0, invalidCustomerTableIdList.size())
                .boxed().collect(Collectors.groupingBy(index -> index / 100))
                .values().stream()
                .map(indices -> indices.stream().map(invalidCustomerTableIdList::get).collect(Collectors.toList()))
                .collect(Collectors.toList());
        invalidCustomerTableIdsList.forEach(invalidCustomerTableIds -> {
            //发送mq处理每个客户
            SendResult sendResult = rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CUSTOMER_TOPIC + ServiceConstant.MqConstant.TOPIC_TAG_SEPARATOR + ServiceConstant.MqConstant.Tag.TRANSFER_INVALID_CUSTOMER_TAG, JSON.toJSONString(invalidCustomerTableIds));
            if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                log.error("发送迁移无效客户消息失败,失败状态为:{},发送的消息为:{}", sendResult.getSendStatus(), JSON.toJSONString(invalidCustomerTableIdList));
            }
        });
        return new ReturnT<>(ReturnT.SUCCESS_CODE, "迁移无效客户mq发送成功");
    }

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.REFRESH_CUSTOMER_NAME_JOB_HANDLER)
    public ReturnT<String> refreshNameAndUnCid(String param) {
        Long index = stringRedisTemplate.opsForValue().increment(REFRESH_CUSTOMER_NAME_INDEX_KEY);
        if (index == null) {
            index = 1L;
        }
        Page<Customer> page = customerService.page(new Page<>(index, 1000),
                new LambdaQueryWrapper<Customer>().orderByAsc(Customer::getCreateTime));
        List<Customer> records = page.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            for (Customer record : records) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("id", record.getId());
                jsonObject.put("customerId", record.getCustomerId());
                jsonObject.put("custName", record.getCustomerName());
                jsonObject.put("pid", record.getSourceDataId());
                jsonObject.put("uncid", record.getCertificateCode());
                rocketMqOperate.syncSend(ServiceConstant.MqConstant.Topic.CUSTOMER_SCRM_REFRESH_CUSTOMER_NAME_TOPIC, JSONObject.toJSONString(jsonObject));
            }
            if (records.size() < 1000) {
                stringRedisTemplate.opsForValue().decrement(REFRESH_CUSTOMER_NAME_INDEX_KEY);
            }
        } else {
            stringRedisTemplate.opsForValue().decrement(REFRESH_CUSTOMER_NAME_INDEX_KEY);
        }
        return new ReturnT<>(ReturnT.SUCCESS_CODE, "刷新客户名称&unCid任务mq发送成功");
    }
}
