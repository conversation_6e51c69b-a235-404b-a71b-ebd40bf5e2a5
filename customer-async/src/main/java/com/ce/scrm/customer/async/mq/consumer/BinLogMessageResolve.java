package com.ce.scrm.customer.async.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;

import java.util.List;
import java.util.Objects;

import static com.ce.scrm.customer.async.mq.consumer.TableChangeEnum.getTableChangeEnumByField;

@Slf4j
public class BinLogMessageResolve {

    /**
     * @param messageExt     message消息
     * @param operatorTypes  要操作的类型
     * @param resolveMessage 方法实现
     */
    public static void resolveBinLogMessage(MessageExt messageExt, List<TableChangeEnum> operatorTypes, ResolveMessage resolveMessage) {
        String data = new String(messageExt.getBody());
        if (StringUtils.isEmpty(data)) {
            return;
        }
        try {
            JSONObject binlogJson = JSON.parseObject(data);
            Boolean isDdl = binlogJson.getBoolean("isDdl");
            if (isDdl) {
                return;
            }
            String type = binlogJson.getString("type");
            if (!operatorTypes.contains(getTableChangeEnumByField(type))) {
                return;
            }
            JSONArray afterAry = binlogJson.getJSONArray("data");
            Long tstime = binlogJson.getLong("ts");
            JSONArray beforeAry = null;
            if (Objects.equals(TableChangeEnum.update.getField(), type.toUpperCase())) {
                beforeAry = binlogJson.getJSONArray("old");
            }
            int size = afterAry.size();
            Exception hasException = null;
            for (int i = 0; i < size; i++) {
                JSONObject afterJson = afterAry.getJSONObject(i);
                afterJson.put("tstime", tstime);
                JSONObject beforeJson = null;
                if (null != beforeAry) {
                    beforeJson = beforeAry.getJSONObject(i);
                }
                try {
                    resolveMessage.resolveMessage(beforeJson, afterJson, getTableChangeEnumByField(type));
                } catch (Exception exception) {
                    hasException = exception;
                }
            }
            if (null != hasException) {
                throw new RuntimeException(hasException);
            }
        } catch (Exception e) {
            if (messageExt.getReconsumeTimes() > 9) {
                log.error("处理RocketMq消息失败! ,{}", e.getMessage());
            }
            throw new RuntimeException(e);
        }
    }

    public interface ResolveMessage {
        /**
         * @param beforeJson      更新之前的json
         * @param afterJson       更新之后的json
         * @param tableChangeEnum 表的操作类型
         */
        void resolveMessage(JSONObject beforeJson, JSONObject afterJson, TableChangeEnum tableChangeEnum) throws Exception;
    }

}
