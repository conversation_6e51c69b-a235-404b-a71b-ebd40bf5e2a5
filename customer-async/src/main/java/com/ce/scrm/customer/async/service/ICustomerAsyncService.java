package com.ce.scrm.customer.async.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.dao.entity.Customer;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 客户异步业务接口
 * <AUTHOR>
 * @date 2023/6/25 17:04
 * @version 1.0.0
 **/
public interface ICustomerAsyncService {

    /**
     * 根据客户ID分页获取客户数据
     * @param customerIdList    客户ID列表（非必填）
     * @param deadline  截止处理时间
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @param startId  开始ID
     * <AUTHOR>
     * @date 2023/6/25 17:06
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.customer.dao.entity.Customer>
     **/
    Page<Customer> page(List<String> customerIdList, LocalDateTime deadline, long pageNum, long pageSize, long startId);

    /**
     * 根据客户表主键ID列表设置客户为无效客户
     * @param customerIdList   客户ID列表
     * <AUTHOR>
     * @date 2023/6/26 11:17
     **/
    void batchSetInvalidCustomer(List<Long> customerIdList);

    /**
     * 获取客户有效标记
     * @param customerIdList    客户ID
     * <AUTHOR>
     * @date 2023/7/12 11:00
     * @return java.util.Map<java.lang.String, java.lang.Boolean>
     **/
    Map<String, Boolean> getCustomerValidFlag(List<String> customerIdList);

    /**
     * 获取要处理的客户列表
     *
     * @param customerTableIdList   客户表ID列表
     * <AUTHOR>
     * @date 2023/7/12 11:00
     * @return java.util.List<com.ce.scrm.customer.dao.entity.Customer>
     **/
    List<Customer> getCustomerDealFlag(List<Long> customerTableIdList);

    /**
     * 设置有效客户标记
     * @param customerId    客户ID
     * <AUTHOR>
     * @date 2023/7/11 15:43
     * @return boolean
     **/
    boolean setValidCustomerFlag(String customerId);

    /**
     * 获取无效客户数据
     * @param size  获取数量
     * <AUTHOR>
     * @date 2023/6/25 17:06
     * @return java.util.List<java.lang.Long>
     **/
    List<Long> setInvalidCustomerDealFlag(long size);

    /**
     * 迁移无效客户数据
     * @param customerTableId   客户表ID
     * <AUTHOR>
     * @date 2023/8/24 11:02
     **/
    void transferInvalidCustomerData(long customerTableId);
}