package com.ce.scrm.customer.async.mq.consumer.binlog;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;

import java.util.Arrays;

import static com.ce.scrm.customer.async.mq.utils.BinlogMessageResolve.resolveBinLogMessage;


/**
 * binlog 处理公共处理流程类
 */
@Slf4j
public abstract class AbstractBinlogConsumer{

    public void dealBinlogMqMessage(MessageExt messageExt) {
        resolveBinLogMessage(messageExt,
                Arrays.asList(TableChangeEnum.insert, TableChangeEnum.update, TableChangeEnum.delete),
                (msgId,before, after, type) -> {
                    this.dealEvent(msgId,before, after, type.getField());
                });
    }

    public abstract void dealEvent(String msgId,JSONObject beforeJ<PERSON>, JSONObject afterJson, String type);


}
