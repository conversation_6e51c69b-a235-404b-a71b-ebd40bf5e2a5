package com.ce.scrm.customer.async.mq.entity;

import java.io.Serializable;
import java.util.Map;

public class IncrementSyncDto implements Serializable {

    private Integer dataType;

    private Integer syncDeleteFlag = 0;

    private Map<String, Object> data;

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public Integer getSyncDeleteFlag() {
        return syncDeleteFlag;
    }

    public void setSyncDeleteFlag(Integer syncDeleteFlag) {
        this.syncDeleteFlag = syncDeleteFlag;
    }
}
