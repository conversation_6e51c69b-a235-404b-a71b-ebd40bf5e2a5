package com.ce.scrm.customer.async.mq.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description:
 * @Author: lijinpeng
 * @Date: 2025/3/24 14:02
 */
@Data
public class CustomerImportMqDto implements Serializable {

    private Long id;

    private String customerId;

    private String customerName;

    private Integer startFlag;

    private Integer addFlag;

    private String failCause;

    private Date createTime;

    private Date updateTime;

}
