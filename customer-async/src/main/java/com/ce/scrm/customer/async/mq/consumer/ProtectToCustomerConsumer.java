package com.ce.scrm.customer.async.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.ce.scrm.customer.async.mq.consumer.binlog.AbstractBinlogConsumer;
import com.ce.scrm.customer.async.mq.consumer.binlog.TableChangeEnum;
import com.ce.scrm.customer.async.mq.entity.CmCustProtect;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.cache.handler.CustomerCacheHandler;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description: 监听保护关系表binlog，修改客户表数据
 * @Author: 李金澎
 * @Date: 2020/12/23
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CM_CUST_PROTECT_TOPIC
        , consumerGroup = ServiceConstant.MqConstant.Group.PROTECT_TO_CUSTOMER_GROUP
        , consumeThreadMax = 3)
public class ProtectToCustomerConsumer extends AbstractBinlogConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CustomerCacheHandler customerCacheHandler;

    @Resource
    private CustomerService customerService;

    @Override
    public void onMessage(MessageExt messageExt) {
        super.dealBinlogMqMessage(messageExt);
    }


    @Override
    public void dealEvent(String msgId, JSONObject beforeJson, JSONObject afterJson, String type) {
        if (Objects.equals(type, TableChangeEnum.insert.getField())) {
            insertStart(afterJson);
        } else if (Objects.equals(type, TableChangeEnum.update.getField())) {
            updateStart(beforeJson, afterJson);
        } else if (Objects.equals(type, TableChangeEnum.delete.getField())) {

        }
    }

    private void updateStart(JSONObject beforeJson, JSONObject afterJson) {
        CmCustProtect oldCmCustProtect = JSONObject.parseObject(beforeJson.toJSONString(), CmCustProtect.class);
        CmCustProtect newCmCustProtect = JSONObject.parseObject(afterJson.toJSONString(), CmCustProtect.class);
        String customerId = newCmCustProtect.getCustId();
        if (StringUtils.isBlank(customerId)) {
            log.info("客户id不能为空");
            return;
        }

        LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerId);

        basics(updateChainWrapper, newCmCustProtect, oldCmCustProtect);

        updateChainWrapper.update();
        customerCacheHandler.del(customerId);

    }

    private void basics(LambdaUpdateChainWrapper<Customer> updateChainWrapper, CmCustProtect newCmCustProtect, CmCustProtect oldCmCustProtect) {
        updateChainWrapper.set(Customer::getProtectStatus, newCmCustProtect.getStatus() == null ? null : String.valueOf(newCmCustProtect.getStatus()));
        updateChainWrapper.set(Customer::getProtectCustType, newCmCustProtect.getCustType() == null ? null : String.valueOf(newCmCustProtect.getCustType()));
        updateChainWrapper.set(Customer::getOperator,"1025");
        updateChainWrapper.set(Customer::getBindFlag, newCmCustProtect.getBindFlag());
        updateChainWrapper.set(Customer::getBusinessOpportunityConfirmationFlag, newCmCustProtect.getBusinessOpportunityConfirmationFlag());

        // 保护关系变化
        boolean b = oldCmCustProtect.getStatus()!=null && oldCmCustProtect.getStatus()==1  && newCmCustProtect.getStatus()!=1;
        if (b || !Objects.equals(oldCmCustProtect.getSalerId(), newCmCustProtect.getSalerId())) {
            updateChainWrapper.set(Customer::getSdrFollowCount,0);
            updateChainWrapper.set(Customer::getLastSdrFollowTime,null);
            updateChainWrapper.set(Customer::getDistributeChannel,null);
            updateChainWrapper.set(Customer::getDistributeTime,null);
        }

        if (Objects.equals(newCmCustProtect.getStatus(),1)) {
            // 保护中
            updateChainWrapper.set(Customer::getProtectSalerId, newCmCustProtect.getSalerId());
            updateChainWrapper.set(Customer::getProtectBussdeptId, newCmCustProtect.getBussdeptId());
            updateChainWrapper.set(Customer::getProtectBuId, newCmCustProtect.getBuId());
            updateChainWrapper.set(Customer::getProtectSubcompanyId, newCmCustProtect.getSubcompanyId());
            updateChainWrapper.set(Customer::getProtectAreaId, newCmCustProtect.getAreaId());

            updateChainWrapper.set(Customer::getProtectProtectTime, newCmCustProtect.getProtectTime());
            updateChainWrapper.set(Customer::getProtectProtectendTime, newCmCustProtect.getExceedTime());
        } else if (Objects.equals(newCmCustProtect.getStatus(),2)) {
            // 总监待分配
            updateChainWrapper.set(Customer::getProtectSalerId,null);
            updateChainWrapper.set(Customer::getProtectBussdeptId,null);
            updateChainWrapper.set(Customer::getProtectBuId,null);
            updateChainWrapper.set(Customer::getProtectSubcompanyId, newCmCustProtect.getSubcompanyId());
            updateChainWrapper.set(Customer::getProtectAreaId, newCmCustProtect.getAreaId());

            updateChainWrapper.set(Customer::getProtectProtectTime,null);
        } else if (Objects.equals(newCmCustProtect.getStatus(),3)) {
            // 部门经理待分配
            updateChainWrapper.set(Customer::getProtectSalerId,null);
            updateChainWrapper.set(Customer::getProtectBussdeptId, newCmCustProtect.getBussdeptId());
            updateChainWrapper.set(Customer::getProtectBuId, newCmCustProtect.getBuId());
            updateChainWrapper.set(Customer::getProtectSubcompanyId, newCmCustProtect.getSubcompanyId());
            updateChainWrapper.set(Customer::getProtectAreaId, newCmCustProtect.getAreaId());

            updateChainWrapper.set(Customer::getProtectProtectTime,null);
        } else if (Objects.equals(newCmCustProtect.getStatus(),4)) {
            // 客户池
            updateChainWrapper.set(Customer::getProtectSalerId,null);
            updateChainWrapper.set(Customer::getProtectBussdeptId,null);
            updateChainWrapper.set(Customer::getProtectBuId,null);
            updateChainWrapper.set(Customer::getProtectSubcompanyId,null);
            updateChainWrapper.set(Customer::getProtectAreaId,null);

            updateChainWrapper.set(Customer::getProtectProtectTime,null);
        } else if (Objects.equals(newCmCustProtect.getStatus(),5)) {
            // 事业部总监待分配
            updateChainWrapper.set(Customer::getProtectSalerId,null);
            updateChainWrapper.set(Customer::getProtectBussdeptId,null);
            updateChainWrapper.set(Customer::getProtectBuId, newCmCustProtect.getBuId());
            updateChainWrapper.set(Customer::getProtectSubcompanyId, newCmCustProtect.getSubcompanyId());
            updateChainWrapper.set(Customer::getProtectAreaId, newCmCustProtect.getAreaId());

            updateChainWrapper.set(Customer::getProtectProtectTime,null);
        }
    }

    private void insertStart(JSONObject afterJson) {
        CmCustProtect newCmCustProtect = JSONObject.parseObject(afterJson.toJSONString(), CmCustProtect.class);
        String customerId = newCmCustProtect.getCustId();
        if (StringUtils.isBlank(customerId)) {
            log.info("客户id不能为空");
            return;
        }

        LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerId);

        basics(updateChainWrapper, newCmCustProtect,new CmCustProtect());

        updateChainWrapper.update();
        customerCacheHandler.del(customerId);
    }
}