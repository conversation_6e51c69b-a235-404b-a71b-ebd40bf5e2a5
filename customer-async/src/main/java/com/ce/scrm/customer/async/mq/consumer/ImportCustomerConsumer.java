package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ce.scrm.customer.async.mq.entity.CustomerImportMqDto;
import com.ce.scrm.customer.dao.entity.CustomerImport;
import com.ce.scrm.customer.dao.service.CustomerImportService;
import com.ce.scrm.customer.dubbo.api.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerAddDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerConditionDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.CustomerAddDubboView;
import com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @version 1.0
 * @Description: 导入客户
 * @Author: lijinpeng
 * @Date: 2025/3/24 11:12
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.SCRM_CUSTOMER_IMPORT_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.SCRM_CUSTOMER_IMPORT_GROUP)
public class ImportCustomerConsumer implements RocketMQListener<String> {

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    ICustomerDubbo customerDubbo;

    @Value("${channel.sourceSecret}")
    private String sourceSecret;

    @Resource
    private CustomerImportService customerImportService;

    @Override
    public void onMessage(String msg) {

        if (StringUtils.isBlank(msg)) {
            return;
        }

        JSONObject jsonObject = JSONObject.parseObject(msg);

        if(jsonObject == null) {
            return;
        }

        String type = jsonObject.getString("type");

        if(!Objects.equals(type,"INSERT")) {
            return;
        }

        String string = jsonObject.getString("data");
        if (StringUtils.isBlank(string)) {
            return;
        }
        List<CustomerImportMqDto> customerImportMqDtos = JSONArray.parseArray(string, CustomerImportMqDto.class);
        if (CollectionUtil.isEmpty(customerImportMqDtos)) {
            return;
        }
        for (CustomerImportMqDto mqDto : customerImportMqDtos) {
            if (mqDto == null || StringUtils.isBlank(mqDto.getCustomerName())) {
                continue;
            }
            CustomerConditionDubboDto customerConditionDubboDto = new CustomerConditionDubboDto();
            customerConditionDubboDto.setCustomerName(mqDto.getCustomerName());
            customerConditionDubboDto.setSourceKey("scrm");
            customerConditionDubboDto.setSourceSecret(sourceSecret);
            DubboResult<List<CustomerDubboView>> byCondition = customerDubbo.findByCondition(customerConditionDubboDto);
            if (Objects.nonNull(byCondition) && byCondition.checkSuccess() && CollectionUtil.isNotEmpty(byCondition.getData())){
                List<CustomerDubboView> customerDubboViews = byCondition.getData();
                mqDto.setStartFlag(1);
                mqDto.setAddFlag(1);
                mqDto.setCustomerId(customerDubboViews.get(0).getCustomerId());
            }else{
                CustomerAddDubboDto addDubboDto = new CustomerAddDubboDto();
                addDubboDto.setCustomerName(mqDto.getCustomerName());
                //国内企业
                addDubboDto.setCustomerType(1);
                //操作用户
                addDubboDto.setCreator("1024");
                //customerImport
                addDubboDto.setCreateWay(6);
                //线索阶段
                addDubboDto.setPresentStage(1);
                addDubboDto.setSourceKey("scrm");
                addDubboDto.setSourceSecret(sourceSecret);
                addDubboDto.setCertificateType(1);
                DubboResult<CustomerAddDubboView> addDubboViewDubboResult = customerDubbo.add(addDubboDto);
                mqDto.setStartFlag(1);
                if(addDubboViewDubboResult == null) {
                    continue;
                }
                if (addDubboViewDubboResult.checkSuccess()) {
                    if (addDubboViewDubboResult.getData() != null) {
                        mqDto.setAddFlag(1);
                        mqDto.setCustomerId(addDubboViewDubboResult.getData().getCustomerId());
                    }
                }else {
                    mqDto.setAddFlag(0);
                    mqDto.setFailCause(addDubboViewDubboResult.getMsg());
                }
            }
            CustomerImport customerImport = BeanUtil.copyProperties(mqDto, CustomerImport.class);
            boolean update = customerImportService.updateById(customerImport);
        }

    }

}
