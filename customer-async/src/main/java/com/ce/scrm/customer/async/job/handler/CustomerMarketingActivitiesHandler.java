package com.ce.scrm.customer.async.job.handler;

import com.ce.scrm.customer.dao.service.abm.CustomerMarketingActivitiesService;
import com.ce.scrm.customer.service.business.ICustomerMarketingActivitiesRecordBusiness;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 跨境ABM 客户营销活动任务
 * <AUTHOR>
 * @date 2025/9/7 21:11
 * @version 1.0.0
 */
@Slf4j
@Component
public class CustomerMarketingActivitiesHandler {

    @Resource
    private ICustomerMarketingActivitiesRecordBusiness customerMarketingActivitiesRecordBusiness;
	@Resource
    private CustomerMarketingActivitiesService customerMarketingActivitiesservice;


    /**
     * 开始执行营销活动【短信、邮件...】
     * @param param 任务参数
     * <AUTHOR>
     * @date 2025/7/10 11:44
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     **/
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_CUSTOMER_MARKETING_ACTIVITIES_JOB_HANDLER)
    public ReturnT<String> startActivities(String param) {
	    if (StringUtils.isNotBlank(param)) {
		    Long activityId = Long.valueOf(param);
		    customerMarketingActivitiesRecordBusiness.executeActivities(activityId);
			return ReturnT.SUCCESS;
	    }
		log.error("开始执行营销活动XXL执行失败，未获取到参数param={}", param);
	    return ReturnT.FAIL;
    }

}