package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.async.job.handler.CustomerJobHandler;
import com.ce.scrm.customer.async.service.ICustomerAsyncService;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.service.third.invoke.NewCustomerThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 无效客户迁移消费
 * <AUTHOR>
 * @date 2023/8/24 10:41
 * @version 1.0.0
 **/
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CUSTOMER_TOPIC, selectorExpression = ServiceConstant.MqConstant.Tag.TRANSFER_INVALID_CUSTOMER_TAG, consumerGroup = ServiceConstant.MqConstant.Group.TRANSFER_INVALID_CUSTOMER_GROUP)
public class InvalidCustomerTransferConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private NewCustomerThirdService newCustomerThirdService;

    @Resource
    private ICustomerAsyncService customerAsyncService;

    @Override
    public void onMessage(MessageExt messageExt) {
        String param = new String(messageExt.getBody());
        if (StrUtil.isBlank(param)) {
            log.error("无效客户迁移mq，参数不能为空，消息ID为:{}", messageExt.getMsgId());
            return;
        }
        //获取要处理的客户表ID列表
        List<Long> customerTableIdList = JSON.parseArray(param, Long.class);
        //判断这些无效客户目前是否有效
        List<Customer> invalidCustomerTransferList = customerAsyncService.getCustomerDealFlag(customerTableIdList);
        invalidCustomerTransferList.forEach(customer -> {
            String customerId = customer.getCustomerId();
            //1、判断如果是财务、会员、高呈导入的数据，则是为有效数据
            //2、无效客户进一步查询客户池、经理（总监待分配）、保护关系表判断是否存在，存在则为有效
            boolean validFlag = CustomerJobHandler.VALID_CUSTOMER_ID.contains(customerId) || newCustomerThirdService.checkValidCustomer(customerId);
            //如果是有效数据，则修改客户标记为有效
            if (validFlag) {
                customerAsyncService.setValidCustomerFlag(customerId);
            }
            //如果确定为无效数据，则迁移数据
            if (!validFlag) {
                customerAsyncService.transferInvalidCustomerData(customer.getId());
            }
        });
    }
}