package com.ce.scrm.customer.async.mq.consumer;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/27
 */
@Data
public class CustomerBinLog extends EventBaseEntity implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户类型：1、国内企业，2、个人，3、国外及港澳台
     */
    private Integer customerType;

    /**
     * 客户/企业名称
     */
    private String customerName;

    /**
     * 证件编码
     */
    private String certificateCode;
}
