package com.ce.scrm.customer.async.job.handler;

import cn.ce.cesupport.cma.dto.SendContentParamDto;
import cn.ce.cesupport.cma.service.CustomerShareAppService;
import cn.ce.cesupport.cma.vo.SendContentRecordView;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ce.scrm.customer.dao.entity.CustomerShareTemporary;
import com.ce.scrm.customer.dao.service.CustomerShareTemporaryService;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description: 客户分享每天为了计算流转客户，入一次mysql
 * @Author: lijinpeng
 * @Date: 2024/10/8 11:04
 */
@Slf4j
@Component
public class CustomerShareTemporaryJobHandler {

    @DubboReference
    private CustomerShareAppService customerShareAppService;

    @Resource
    private CustomerShareTemporaryService customerShareTemporaryService;

    /**
     * 客户分享每天为了计算流转客户，入一次redis
     * @param param
     * @return
     */
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SCRM_CACHE_SHARE_DATA_JOB_HANDLER)
    public ReturnT<String> getCacheShareData(String param) {
        log.info("===========客户分享内容缓存-开始==========");

        // 获取业务平台近三个月的数据
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 获取三个自然月前的时间
        LocalDateTime threeMonthsAgo = now.minusMonths(3);

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 格式化时间
        String currentTime = now.format(formatter);
        String threeMonthsAgoTime = threeMonthsAgo.format(formatter);

        List<SendContentRecordView> shareData = new ArrayList<>();

        int pageIndex = 0;
        int pageSize = 1000;
        SendContentParamDto queryCondition = new SendContentParamDto();
        queryCondition.setPageSize(pageSize);
        queryCondition.setStartDateTime(threeMonthsAgoTime);
        queryCondition.setEndDateTime(currentTime);
        queryCondition.setPrivilegeId(9);
        List<SendContentRecordView> itemData;
        do {
            queryCondition.setPageIndex(++pageIndex);
            itemData = customerShareAppService.selectContentListByCondition(queryCondition);
            if (CollectionUtils.isNotEmpty(itemData)) {
                shareData.addAll(itemData);
            }
        } while (itemData != null && itemData.size() == pageSize);
        log.info("getCacheShareData size={}", shareData.size());

        List<CustomerShareTemporary> customerShareDtoList = shareData.stream().map(item -> {
            CustomerShareTemporary customerShareTemporary = new CustomerShareTemporary();

            customerShareTemporary.setCustomerId(item.getCustId());
            customerShareTemporary.setSalerId(item.getCreatorId());
            customerShareTemporary.setDeptId(item.getBussdeptId());
            customerShareTemporary.setBuId(item.getBuId());
            customerShareTemporary.setSubId(item.getSubcompanyId());
            customerShareTemporary.setAreaId(item.getAreaId());
            if (StringUtils.isNotBlank(item.getCreateTime())) {
                LocalDateTime localDateTime = LocalDateTime.parse(item.getCreateTime(), formatter);
                Date shareTime = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
                customerShareTemporary.setShareTime(shareTime);
            }
            return customerShareTemporary;
        }).collect(Collectors.toList());

        Wrapper<CustomerShareTemporary> queryWrapper = new QueryWrapper<>();
        customerShareTemporaryService.remove(queryWrapper);
        customerShareTemporaryService.saveBatch(customerShareDtoList);

        log.info("===========客户分享内容缓存-结束==========");
        return ReturnT.SUCCESS;
    }



}
