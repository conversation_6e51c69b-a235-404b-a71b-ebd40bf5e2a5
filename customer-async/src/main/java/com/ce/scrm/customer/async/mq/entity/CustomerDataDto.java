package com.ce.scrm.customer.async.mq.entity;

import lombok.Data;

@Data
public class CustomerDataDto {
    private String customerId;
    private String sourceDataId;
    private String customerType;
    private String customerName;
    private String certificateType;
    private String certificateCode;
    private String staffScale;
    private String provinceCode;
    private String cityCode;
    private String districtCode;
    private String firstIndustryCode;
    private String secondIndustryCode;
    private String registerAddress;
    private String createWay;
    private String deleteFlag;
    private String createTime;
    private String creatorKey;
    private String creator;
    private String updateTime;
    private String operatorKey;
    private String operator;
}