package com.ce.scrm.customer.async.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;

import java.util.Arrays;
import java.util.List;

import static com.ce.scrm.customer.async.mq.consumer.BinLogMessageResolve.resolveBinLogMessage;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/27
 */
@Slf4j
public abstract class AbstractEventConsumer<Entity extends EventBaseEntity> {

    public void dealBinlogMqMessage(MessageExt messageExt) {
        resolveBinLogMessage(messageExt,
                Arrays.asList(TableChangeEnum.insert, TableChangeEnum.update, TableChangeEnum.delete),
                (before, after, type) -> {
                    this.dealEvent(before, after, type.getField());
                });
    }

    public abstract List<Entity> dealEvent(JSONObject beforeJson, JSONObject afterJson, String type);
}
