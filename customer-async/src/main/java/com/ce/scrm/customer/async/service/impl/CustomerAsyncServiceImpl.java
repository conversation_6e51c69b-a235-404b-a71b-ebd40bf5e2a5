package com.ce.scrm.customer.async.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.async.service.ICustomerAsyncService;
import com.ce.scrm.customer.cache.access.BaseManager;
import com.ce.scrm.customer.cache.lock.Lock;
import com.ce.scrm.customer.dao.entity.*;
import com.ce.scrm.customer.dao.service.*;
import com.ce.scrm.customer.service.cache.handler.ContactPersonCacheHandler;
import com.ce.scrm.customer.service.cache.handler.CustomerCacheHandler;
import com.ce.scrm.customer.service.cache.handler.CustomerContactCacheHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 客户异步业务实现
 * <AUTHOR>
 * @date 2023/6/25 17:08
 * @version 1.0.0
 **/
@Slf4j
@Service
public class CustomerAsyncServiceImpl implements ICustomerAsyncService {
    /**
     * 无效客户迁移锁名称
     */
    private final static String INVALID_CUSTOMER_TRANSFER_LOCK_NAME = "INVALID_CUSTOMER_TRANSFER_LOCK_NAME";

    @Resource
    private CustomerService customerService;

    @Resource
    private CustomerBackService customerBackService;

    @Resource
    private CustomerContactService customerContactService;

    @Resource
    private CustomerContactBackService customerContactBackService;

    @Resource
    private ContactPersonService contactPersonService;

    @Resource
    private ContactPersonBackService contactPersonBackService;

    @Resource
    private ContactInfoService contactInfoService;

    @Resource
    private ContactInfoBackService contactInfoBackService;

    @Resource
    private CustomerCacheHandler customerCacheHandler;

    @Resource
    private CustomerContactCacheHandler customerContactCacheHandler;

    @Resource
    private ContactPersonCacheHandler contactPersonCacheHandler;

    @Resource
    private BaseManager baseManager;

    /**
     * 根据客户ID和截止创建时间分页获取客户数据
     * @param customerIdList    客户ID列表（非必填）
     * @param deadline  截止处理时间
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @param startId 开始ID
     * <AUTHOR>
     * @date 2023/6/25 17:06
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.customer.dao.entity.Customer>
     **/
    @Override
    public Page<Customer> page(List<String> customerIdList, LocalDateTime deadline, long pageNum, long pageSize, long startId) {
        LambdaQueryWrapper<Customer> customerLambdaQueryWrapper = Wrappers.lambdaQuery();
        if (CollectionUtil.isNotEmpty(customerIdList)) {
            customerLambdaQueryWrapper.in(Customer::getCustomerId, customerIdList);
        }
        customerLambdaQueryWrapper.gt(Customer::getId, startId);
        customerLambdaQueryWrapper.le(Customer::getCreateTime, deadline);
        customerLambdaQueryWrapper.eq(Customer::getInvalidFlag, 0);
        customerLambdaQueryWrapper.select(Customer::getCustomerId, Customer::getId);
        customerLambdaQueryWrapper.orderByAsc(Customer::getId);
        Page<Customer> page = Page.of(pageNum, pageSize, false);
        customerService.page(page, customerLambdaQueryWrapper);
        return page;
    }

    /**
     * 根据客户表主键ID列表设置客户为无效客户
     * @param customerIdList   客户列表
     * <AUTHOR>
     * @date 2023/6/26 11:17
     **/
    @Override
    public void batchSetInvalidCustomer(List<Long> customerIdList) {
        if (CollectionUtil.isEmpty(customerIdList)) {
            return;
        }
        int maxSize = 10000;
        List<List<Long>> customerBatchList = new ArrayList<>();
        List<Long> currentList = new ArrayList<>();
        for (Long id : customerIdList) {
            if (currentList.size() == maxSize) {
                customerBatchList.add(currentList);
                currentList = new ArrayList<>();
            }
            currentList.add(id);
        }
        if (!currentList.isEmpty()) {
            customerBatchList.add(currentList);
        }
        customerBatchList.forEach(customerBatch -> {
            customerService.lambdaUpdate()
                    .set(Customer::getInvalidFlag, 1)
                    .setSql("update_time = update_time")
                    .in(Customer::getId, customerBatch)
                    .update();
        });
    }

    /**
     * 获取客户有效标记
     *
     * @param customerIdList 客户ID
     * <AUTHOR>
     * @date 2023/7/12 11:00
     * @return java.util.Map<java.lang.String, java.lang.Boolean>
     **/
    @Override
    public Map<String, Boolean> getCustomerValidFlag(List<String> customerIdList) {
        return customerService.lambdaQuery()
                .in(Customer::getCustomerId, customerIdList)
                .select(Customer::getCustomerId, Customer::getInvalidFlag)
                .list().stream()
                .collect(Collectors.toMap(Customer::getCustomerId, customer -> customer.getInvalidFlag() == 0, (x, y) -> x));
    }

    /**
     * 获取处理中的客户列表
     *
     * @param customerTableIdList   客户表ID列表
     * <AUTHOR>
     * @date 2023/8/24 16:03
     * @return java.util.List<com.ce.scrm.customer.dao.entity.Customer>
     **/
    @Override
    public List<Customer> getCustomerDealFlag(List<Long> customerTableIdList) {
        return customerService.lambdaQuery()
                .in(Customer::getId, customerTableIdList)
                .select(Customer::getId, Customer::getCustomerId, Customer::getInvalidFlag)
                .list().stream()
                .filter(customer -> customer.getInvalidFlag() != 0)
                .collect(Collectors.toList());
    }

    /**
     * 设置有效客户标记
     *
     * @param customerId 客户ID
     * <AUTHOR>
     * @date 2023/7/11 15:43
     * @return boolean
     **/
    @Override
    public boolean setValidCustomerFlag(String customerId) {
        return customerService.lambdaUpdate()
                .set(Customer::getInvalidFlag, 0)
                .setSql("update_time = update_time")
                .eq(Customer::getCustomerId, customerId)
                .eq(Customer::getInvalidFlag, 1)
                .update();
    }

    /**
     * 获取无效客户表ID数据
     *
     * @param size 获取数量
     * <AUTHOR>
     * @date 2023/6/25 17:06
     * @return java.util.List<java.lang.Long>
     **/
    @Override
    public List<Long> setInvalidCustomerDealFlag(long size) {
        Lock lock = baseManager.getLock();
        try {
            lock.lock(INVALID_CUSTOMER_TRANSFER_LOCK_NAME);
            LambdaQueryWrapper<Customer> customerLambdaQueryWrapper = Wrappers.lambdaQuery();
            customerLambdaQueryWrapper.eq(Customer::getInvalidFlag, 1);
            customerLambdaQueryWrapper.select(Customer::getId);
            customerLambdaQueryWrapper.orderByAsc(Customer::getId);
            Page<Customer> page = Page.of(1, size, false);
            customerService.page(page, customerLambdaQueryWrapper);
            return page.getRecords().stream().map(Customer::getId).collect(Collectors.toList());
        } finally {
            lock.unLock(INVALID_CUSTOMER_TRANSFER_LOCK_NAME);
        }
    }

    /**
     * 迁移无效客户数据
     *
     * @param customerTableId 客户表ID
     * <AUTHOR>
     * @date 2023/8/24 11:02
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferInvalidCustomerData(long customerTableId) {
        //迁移无效客户数据
        String customerId = transferInvalidCustomer(customerTableId);
        if (StrUtil.isBlank(customerId)) {
            return;
        }
        //迁移无效客户联系人数据
        List<String> contactPersonIdList = transferInvalidCustomerContact(customerId);
        if (CollectionUtil.isEmpty(contactPersonIdList)) {
            return;
        }
        //迁移无效联系人数据
        transferInvalidContactPerson(contactPersonIdList);
        //迁移无效联系方式数据
        transferInvalidContactInfo(contactPersonIdList);
    }

    /**
     * 迁移无效客户
     * @param customerTableId   客户表ID
     * <AUTHOR>
     * @date 2023/8/24 11:30
     * @return java.lang.String
     **/
    private String transferInvalidCustomer(long customerTableId) {
        Customer customer = customerService.lambdaQuery().eq(Customer::getId, customerTableId).one();
        if (customer == null) {
            return null;
        }
        customer.setInvalidFlag(1);
        CustomerBack customerBack = BeanUtil.copyProperties(customer, CustomerBack.class);
        customerBackService.save(customerBack);
        customerService.removeById(customerTableId);
        customerCacheHandler.del(customer.getCustomerId());
        return customer.getCustomerId();
    }

    /**
     * 迁移无效客户下的客户联系人
     * @param customerId    客户ID
     * <AUTHOR>
     * @date 2023/8/24 11:36
     * @return java.util.List<java.lang.String>
     **/
    private List<String> transferInvalidCustomerContact(String customerId) {
        List<CustomerContact> customerContactList = customerContactService.lambdaQuery().eq(CustomerContact::getCustomerId, customerId).list();
        //当前客户没有联系人或者已经迁移过（重复客户ID的情况）则数据无需迁移
        if (CollectionUtil.isEmpty(customerContactList)) {
            return null;
        }
        List<CustomerContactBack> customerContactBackList = BeanUtil.copyToList(customerContactList, CustomerContactBack.class);
        customerContactBackService.saveBatch(customerContactBackList);
        customerContactService.removeBatchByIds(customerContactList.stream().map(CustomerContact::getId).collect(Collectors.toList()));
        customerContactCacheHandler.del(customerId);
        return customerContactList.stream().map(CustomerContact::getContactPersonId).collect(Collectors.toList());
    }

    /**
     * 迁移无效客户下的联系人
     * @param contactPersonIdList   联系人ID列表
     * <AUTHOR>
     * @date 2023/8/24 11:37
     **/
    private void transferInvalidContactPerson(List<String> contactPersonIdList) {
        List<ContactPerson> contactPersonList = contactPersonService.lambdaQuery().in(ContactPerson::getContactPersonId, contactPersonIdList).list();
        if (CollectionUtil.isEmpty(contactPersonList)) {
            return;
        }
        List<ContactPersonBack> contactPersonBackList = BeanUtil.copyToList(contactPersonList, ContactPersonBack.class);
        contactPersonBackService.saveBatch(contactPersonBackList);
        contactPersonService.removeBatchByIds(contactPersonList.stream().map(ContactPerson::getId).collect(Collectors.toList()));
        contactPersonList.forEach(contactPerson -> contactPersonCacheHandler.del(contactPerson.getContactPersonId()));
    }

    /**
     * 迁移无效客户下的联系方式
     * @param contactPersonIdList   联系人ID列表
     * <AUTHOR>
     * @date 2023/8/24 11:37
     **/
    private void transferInvalidContactInfo(List<String> contactPersonIdList) {
        List<ContactInfo> contactInfoList = contactInfoService.lambdaQuery().in(ContactInfo::getContactPersonId, contactPersonIdList).list();
        if (CollectionUtil.isEmpty(contactInfoList)) {
            return;
        }
        List<ContactInfoBack> contactInfoBackList = BeanUtil.copyToList(contactInfoList, ContactInfoBack.class);
        contactInfoBackService.saveBatch(contactInfoBackList);
        contactInfoService.removeBatchByIds(contactInfoList.stream().map(ContactInfo::getId).collect(Collectors.toList()));
    }
}