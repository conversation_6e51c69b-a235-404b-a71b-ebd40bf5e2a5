package com.ce.scrm.customer.async.mq.consumer;

import cn.ce.cesupport.base.utils.PositionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew;
import com.ce.scrm.customer.async.mq.consumer.binlog.AbstractBinlogConsumer;
import com.ce.scrm.customer.async.mq.consumer.binlog.TableChangeEnum;
import com.ce.scrm.customer.async.mq.entity.CmCustProtect;
import com.ce.scrm.customer.async.mq.entity.CmCustVisitLog;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.cache.handler.CustomerCacheHandler;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.service.third.invoke.CmCustVisitLogThirdService;
import com.ce.scrm.customer.service.third.invoke.ProtectThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 监听 cm_cust_visit_log 跟进记录表binlog，修改客户表数据
 * @Author: 李金澎
 * @Date: 2020/12/23
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CM_CUST_VISIT_LOG_TOPIC
        , consumerGroup = ServiceConstant.MqConstant.Group.VISIT_LOG_TO_CUSTOMER_GROUP
        , consumeThreadMax = 3)
public class VisitLogToCustomerConsumer extends AbstractBinlogConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CustomerCacheHandler customerCacheHandler;

    @Resource
    private CustomerService customerService;

    @Resource
    private ProtectThirdService protectThirdService;

    @Resource
    private CmCustVisitLogThirdService cmCustVisitLogThirdService;

    @Override
    public void onMessage(MessageExt messageExt) {
        super.dealBinlogMqMessage(messageExt);
    }


    @Override
    public void dealEvent(String msgId, JSONObject beforeJson, JSONObject afterJson, String type) {
        if (Objects.equals(type, TableChangeEnum.insert.getField())) {
            insertStart(afterJson);
        } else if (Objects.equals(type, TableChangeEnum.update.getField())) {
        } else if (Objects.equals(type, TableChangeEnum.delete.getField())) {

        }
    }

    private void insertStart(JSONObject afterJson) {

        CmCustVisitLog cmCustVisitLog = JSONObject.parseObject(afterJson.toJSONString(), CmCustVisitLog.class);
        String customerId = cmCustVisitLog.getCustId();
        if (StringUtils.isBlank(customerId)) {
            log.info("客户id不能为空");
            return;
        }

        CustomerProtectDubboVew protect = protectThirdService.getProtectByCustomerId(customerId);


        LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerId);

        String recordWay = cmCustVisitLog.getRecordWay();
        Integer followCount = cmCustVisitLogThirdService.getCountByCustomerAndRecordWay(customerId, recordWay);
        if (Objects.equals(recordWay,"DICT_VISIT_RECORDWAY_01")) {
            // 手动录入跟进记录
            updateChainWrapper.set(Customer::getLastVisitType, cmCustVisitLog.getVisitType());
            updateChainWrapper.set(Customer::getLastFollowTime, cmCustVisitLog.getCreateTime());

            updateChainWrapper.set(Customer::getFollowCount, followCount);

            if (protect != null && protect.getStatus() == 1 && PositionUtil.isSdrArea(protect.getAreaId())) {
                updateChainWrapper.set(Customer::getLastSdrFollowTime, cmCustVisitLog.getCreateTime());
                updateChainWrapper.setSql("sdr_follow_count = COALESCE(sdr_follow_count, 0) +1");
            }
        }else if (Objects.equals(recordWay,"DICT_VISIT_RECORDWAY_02")) {
            // 地理打卡
            updateChainWrapper.set(Customer::getLastSiteTime, cmCustVisitLog.getCreateTime());
            updateChainWrapper.set(Customer::getSiteCount, followCount);

        } else {
            return;
        }


        updateChainWrapper.update();
        customerCacheHandler.del(customerId);

    }

}