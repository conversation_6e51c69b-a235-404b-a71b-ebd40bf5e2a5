package com.ce.scrm.customer.async.job.handler;

import com.ce.scrm.customer.service.business.IContactPersonBusiness;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 联系人任务处理器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/8/30
 **/
@Slf4j
@Component
public class ContactPersonJobHandler {

    @Resource
    IContactPersonBusiness contactPersonBusiness;

    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.REFRESH_LEGAL_PERSON_FLAG_JOB_HANDLER)
    public ReturnT<String> refreshLegalPersonFlagJobHandler(String param) {
        try {
            log.info("===========定时更新是否法人字段开始==========");
            contactPersonBusiness.refreshLegalPersonFlag();
            log.info("===========定时更新是否法人字段结束==========");
            return ReturnT.SUCCESS;
        }catch (Exception e){
            log.error("定时更新是否法人字段失败", e);
            return ReturnT.FAIL;
        }
    }
}
