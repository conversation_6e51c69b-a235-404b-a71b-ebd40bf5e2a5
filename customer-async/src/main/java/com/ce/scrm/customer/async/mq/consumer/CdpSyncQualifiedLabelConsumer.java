package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.cache.handler.CustomerCacheHandler;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/***
 * cdp同步客户转介绍合格系列标签同步到CRM
 * <AUTHOR>
 * @date 2024/7/16 19:10
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_SYNC_CUST_QUALIFIED_LABEL_TOPIC
        , consumerGroup = ServiceConstant.MqConstant.Group.CDP_SYNC_CUST_QUALIFIED_LABEL_GROUP, consumeThreadMax = 5)
public class CdpSyncQualifiedLabelConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CustomerService customerService;

    @Resource
    private CustomerCacheHandler customerCacheHandler;

    @Override
    public void onMessage(MessageExt messageExt) {
        String param = new String(messageExt.getBody());
        if (StrUtil.isBlank(param)) {
            log.error("cdp同步客户转介绍合格系列标签同步到CRM 参数为空，消息ID为:{}", messageExt.getMsgId());
            return;
        }
        try {
            JSONObject paramJson = JSONObject.parseObject(param);
            String customerId = paramJson.getString("customerId");
            if (null == paramJson || paramJson.isEmpty() || StringUtils.isEmpty(customerId)) {
                return;
            }

            LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerId);
            Integer qualifiedNewCust = paramJson.getInteger("qualified_new_cust");
            Integer qualifiedOldCust = paramJson.getInteger("qualified_old_cust");
            Integer invitedNewCust = paramJson.getInteger("invited_new_cust");
            Date qualifiedNewCustTime = paramJson.getDate("qualified_new_cust_time");
            updateChainWrapper.set(Customer::getTagInvitedNewCust, invitedNewCust);
            updateChainWrapper.set(Customer::getTagQualifiedNewCust, qualifiedNewCust);
            updateChainWrapper.set(Customer::getTagQualifiedOldCust, qualifiedOldCust);
            updateChainWrapper.set(Customer::getTagQualifiedNewCustTime, qualifiedNewCustTime);

            boolean result = updateChainWrapper.update();
            if (result) {
                customerCacheHandler.del(customerId);
            }
        } catch (Exception e) {
            log.error("cdp同步客户转介绍合格系列标签同步到CRM,messageId={}", messageExt.getMsgId(), e);
        }
    }

}