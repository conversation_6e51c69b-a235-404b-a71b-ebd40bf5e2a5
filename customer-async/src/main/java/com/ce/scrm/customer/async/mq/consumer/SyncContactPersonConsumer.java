package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.async.mq.entity.ImportContactPersonDto;
import com.ce.scrm.customer.async.mq.entity.SyncRocketContactPersonDto;
import com.ce.scrm.customer.dao.entity.ContactInfo;
import com.ce.scrm.customer.dao.entity.ContactPerson;
import com.ce.scrm.customer.dao.entity.CustomerContact;
import com.ce.scrm.customer.dao.service.ContactInfoService;
import com.ce.scrm.customer.dao.service.ContactPersonService;
import com.ce.scrm.customer.dao.service.CustomerContactService;
import com.ce.scrm.customer.service.business.IAreaBusiness;
import com.ce.scrm.customer.service.business.entity.view.AreaBusinessView;
import com.ce.scrm.customer.service.config.UniqueIdService;
import com.ce.scrm.customer.service.enums.ContactInfoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 同步联系人mq（洗数据使用）
 * <AUTHOR>
 * @date 2023/5/25 17:07
 * @version 1.0.0
 **/
@Slf4j
@Component
@RocketMQMessageListener(topic = "IMPORT_CONTACT_PERSON_TOPIC", consumerGroup = "IMPORT_CONTACT_PERSON_GROUP", consumeMode = ConsumeMode.CONCURRENTLY)
public class SyncContactPersonConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private ContactInfoService contactInfoService;

    @Resource
    private ContactPersonService contactPersonService;
    @Resource
    private CustomerContactService customerContactService;
    @Resource
    private IAreaBusiness areaBusiness;
    @Resource
    private UniqueIdService uniqueIdService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onMessage(MessageExt messageExt) {
        String data = new String(messageExt.getBody());

        log.info("接收到mq同步联系人消息:{}", data);
        SyncRocketContactPersonDto syncRocketContactPersonDto;
        if (StrUtil.isBlank(data) || (syncRocketContactPersonDto = JSON.parseObject(data, SyncRocketContactPersonDto.class)) == null) {
            log.error("收到同步联系人消息，必要数据为空，参数为:{}", data);
            return;
        }

        if (StrUtil.isEmpty(syncRocketContactPersonDto.getType())) {
            log.error("收到同步联系人消息，type不能为空");
            return;
        }

        if (CollectionUtil.isEmpty(syncRocketContactPersonDto.getData())) {
            log.error("收到同步联系人消息，data不能为空");
            return;
        }

        List<ImportContactPersonDto> contactPersonOperDtoList = syncRocketContactPersonDto.getData();
        if (!"INSERT".equals(syncRocketContactPersonDto.getType())) {
            log.warn("仅处理新增操作，当前操作类型为:{}", syncRocketContactPersonDto.getType());
            return;
        }
        for (ImportContactPersonDto importContactPersonDto : contactPersonOperDtoList) {
            //拼装联系人
            ContactPerson contactPerson = packageContactPersonData(importContactPersonDto);
            //拼装联系方式
            List<ContactInfo> contactInfoList = packageContactInfoData(importContactPersonDto);
            for (ContactInfo contactInfo : contactInfoList) {
                contactInfo.setContactInfoId(uniqueIdService.getId());
                contactInfo.setContactPersonId(contactPerson.getContactPersonId());
                contactInfo.setSourceKey(contactPerson.getSourceKey());
                contactInfo.setCreator(contactPerson.getCreator());
                contactInfo.setCreateTime(contactPerson.getCreateTime());
                contactInfo.setOperator(contactPerson.getOperator());
                contactInfo.setUpdateTime(contactPerson.getUpdateTime());
            }
            //拼装客户联系人关系
            CustomerContact customerContact = new CustomerContact();
            customerContact.setCustomerId(importContactPersonDto.getCustomerId());
//            customerContact.setCustomerId("wangshoufang");
            customerContact.setContactPersonId(contactPerson.getContactPersonId());
            customerContact.setCreator(contactPerson.getCreator());
            customerContact.setCreateTime(contactPerson.getCreateTime());
            customerContact.setOperator(contactPerson.getOperator());
            customerContact.setUpdateTime(contactPerson.getUpdateTime());
            contactPersonService.save(contactPerson);
            contactInfoService.saveBatch(contactInfoList);
            customerContactService.save(customerContact);
            log.info("联系人ID为:{}", contactPerson.getContactPersonId());
        }
    }

    private List<ContactInfo> packageContactInfoData(ImportContactPersonDto importContactPersonDto) {
        //拼装联系方式数据
        List<ContactInfo> contactInfoList = new ArrayList<>();
        packageInfo(contactInfoList, ContactInfoEnum.mobile, importContactPersonDto.getPhone());
        if (StrUtil.isNotBlank(importContactPersonDto.getWechat())) {
            packageInfo(contactInfoList, ContactInfoEnum.wechat, importContactPersonDto.getWechat());
        }
        if (StrUtil.isNotBlank(importContactPersonDto.getEmail())) {
            packageInfo(contactInfoList, ContactInfoEnum.email, importContactPersonDto.getEmail());
        }
        if (StrUtil.isNotBlank(importContactPersonDto.getTelephone())) {
            packageInfo(contactInfoList, ContactInfoEnum.telephone, importContactPersonDto.getTelephone());
        }
        if (StrUtil.isNotBlank(importContactPersonDto.getQq())) {
            packageInfo(contactInfoList, ContactInfoEnum.qq, importContactPersonDto.getQq());
        }
        if (StrUtil.isNotBlank(importContactPersonDto.getWecome())) {
            packageInfo(contactInfoList, ContactInfoEnum.wecome, importContactPersonDto.getWecome());
        }
        return contactInfoList;
    }

    private void packageInfo(List<ContactInfo> contactInfoList, ContactInfoEnum contactInfoEnum, String contactWay) {
        contactInfoList.addAll(Arrays.stream(contactWay.split(";")).map(way -> {
            ContactInfo contactInfo = new ContactInfo();
            contactInfo.setContactType(contactInfoEnum.getType());
            contactInfo.setContactWay(way);
            contactInfo.setPhoneFlag(1);
            return contactInfo;
        }).collect(Collectors.toList()));
    }

    private ContactPerson packageContactPersonData(ImportContactPersonDto importContactPersonDto) {
        //拼装联系人数据
        ContactPerson contactPerson = BeanUtil.copyProperties(importContactPersonDto, ContactPerson.class);
        //生成联系人ID
        String contactPersonId = uniqueIdService.getId();
        contactPerson.setContactPersonId(contactPersonId);
        //省
        String provinceCode;
        AreaBusinessView provinceData;
        if (StrUtil.isNotBlank(provinceCode = importContactPersonDto.getProvinceCode()) && (provinceData = areaBusiness.get(provinceCode)) != null) {
            contactPerson.setProvinceName(provinceData.getName());
        }
        //市
        String cityCode;
        AreaBusinessView cityData;
        if (StrUtil.isNotBlank(cityCode = importContactPersonDto.getCityCode()) && (cityData = areaBusiness.get(cityCode)) != null) {
            contactPerson.setCityName(cityData.getName());
        }
        //区
        String districtCode;
        AreaBusinessView districtData;
        if (StrUtil.isNotBlank(districtCode = importContactPersonDto.getDistrictCode()) && (districtData = areaBusiness.get(districtCode)) != null) {
            contactPerson.setDistrictName(districtData.getName());
        }
        return contactPerson;
    }
}
