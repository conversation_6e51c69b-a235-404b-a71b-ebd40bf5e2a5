package com.ce.scrm.customer;

import com.alibaba.nacos.spring.context.annotation.config.EnableNacosConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.PreDestroy;


/**
 * async启动类
 * <AUTHOR>
 * @date 2023/4/7 20:48
 * @version 1.0.0
 **/
@SpringBootApplication
@EnableTransactionManagement
@EnableNacosConfig
@MapperScan("com.ce.scrm.customer.dao.mapper")
public class ScrmCustomerAsyncApplication {

    private final static Logger LOGGER = LoggerFactory.getLogger(ScrmCustomerAsyncApplication.class);

    /**
     * 项目启动方法
     * @param args  启动参数
     * <AUTHOR>
     * @date 2023/4/7 20:48
     **/
    public static void main(String[] args) {
        SpringApplication.run(ScrmCustomerAsyncApplication.class, args);
        LOGGER.error("ScrmCustomerAsyncApplication is started（项目启动成功了）");
    }

    @PreDestroy
    public void destroy(){
        LOGGER.error("ScrmCustomerAsyncApplication is started（项目即将重启了）");
    }
}
