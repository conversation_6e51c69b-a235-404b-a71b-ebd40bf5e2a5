package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.ce.scrm.customer.async.mq.entity.CdpBaoJiaKeHuInfo;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.cache.handler.CustomerCacheHandler;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/***
* cdp 同步报价客户到客户库
* <AUTHOR>
* @date 2024/7/16 19:10
*/
@Deprecated
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_SYNC_BJKH_SCRM_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.CDP_SYNC_BJKH_SCRM_GROUP,consumeThreadMax = 5)
public class SyncCdpBaoJiaKeHuToCustomerConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CustomerService customerService;

    @Resource
    private CustomerCacheHandler customerCacheHandler;

    @Override
    public void onMessage(MessageExt messageExt) {
        String param = new String(messageExt.getBody());
        if (StrUtil.isBlank(param)) {
            log.error("cdp 同步报价客户到客户库 参数为空，消息ID为:{}", messageExt.getMsgId());
            return;
        }
        try {
            CdpBaoJiaKeHuInfo baoJiaKeHuInfo = JSONObject.parseObject(param, CdpBaoJiaKeHuInfo.class);
            if (baoJiaKeHuInfo == null || CollectionUtil.isEmpty(baoJiaKeHuInfo.getCustomerIds())){
                return;
            }
            // step1.库里面是报价客户全部更新成非报价客户
            LambdaUpdateChainWrapper<Customer> cleanChainWrapper = customerService.lambdaUpdate().eq(Customer::getTagQuoteCust, 1);
            cleanChainWrapper.set(Customer::getTagQuoteCust, 0);
            cleanChainWrapper.update();

            // step2.更新新一批报价客户,分批更新，50个一组
            List<List<String>> partitions = Lists.partition(baoJiaKeHuInfo.getCustomerIds(), 50);
            for (List<String> ids:partitions){
                LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().in(Customer::getCustomerId, ids);
                updateChainWrapper.set(Customer::getUpdateTime, LocalDateTime.now());
                updateChainWrapper.set(Customer::getTagQuoteCust, 1);
                updateChainWrapper.update();
            }
            for(String customerId:baoJiaKeHuInfo.getCustomerIds()){
                customerCacheHandler.del(customerId);
            }
        }catch (Exception e){
            log.error("cdp同步报价客户到客户库失败,messageId={}",messageExt.getMsgId(),e);
        }
    }
}