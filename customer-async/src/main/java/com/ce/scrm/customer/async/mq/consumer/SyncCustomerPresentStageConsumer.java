package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.async.mq.entity.SyncCustomerPresentStageDto;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.business.ICustomerBusiness;
import com.ce.scrm.customer.service.business.entity.dto.CustomerAddBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.CustomerBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.CustomerUpdateBusinessDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 同步客户阶段消费
 * <AUTHOR>
 * @date 2023/5/25 17:07
 * @version 1.0.0
 **/
@Slf4j
@Component
@RocketMQMessageListener(topic = "CESUPPORT_SCRM_PRESENT_STAGE_TOPIC", consumerGroup = "CESUPPORT_SCRM_PRESENT_STAGE_GROUP", consumeMode = ConsumeMode.CONCURRENTLY)
public class SyncCustomerPresentStageConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private ICustomerBusiness customerBusiness;

    @Resource
    private CustomerService customerService;

    @Override
    public void onMessage(MessageExt messageExt) {
        String data = new String(messageExt.getBody());
        SyncCustomerPresentStageDto syncCustomerPresentStageDto;
        try {
            if (StrUtil.isBlank(data) || (syncCustomerPresentStageDto = JSON.parseObject(data, SyncCustomerPresentStageDto.class)) == null) {
                log.error("收到同步联系人消息，必要数据为空，参数为:{}", data);
                return;
            }

            if (StrUtil.isEmpty(syncCustomerPresentStageDto.getCustomerId())) {
                log.error("收到同步客户阶段消息，customerId不能为空");
                return;
            }

            if (syncCustomerPresentStageDto.getPresentStage() == null) {
                log.error("收到同步客户阶段消息，presentStage不能为空");
                return;
            }
            CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
            customerBusinessDto.setCustomerId(syncCustomerPresentStageDto.getCustomerId());
            Customer customer = customerService.lambdaQuery().eq(Customer::getCustomerId, syncCustomerPresentStageDto.getCustomerId()).last("limit 1").one();
            if (customer == null) {
                /*
                CustomerAddBusinessDto customerAddBusinessDto = BeanUtil.copyProperties(syncCustomerPresentStageDto, CustomerAddBusinessDto.class);
                customerBusiness.add(customerAddBusinessDto);
                */
            } else {
                // 客户阶段不相等的时候才需要更新
                if (!syncCustomerPresentStageDto.getPresentStage().equals(customer.getPresentStage())) {
                    CustomerUpdateBusinessDto customerUpdateBusinessDto = new CustomerUpdateBusinessDto();
                    customerUpdateBusinessDto.setCustomerId(syncCustomerPresentStageDto.getCustomerId());
                    customerUpdateBusinessDto.setPresentStage(syncCustomerPresentStageDto.getPresentStage());
                    customerBusiness.update(customerUpdateBusinessDto);
                }
            }
        } catch (Exception e) {
            log.error("同步客户阶段消费失败，异常信息为:", e);
            throw new RuntimeException();
        }
    }
}
