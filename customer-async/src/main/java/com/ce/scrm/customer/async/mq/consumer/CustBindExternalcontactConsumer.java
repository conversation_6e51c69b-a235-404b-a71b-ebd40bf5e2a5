package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.customer.async.mq.consumer.binlog.AbstractBinlogConsumer;
import com.ce.scrm.customer.async.mq.consumer.binlog.TableChangeEnum;
import com.ce.scrm.customer.async.mq.entity.TCustBindExternalcontact;
import com.ce.scrm.customer.dao.entity.CustomerBindWx;
import com.ce.scrm.customer.dao.service.CustomerBindWxService;
import com.ce.scrm.customer.service.business.IContactPersonBusiness;
import com.ce.scrm.customer.service.business.ICustomerContactBusiness;
import com.ce.scrm.customer.service.business.entity.dto.ContactPersonAddBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.CustomerContactBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView;
import com.ce.scrm.customer.service.business.entity.view.CustomerContactBusinessView;
import com.ce.scrm.customer.service.cache.entity.CustomerCacheData;
import com.ce.scrm.customer.service.cache.handler.CustomerCacheHandler;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/***
 * 监听业务平台 t_cust_bind_externalcontact 表绑定时创建联系人id,然后保存到customer_bind_wx中
 * <AUTHOR>
 * @date 2024/7/16 19:10
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CUST_BIND_EXTERNALCONTACT_TOPIC
        , consumerGroup = ServiceConstant.MqConstant.Group.CUST_BIND_EXTERNALCONTACT_GROUP
        , consumeThreadMax = 3)
public class CustBindExternalcontactConsumer extends AbstractBinlogConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private CustomerCacheHandler customerCacheHandler;

    @Resource
    private IContactPersonBusiness contactPersonBusiness;

    @Resource
    CustomerBindWxService customerBindWxService;

    @Resource
    ICustomerContactBusiness customerContactBusiness;

    @Override
    public void onMessage(MessageExt messageExt) {
        String data = new String(messageExt.getBody());
        String msgId = messageExt.getMsgId();
        if (StrUtil.isBlank(data)) {
            log.error("绑定后创建联系人，参数为空，消息ID为:{}", msgId);
            return;
        }
        super.dealBinlogMqMessage(messageExt);
    }


    @Override
    public void dealEvent(String msgId, JSONObject beforeJson, JSONObject afterJson, String type) {
        TCustBindExternalcontact custBindExternalcontact = null;

        if (Objects.equals(type, TableChangeEnum.insert.getField())) {
            custBindExternalcontact = JSONObject.parseObject(afterJson.toJSONString(), TCustBindExternalcontact.class);
        } else if (Objects.equals(type, TableChangeEnum.update.getField())) {
            if (beforeJson.containsKey("state")){
                // 状态变更
                custBindExternalcontact = JSONObject.parseObject(afterJson.toJSONString(), TCustBindExternalcontact.class);
            }else {
                return;
            }

        } else {
            log.error("处理不了DELETE ... msgId={}", msgId);
            return;
        }
        if (Objects.isNull(custBindExternalcontact)) {
            return;
        }

        try {
            String custId = custBindExternalcontact.getCustId();

            CustomerCacheData customerCacheData = null;

            /**
             * 1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户
             */
            Integer custBindExternalcontactType = custBindExternalcontact.getType();

            if (!Objects.equals(custBindExternalcontactType, 1) && !Objects.equals(custBindExternalcontactType, 2)) {
                log.error("未知绑定类型 msgId={}", msgId);
                return;
            }
            /**
             * 0 首次绑定  1 换绑
             */
            String contactPersonId = null;
            Integer state = custBindExternalcontact.getState();
            if (Objects.equals(state, 0)) {
                if (StringUtils.isNotBlank(custId)) {
                    // 有客户id 则crm添加联系人记录
                    customerCacheData = customerCacheHandler.get(custId);
                    if (Objects.isNull(customerCacheData)) {
                        log.error("客户id={} 在crm中找不到 msgId={} data={}", custId, msgId, JSONObject.toJSONString(custBindExternalcontact));
                    }
                    Optional<String> optional = contactPersonAdd(custBindExternalcontact, msgId);
                    if (optional.isPresent()) {
                        contactPersonId = optional.get();
                        log.info("获取到了联系人id = {}",contactPersonId);
                    }
                }
                if (!saveCustomerBindWxLog(contactPersonId, custBindExternalcontact, customerCacheData)) {
                    log.error("保存绑定记录失败, msgId={} contactPersonId={}", msgId, contactPersonId);
                }
            } else if (Objects.equals(state, 1)) {
                // 查出对应的 联系人id
                Long id = custBindExternalcontact.getId();
                CustomerBindWx customerBindWx = customerBindWxService.getOne(new LambdaQueryWrapper<CustomerBindWx>().eq(CustomerBindWx::getBindSourceTableId, id));
                if (Objects.isNull(customerBindWx)) {
                    log.warn("客户解绑，未找到之前绑定时的记录 msgId={}", msgId);
                } else {
                    contactPersonId = customerBindWx.getContactPersonId();
                }
                saveCustomerBindWxLog(contactPersonId, custBindExternalcontact, customerCacheData);
            }
        } catch (Exception e) {
            log.error("绑定创建联系人 失败,messageId={}", msgId, e);
        }
    }

    /***
     * 添加联系人
     * @param custBindExternalcontact
     * @param msgId
     * <AUTHOR>
     * @date 2025/7/4 13:54
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
     **/
    public Optional<String> contactPersonAdd(TCustBindExternalcontact custBindExternalcontact, String msgId) {

        ContactPersonAddBusinessDto contactPersonAddBusinessDto = new ContactPersonAddBusinessDto();
        contactPersonAddBusinessDto.setCustomerId(custBindExternalcontact.getCustId());
        contactPersonAddBusinessDto.setContactPersonName(custBindExternalcontact.getWxName());
        contactPersonAddBusinessDto.setSourceKey("scrm");
        contactPersonAddBusinessDto.setGender(0);
        contactPersonAddBusinessDto.setOperator(custBindExternalcontact.getCreateId());
        List<ContactPersonAddBusinessDto.ContactInfoAddBusinessDto> contactInfoList = new ArrayList<>();
        ContactPersonAddBusinessDto.ContactInfoAddBusinessDto contactInfoAddBusinessDto = new ContactPersonAddBusinessDto.ContactInfoAddBusinessDto();
        /**
         * 联系类型：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
         */
        if (Objects.equals(custBindExternalcontact.getType(), 1)) {
            contactInfoAddBusinessDto.setContactType(2);
            contactPersonAddBusinessDto.setBindType(1);
            contactInfoAddBusinessDto.setContactWay(custBindExternalcontact.getUnionid());
            contactPersonAddBusinessDto.setUnionId(custBindExternalcontact.getUnionid());


        } else if (Objects.equals(custBindExternalcontact.getType(), 2)) {
            contactInfoAddBusinessDto.setContactType(6);
            contactPersonAddBusinessDto.setBindType(2);
            contactInfoAddBusinessDto.setContactWay(custBindExternalcontact.getExternalUserid());
            contactPersonAddBusinessDto.setUnionId(custBindExternalcontact.getExternalUserid());
        }
        Integer state = custBindExternalcontact.getState();
        if (Objects.equals(state, 0)) {
            // 将 Date 转换为 LocalDateTime
            LocalDateTime localDateTime = custBindExternalcontact.getCreateTime().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();

            contactPersonAddBusinessDto.setUnionIdBindTime(localDateTime);
        } else {
            return Optional.empty();
        }
        contactInfoList.add(contactInfoAddBusinessDto);
        contactPersonAddBusinessDto.setWechatNickName(custBindExternalcontact.getWxName());
        contactPersonAddBusinessDto.setContactInfoList(contactInfoList);


        // 是否已经存在联系人
        List<ContactPersonBusinessView> contactPersonBusinessViews = contactPersonBusiness.getContactPersonsByUnionId(contactPersonAddBusinessDto.getBindType(), contactPersonAddBusinessDto.getUnionId());
        if (!CollectionUtil.isEmpty(contactPersonBusinessViews)) {
            for (ContactPersonBusinessView contactPersonBusinessView : contactPersonBusinessViews) {
                String contactPersonId = contactPersonBusinessView.getContactPersonId();
                CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
                customerContactBusinessDto.setContactPersonId(contactPersonId);
                CustomerContactBusinessView customerContactBusinessView = customerContactBusiness.customerContactByContactId(customerContactBusinessDto);
                String customerId = customerContactBusinessView.getCustomerId();
                // 同一个客户下 同一个联系方式 则直接返回
                if (Objects.equals(customerId, custBindExternalcontact.getCustId())) {
                    return Optional.of(contactPersonId);
                }
            }
        }

        boolean add = contactPersonBusiness.add(contactPersonAddBusinessDto);
        if (!add) {
            log.error("添加客户联系人失败, msgId={} contactPersonAddBusinessDto={}", msgId, JSONObject.toJSONString(contactPersonAddBusinessDto));
        } else {
            return Optional.ofNullable(contactPersonAddBusinessDto.getContactPersonId());
        }
        return Optional.empty();
    }

    /***
     * 保存绑定客户操作记录
     * @param contactPersonId
     * @param custBindExternalcontact
     * @param customerCacheData
     * <AUTHOR>
     * @date 2025/7/4 10:22
     * @version 1.0.0
     * @return boolean
     **/
    private boolean saveCustomerBindWxLog(String contactPersonId, TCustBindExternalcontact custBindExternalcontact, CustomerCacheData customerCacheData) {
        String operator = null;
        Date createTime = null;
        Integer state = custBindExternalcontact.getState();
        if (Objects.equals(state, 0)) {
            operator = custBindExternalcontact.getCreateId();
            createTime = custBindExternalcontact.getCreateTime();
        } else if (Objects.equals(state, 1)) {
            operator = custBindExternalcontact.getModifyId();
            createTime = custBindExternalcontact.getModifyTime();
        } else {
            return true;
        }

        CustomerBindWx customerBindWx = new CustomerBindWx();
        customerBindWx.setType(custBindExternalcontact.getState());
        customerBindWx.setBindType(1);
        customerBindWx.setBindSourceTableId(custBindExternalcontact.getId());
        customerBindWx.setPid(custBindExternalcontact.getPid());
        customerBindWx.setCustomerId(custBindExternalcontact.getCustId());
        customerBindWx.setContactPersonId(contactPersonId);
        customerBindWx.setBindUserType(custBindExternalcontact.getType());
        customerBindWx.setDeptId(custBindExternalcontact.getBussdeptId());
        customerBindWx.setDeptName(custBindExternalcontact.getBussdeptName());
        customerBindWx.setAreaId(custBindExternalcontact.getAreaId());
        customerBindWx.setAreaName(custBindExternalcontact.getAreaName());
        customerBindWx.setSubId(custBindExternalcontact.getSubcompanyId());
        customerBindWx.setSubName(custBindExternalcontact.getSubcompanyName());
        customerBindWx.setBuId(custBindExternalcontact.getBuId());
        customerBindWx.setBuName(custBindExternalcontact.getBuName());

        if (Objects.nonNull(customerCacheData)) {
            Integer tagRetainCust = customerCacheData.getTagRetainCust();
            if (Objects.equals(tagRetainCust, 1)) {
                customerBindWx.setIsTradeWhenOperate(1);
            } else {
                customerBindWx.setIsTradeWhenOperate(0);
            }
            String protectSalerId = customerCacheData.getProtectSalerId();
            if (Objects.equals(protectSalerId, operator)) {
                customerBindWx.setIsSameSalerWhenOperation(1);
            } else {
                customerBindWx.setIsSameSalerWhenOperation(0);
            }
            if (StrUtil.isNotBlank(protectSalerId)) {
                customerBindWx.setIsProtectedWhenOperate(1);
            } else {
                customerBindWx.setIsProtectedWhenOperate(0);
            }
        } else {
            customerBindWx.setIsTradeWhenOperate(0);
            customerBindWx.setIsSameSalerWhenOperation(0);
            customerBindWx.setIsProtectedWhenOperate(0);
        }

        customerBindWx.setCreateId(operator);
        customerBindWx.setCreateTime(createTime);
        customerBindWx.setUpdateTime(custBindExternalcontact.getModifyTime());
        customerBindWx.setUnionid(custBindExternalcontact.getUnionid());
        customerBindWx.setExternalUserid(custBindExternalcontact.getExternalUserid());
        customerBindWx.setWxName(custBindExternalcontact.getWxName());
        log.info("保存绑定/解绑记录 ={} ", JSONObject.toJSONString(customerCacheData));
        return customerBindWxService.save(customerBindWx);
    }


}