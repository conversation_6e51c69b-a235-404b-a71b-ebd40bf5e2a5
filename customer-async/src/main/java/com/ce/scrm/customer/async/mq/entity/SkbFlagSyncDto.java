package com.ce.scrm.customer.async.mq.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/2/21 10:28
 */
@Data
public class SkbFlagSyncDto implements Serializable {
    /**
     * 客户ID
     */
    private String custId;
    /**
     * 企业PID
     */
    private String id;
    /**
     * 统一信用代码
     */
    private String UNCID;
    /**
     * 企业名称
     */
    private String entName;
    /**
     * 搜客宝 1:规模工业,2:规上服务业,3:规上建筑业,4:规上批发零售业,5:规上住宿餐饮业,6:规上房地产开发与经营业
     */
    private List<String> flag7;
    /**
     * 搜客宝 行业 1:律师,2:学校,3:医院
     */
    private List<String> flag8;

    /**
     * 是否是上市企业 1是0否
     */
    private String flag12;

    /**
     * 注册资本
     */
    private String regCapUnify;

    /**
     * 搜客宝 科技型企业
     */
    private List<String> tagTechcompany;
}
