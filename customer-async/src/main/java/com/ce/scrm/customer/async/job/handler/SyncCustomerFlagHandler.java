package com.ce.scrm.customer.async.job.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.ce.scrm.customer.service.business.ICustomerBusiness;
import com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.service.third.entity.view.CompanyInfoData;
import com.ce.scrm.customer.service.third.invoke.SkbCompanyInfoThirdService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 更新客户标记
 * <AUTHOR>
 * @date 2024/1/18 15:41
 * @version 1.0.0
 */
@Slf4j
@Component
public class SyncCustomerFlagHandler {

    @Resource
    private SkbCompanyInfoThirdService skbCompanyInfoThirdService;

    @Resource
    private ICustomerBusiness customerBusiness;


    /**
     * 更新客户KA标记任务处理器
     * @param param 任务参数
     * <AUTHOR>
     * @date 2024/1/18 15:44
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     **/
    @XxlJob(ServiceConstant.JobConstant.JobHandlerConstant.SYNC_CUSTOMER_KA_FLAG_JOB_HANDLER)
    public ReturnT<String> syncCustomerKaFlagJobHandler(String param) {
        //获取搜客宝中标记了KA的客户数据
        List<CompanyInfoData> kaCompanyList = skbCompanyInfoThirdService.getKaCompanyList();
        //获取客户库中标记了KA的数据
        List<CustomerBusinessView> kaCustomerList = customerBusiness.getKaCustomerList();
        //对比获取需要去除KA标记的客户ID
        List<Long> needCleanKaFlagCustomerList = findNeedCleanKaFlagCustomerList(kaCompanyList, kaCustomerList);
        if (CollectionUtil.isNotEmpty(needCleanKaFlagCustomerList)) {
            customerBusiness.cleanCustomerKaFlag(needCleanKaFlagCustomerList);
        }
        //对比获取需要添加KA标记的客户ID
        List<CustomerBusinessView> needAddKaFlagCustomerList = findNeedAddKaFlagCustomerList(kaCompanyList, kaCustomerList);
        if (CollectionUtil.isNotEmpty(needAddKaFlagCustomerList)) {
            customerBusiness.addCustomerKaFlag(needAddKaFlagCustomerList);
        }
        return new ReturnT<>(ReturnT.SUCCESS_CODE, "更新KA标记处理完成, 清理KA标记的数量为:" + needCleanKaFlagCustomerList.size() + ", 添加KA标记的数量为:" + needAddKaFlagCustomerList.size());
    }

    /**
     * 找出需要清理KA标记的客户
     * @param kaCompanyList     标记了ka的搜客表公司数据
     * @param kaCustomerList    标记了ka的客户数据
     * <AUTHOR>
     * @date 2024/1/22 16:06
     * @return java.util.List<java.lang.Long>
     **/
    private List<Long> findNeedCleanKaFlagCustomerList(List<CompanyInfoData> kaCompanyList, List<CustomerBusinessView> kaCustomerList) {
        if (CollectionUtil.isEmpty(kaCustomerList)) {
            return Lists.newArrayList();
        }
        if (CollectionUtil.isEmpty(kaCompanyList)) {
            return kaCustomerList.stream().map(CustomerBusinessView::getId).collect(Collectors.toList());
        }
        //将搜客宝中标记了KA的公司数据拼装成key分别为名称、信用编码、pid的map
        Map<String, CompanyInfoData> pidMap = kaCompanyList.stream().filter(companyInfoData -> StrUtil.isNotBlank(companyInfoData.getPid())).collect(Collectors.toMap(CompanyInfoData::getPid, Function.identity(), (x, y) -> x));
        Map<String, CompanyInfoData> entNameMap = kaCompanyList.stream().filter(companyInfoData -> StrUtil.isNotBlank(companyInfoData.getEntName())).collect(Collectors.toMap(CompanyInfoData::getEntName, Function.identity(), (x, y) -> x));
        Map<String, CompanyInfoData> uncidMap = kaCompanyList.stream().filter(companyInfoData -> StrUtil.isNotBlank(companyInfoData.getUncid())).collect(Collectors.toMap(CompanyInfoData::getUncid, Function.identity(), (x, y) -> x));
        //客户库中的标记客户在搜客宝中获取不到，代表需要去掉当前客户的KA标记
        return kaCustomerList.stream().filter(customerBusinessView -> {
            boolean pidFlag = StrUtil.isNotBlank(customerBusinessView.getSourceDataId()) && pidMap.containsKey(customerBusinessView.getSourceDataId());
            boolean uncidFlag = StrUtil.isNotBlank(customerBusinessView.getCertificateCode()) && uncidMap.containsKey(customerBusinessView.getCertificateCode());
            boolean entNameFlag = StrUtil.isNotBlank(customerBusinessView.getCustomerName()) && entNameMap.containsKey(customerBusinessView.getCustomerName());
            return !(pidFlag || uncidFlag || entNameFlag);
        }).map(CustomerBusinessView::getId).collect(Collectors.toList());
    }

    /**
     * 找出需要添加KA标记的客户
     * @param kaCompanyList     标记了ka的搜客表公司数据
     * @param kaCustomerList    标记了ka的客户数据
     * <AUTHOR>
     * @date 2024/1/22 16:07
     * @return java.util.List<com.ce.scrm.customer.service.third.entity.view.CompanyInfoData>
     **/
    private List<CustomerBusinessView> findNeedAddKaFlagCustomerList(List<CompanyInfoData> kaCompanyList, List<CustomerBusinessView> kaCustomerList) {
        if (CollectionUtil.isEmpty(kaCompanyList)) {
            return Lists.newArrayList();
        }
        if (CollectionUtil.isEmpty(kaCustomerList)) {
            return kaCompanyList.stream().map(companyInfoData -> {
                CustomerBusinessView customerBusinessView = new CustomerBusinessView();
                customerBusinessView.setSourceDataId(companyInfoData.getPid());
                customerBusinessView.setCertificateCode(companyInfoData.getUncid());
                customerBusinessView.setCustomerName(companyInfoData.getEntName());
                return customerBusinessView;
            }).collect(Collectors.toList());
        }
        //将客户库中标记了KA的客户数据拼装成key分别为名称、信用编码、pid的map
        Map<String, CustomerBusinessView> sourceDataIdMap = kaCustomerList.stream().filter(customerBusinessView -> StrUtil.isNotBlank(customerBusinessView.getSourceDataId())).collect(Collectors.toMap(CustomerBusinessView::getSourceDataId, Function.identity(), (x, y) -> x));
        Map<String, CustomerBusinessView> customerNameMap = kaCustomerList.stream().filter(customerBusinessView -> StrUtil.isNotBlank(customerBusinessView.getCustomerName())).collect(Collectors.toMap(CustomerBusinessView::getCustomerName, Function.identity(), (x, y) -> x));
        Map<String, CustomerBusinessView> certificateCodeMap = kaCustomerList.stream().filter(customerBusinessView -> StrUtil.isNotBlank(customerBusinessView.getCertificateCode())).collect(Collectors.toMap(CustomerBusinessView::getCertificateCode, Function.identity(), (x, y) -> x));
        //搜客宝的标记客户在客户库中获取不到，代表需要添加当前客户的KA标记
        return kaCompanyList.stream().filter(companyInfoData -> {
            boolean sourceDataFlag = StrUtil.isNotBlank(companyInfoData.getPid()) && sourceDataIdMap.containsKey(companyInfoData.getPid());
            boolean certificateCodeFlag = StrUtil.isNotBlank(companyInfoData.getUncid()) && customerNameMap.containsKey(companyInfoData.getUncid());
            boolean customerNameFlag = StrUtil.isNotBlank(companyInfoData.getEntName()) && certificateCodeMap.containsKey(companyInfoData.getEntName());
            return !(sourceDataFlag || certificateCodeFlag || customerNameFlag);
        }).map(companyInfoData -> {
            CustomerBusinessView customerBusinessView = new CustomerBusinessView();
            customerBusinessView.setSourceDataId(companyInfoData.getPid());
            customerBusinessView.setCertificateCode(companyInfoData.getUncid());
            customerBusinessView.setCustomerName(companyInfoData.getEntName());
            return customerBusinessView;
        }).collect(Collectors.toList());
    }
}