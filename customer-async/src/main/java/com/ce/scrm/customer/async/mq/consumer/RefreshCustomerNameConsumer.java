package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.entity.CustomerNameMapping;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.dao.service.ICustomerNameMappingService;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * <p>
 * 刷新客户名称消费
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-25 14:19
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CUSTOMER_SCRM_REFRESH_CUSTOMER_NAME_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.CUSTOMER_SCRM_REFRESH_CUSTOMER_NAME_GROUP)
public class RefreshCustomerNameConsumer implements RocketMQListener<String> {
    @Autowired
    private CustomerService customerService;
    @Autowired
    private ICustomerNameMappingService customerNameMappingService;
    @Value("${third.bigdata.searchByName}")
    private String searchByName;

    @Override
    public void onMessage(String msg) {
        try {
            JSONObject msgJson = JSONObject.parseObject(msg);
            Long id = msgJson.getLong("id");
            String pid = msgJson.getString("pid");
            String customerId = msgJson.getString("customerId");
            String oldCustName = msgJson.getString("custName");
            String oldUncid = msgJson.getString("uncid");
            String url;
            if (StringUtils.isBlank(pid)) {
                pid = "";
            }
            if (StringUtils.isBlank(oldCustName)) {
                oldCustName = "";
            }
            url = searchByName + "?custName=" + oldCustName + "&pid=" + pid;
            HashMap<String, String> headers = new HashMap<>();
            headers.put("uid", "1");

            log.info("getUncidByentName.header:{},custName:{},pid:{}", JSON.toJSONString(headers), oldCustName, pid);
            HttpResponse response = HttpRequest.get(url)
                    .headerMap(headers, true)
                    .execute();
            if (response.isOk()) {
                String responseBody = response.body();
                log.info("查询大数据返回值:{}", responseBody);
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                if (null != jsonObject && jsonObject.getInteger("code") == 200) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    if (null != data) {
                        String newCustName = data.getString("entName");
                        String newUncid = data.getString("uncid");
                        String newPid = data.getString("pid");
//                        if (StringUtils.isNotBlank(oldUncid) && oldUncid.equals(newUncid)) {
//                            log.info("pid:{},custName:{},查到的uncid一样,跳过不更新", pid, oldCustName);
//                        } else {
//                            if (StringUtils.isNotBlank(newUncid)) {
//                                Customer customer = new Customer();
//                                customer.setId(id);
//                                customer.setCertificateCode(newUncid);
//                                customerService.updateById(customer);
//                            }
//                        }
                        CustomerNameMapping customerNameMapping = new CustomerNameMapping();
                        customerNameMapping.setCustomerKey(id);
                        customerNameMapping.setCustomerId(customerId);
                        customerNameMapping.setOldCustName(oldCustName);
                        customerNameMapping.setNewCustName(newCustName);
                        customerNameMapping.setPid(newPid);
                        customerNameMapping.setNewUncid(newUncid);
                        customerNameMappingService.saveOrUpdate(customerNameMapping, new UpdateWrapper<CustomerNameMapping>().lambda().eq(CustomerNameMapping::getCustomerId, customerId));
                    }
                }
            } else {
                log.error("查询大数据接口失败,返回值:{}", JSON.toJSONString(response));
                throw new RuntimeException("查询大数据接口失败");
            }
        } catch (Exception e) {
            log.error("刷新客户名称方法异常,msg:{}", msg, e);
            throw new RuntimeException("刷新客户名称方法异常");
        }
    }
}
