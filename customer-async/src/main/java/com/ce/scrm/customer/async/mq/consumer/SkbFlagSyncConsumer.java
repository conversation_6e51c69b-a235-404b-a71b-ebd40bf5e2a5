package com.ce.scrm.customer.async.mq.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.async.mq.entity.SkbFlagSyncDto;
import com.ce.scrm.customer.service.business.ICustomerBusiness;
import com.ce.scrm.customer.service.business.entity.dto.CustomerTagUpdateBusinessDto;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 搜客宝标签同步
 * <AUTHOR>
 * @date 2023/8/24 10:41
 * @version 1.0.0
 **/
@Slf4j
@Component
@RocketMQMessageListener(topic = ServiceConstant.MqConstant.Topic.CDP_CUS_FLAG_TOPIC, consumerGroup = ServiceConstant.MqConstant.Group.CDP_CUS_FLAG_CUSTOMER_GROUP,consumeThreadMax = 5)
public class SkbFlagSyncConsumer implements RocketMQListener<MessageExt> {
    @Resource
    private ICustomerBusiness customerBusiness;
    @Override
    public void onMessage(MessageExt messageExt) {
        String param = new String(messageExt.getBody());
        if (StrUtil.isBlank(param)) {
            log.error("搜客宝标签同步参数为空，消息ID为:{}", messageExt.getMsgId());
            return;
        }
        try {
            SkbFlagSyncDto skbFlagSyncDto = JSON.parseObject(param, SkbFlagSyncDto.class);
            if (skbFlagSyncDto == null || StringUtils.isEmpty(skbFlagSyncDto.getCustId())){
                return;
            }
            CustomerTagUpdateBusinessDto customerTagUpdateBusinessDto = new CustomerTagUpdateBusinessDto();
            customerTagUpdateBusinessDto.setCustomerId(skbFlagSyncDto.getCustId());
            if (CollectionUtil.isNotEmpty(skbFlagSyncDto.getFlag7())){
                customerTagUpdateBusinessDto.setTagFlag7(skbFlagSyncDto.getFlag7().get(0));
            }
            if (CollectionUtil.isNotEmpty(skbFlagSyncDto.getFlag8())){
                customerTagUpdateBusinessDto.setTagFlag8(skbFlagSyncDto.getFlag8().get(0));
            }
            customerTagUpdateBusinessDto.setTagFlag12(skbFlagSyncDto.getFlag12() != null && skbFlagSyncDto.getFlag12().equals("1")
                    ?"1":"0");
            customerTagUpdateBusinessDto.setRegCapUnify(skbFlagSyncDto.getRegCapUnify());
            if (CollectionUtil.isNotEmpty(skbFlagSyncDto.getTagTechcompany())){
                customerTagUpdateBusinessDto.setTagTechcompany(skbFlagSyncDto.getTagTechcompany().get(0));
            }
            if (StringUtils.isNotBlank(skbFlagSyncDto.getId())){
                customerTagUpdateBusinessDto.setSourceDataId(skbFlagSyncDto.getId());
            }
            customerBusiness.updateCustomerSkbTag(customerTagUpdateBusinessDto);
        }catch (Exception e){
            log.error("搜客宝标签同步失败",e);
        }
    }
}