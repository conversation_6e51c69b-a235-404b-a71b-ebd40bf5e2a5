package com.ce.scrm.customer.async.mq.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商务绑定客户信息表
 *
 * @TableName t_cust_bind_externalcontact
 */
@Data
public class TCustBindExternalcontact implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 线索池 pid
     */
    private String pid;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户
     */
    private Integer type;

    /**
     * 外部联系人id
     */
    private String externalUserid;

    /**
     * 微信unionId
     */
    private String unionid;

    /**
     * 微信名称
     */
    private String wxName;

    /**
     * 0 首次绑定  1 换绑
     */
    private Integer state;

    /**
     * 创建人
     */
    private String createId;

    /**
     * 发表用户名称
     */
    private String creatorName;

    /**
     * 部门ID
     */
    private String bussdeptId;

    /**
     * 部门名称
     */
    private String bussdeptName;

    /**
     * 分公司ID
     */
    private String subcompanyId;

    /**
     * 分公司名称
     */
    private String subcompanyName;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * bu部门id
     */
    private String buId;

    /**
     * bu名称
     */
    private String buName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人Id
     */
    private String modifyId;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 是否开启触达 0 未开启 1 开启
     */
    private Integer isReach;
}