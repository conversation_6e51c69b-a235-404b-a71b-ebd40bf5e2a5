package com.ce.scrm.customer.async.mq.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户实体
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/12/22 13:41
 */
@Data
public class CustomerLabel implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Long id;

    /**
     * 客户id
     */
    private String custId;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 客户类型(1企业客户；2个人客户)
     */
    private Integer custType;

    /**
     * 建档时间
     */
    private Date creDate;

    /**
     * 成立日期
     */
    private Date estimateDate;

    /**
     * 所属一级行业
     */
    private String cneiCode;

    /**
     * 所属二级行业
     */
    private String cneiSecCode;

    /**
     * 注册资金
     */
    private Double regCaps;

    /**
     * 注册资本单位
     */
    private String regCapsUnit;

    /**
     * 注册地址_省
     */
    private String mailingAddrProvince;

    /**
     * 注册地址_市
     */
    private String mailingAddrCity;

    /**
     * 注册地址_区
     */
    private String mailingAddrZone;

    /**
     * 是否含进出口
     */
    private Integer isIo;

    /**
     * 是否有商标
     */
    private Integer isTrademark;

    /**
     * 是否有专利
     */
    private Integer isPatent;

    /**
     * 营业执照是否有变更
     */
    private Integer isChangeLicense;

    /**
     * 是否有百度竞价推广
     */
    private Integer isPromotion;

    /**
     * 是否有公众号
     */
    private Integer isOfficialAccount;

    /**
     * 是否有短信产品
     */
    private Integer isMessage;

    /**
     * 客户是否有订购支付产品
     */
    private Integer isOrderProducts;

    /**
     * 门户后台登录次数
     */
    private Integer portalLoginCou;

    /**
     * 门户后台最近一次登录时间
     */
    private Date portalLastLoginTime;

    /**
     * 速成建站首页登录用户数
     */
    private Integer speedLonginCou;

    /**
     * 全网网站概况登用户数
     */
    private Integer wholeLonginCou;

    /**
     * 外贸-全球站点登用户数
     */
    private Integer foreignLonginCou;

    /**
     * 全球-全球站点登用户数
     */
    private Integer globalLonginCou;

    /**
     * 产品管理点击次数
     */
    private Integer proClickCou;

    /**
     * 资讯管理点击次数
     */
    private Integer inforClickCou;

    /**
     * 资源管理点击次数
     */
    private Integer resClickCou;

    /**
     * 线索处理点击次数
     */
    private Integer clueClickCou;

    /**
     * 操作seo设置点击次数
     */
    private Integer seoClickCou;

    /**
     * 访问统计点击次数
     */
    private Integer visitStaClickCou;

    /**
     * 常规业务受理的数量
     */
    private Integer acceptanceCou;

    /**
     * 客户售后咨询的数量
     */
    private Integer salesInforClickCou;

    /**
     * 客诉的数量
     */
    private Integer complaintCou;

    /**
     * 网站制作类的数量
     */
    private Integer webProCou;

    /**
     * 平台生产异常的数量
     */
    private Integer proAbnormalCou;

    /**
     * 是否提交事件单
     */
    private Integer isEventList;

    /**
     * 客户推广已消耗金额
     */
    private Double consumeAmount;

    /**
     * 客户推广账户余额
     */
    private Double accountBalance;

    /**
     * 客户支付成功金额
     */
    private Double paymentAmount;

    /**
     * 所属产业
     */
    private String thriceIndustrial;

    /**
     * 客户购买次数
     */
    private Integer custOrderCount;

    /**
     * seo后台登录点击次数
     */
    private Integer seoLoginCount;

    /**
     * seo_last_login_time
     */
    private Date seoLastLoginTime;

    /**
     * 域名后台登录点击次数
     */
    private Integer domainLoginCount;

    /**
     * domain_last_login_time
     */
    private Date domainLastLoginTime;

    /**
     * 商城后台登录次数
     */
    private Integer shopLoginCount;

    /**
     * shop_last_login_time
     */
    private Date shopLastLoginTime;

    /**
     * 网站后台关键词设置点击保存按钮的次数
     */
    private Integer webSeokeySetCount;

    /**
     * 是否参加过直播
     */
    private Integer isWatchLive;

    /**
     * 是否有360竞价推广 0 否 1 是
     */
    private Integer is360promotion;

    /**
     * 是否有搜狗竞价推广 0 否 1 是
     */
    private Integer isSgpromotion;

    /**
     * 是否有网店 0 否 1 是
     */
    private Integer isEshop;

    /**
     * 是否有网站 0 否 1 是
     */
    private Integer isWebsite;

    /**
     * 网站服务商
     */
    private String webDeveloper;

    /**
     * 客户数据更新来源
     */
    private String edsUpdateSource;

    /**
     * 网站设置关键词数量
     */
    private Integer webSeoCount;

    /**
     * 网站设置关键词的内容
     */
    private String webSeoKeywordlist;

    /**
     * 网站关键词百度网页端排名
     */
    private String webSeoBdpcpagelist;

    /**
     * 网站关键词百度移动端排名
     */
    private String webSeoBdmbpagelist;

    /**
     * 客户近期未跟进天数
     */
    private Integer custFreeDays;

    /**
     * 订单未支付尾款金额
     */
    private Double custDebt;

    /**
     * 近期未合作的客户月数
     */
    private Integer custRechargeIntervalMonth;

    /**
     * 近期未合作的客户天数
     */
    private Integer custRechargeIntervalDay;

    /**
     * 是否调拨客户
     */
    private Integer isProtectActivity;

    /**
     * 签单客户类型 自签网站客户 type3  source6、
     * 自签非网站客户          type4  source6、
     * 分配网站               type3  source3、
     * 分配非网站             type4  source3
     */
    private String custSigingType;

    /**
     * 客户最近一次到账日期
     */
    private Date custRechargeLastTime;

    /**
     * 客户剩余保护天数
     */
    private Integer daysLeftForProtect;

    /**
     * 最近一次拜访日期
     */
    private Date lastVisitTime;

    /**
     * 客户未打卡拜访的天数
     */
    private Integer numberOfVisitsFromToday;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 末次签单金额
     */
    private Double lastSignedAmount;

    /**
     * 末次签单日期
     */
    private Date lastSignedDate;

    /**
     * 营业状态
     */
    private String entStatus;

    /**
     * 是否门户连带客户 0:否，1:是
     */
    private Integer tagMenhuRelated;

    /**
     * 是否生态转门户 0:否，1:是
     */
    private Integer tagEcoToMenhu;

    /**
     * 是否生态客户 0:否，1:是
     */
    private Integer tagEcoCust;

    /**
     * 是否数字版本门户 0:否，1:是
     */
    private Integer tagMenhuDigital;

    /**
     * 是否2023版本门户 0:否，1:是
     */
    private Integer tagMenhu2023;

    /**
     * 是否低版本门户 0:否，1:是
     */
    private Integer tagMenhuLowver;

    /**
     * 是否门户应升已升客户 0:否，1:是
     */
    private Integer tagMenhuUpgradeableUpgrade;

    /**
     * 是否门户应升级客户 0:否，1:是
     */
    private Integer tagMenhuUpgradeable;

    /**
     * 是否门户应续已续客户 0:否，1:是
     */
    private Integer tagMenhuRenewableRenew;

    /**
     * 是否门户应续客户 0:否，1:是
     */
    private Integer tagMenhuRenewable;

    /**
     * 是否交叉购买客户 0:否，1:是
     */
    private Integer tagCrossBuy;

    /**
     * 是否纯门户客户 0:否，1:是
     */
    private Integer tagPureMenhu;

    /**
     * 搜客宝 1:规模工业，2:规上服务业，3:规上建筑业，4:规上批发零售业，5:规上住宿餐饮业，6:规上房地产开发与经营业
     */
    private String tagFlag7;

    /**
     * 搜客宝 行业 1:律师，2:学校，3:医院
     */
    private String tagFlag8;

    /**
     * 科技型企业
     */
    private String tagTechcompany;

    /**
     * 应续日期
     */
    private Date maxRenewalTime;

    /**
     * 报价客户
     * 0:否
     * 1:是
     */
    private Integer tagbjkh;

    /**
     * 是否保有客户 0:否，1:是
     */
    private Integer tagRetainCust;

    /**
     * 到期时间
     */
    private Date tagRetainTime;

    /**
     * 是否流失客户 0:否，1:是
     */
    private Integer tagLostCust;

    /**
     * 流失时间
     */
    private Date tagLostTime;

    /**
     * 是否转介绍新客 0:否，1:是
     */
    private Integer tagInvitedNewCust;

    /**
     * 是否合格新客 0:否，1:是
     */
    private Integer tagQualifiedNewCust;

    /**
     * 是否合格老客0:否，1:是
     */
    private Integer tagQualifiedOldCust;

    /**
     * 是否低价值客户 0:否 1:是
     */
    private Integer tagLowValue;

    /**
     * 是否外贸客户 0:否 1:是
     */
    private Integer tagWaiMao;

    /**
     * 所属商务id
     */
    private String protectSalerId;

    /**
     * 部门id
     */
    private String protectBussdeptId;

    /**
     * 分公司ID
     */
    private String protectSubcompanyId;

    /**
     * 区域ID
     */
    private String protectAreaId;

    /**
     * 状态值（1、保护；2、总监；3、经理；4、客户池）
     */
    private String protectStatus;

    /**
     * 客户阶段。(2：保护跟进；3：网站客户；4：非网站客户)
     */
    private String protectCustType;

    /**
     * 阶段性保护时间
     */
    private Date protectProtectTime;

    /**
     * 已保护天数(仅2023年以后数据)
     */
    private Integer cdpProtectDay;

    /**
     * 本月打卡次数
     */
    private Integer cdpCurrentMonthClockCount;

    /**
     * 累计打卡次数
     */
    private Integer cdpClockCount;

    /**
     * 客户订单首次支付时间
     */
    private Date cdpFirstPaymentTime;

    /**
     * 新客变为合格新客时间
     */
    private Date qualifiedNewCustTime;

    /**
     * 客户分层
     */
    private Integer customerLayer;

    /**
     * 客户等级
     */
    private Integer customerGrade;

    /**
     * CDP标签user_tag_sfvwcjkh20250530 未成交客户是否是vip判断依据
     */
    private Integer tagWeichengjiaoVip;

    /**
     * 【销管口径】门户客户 0:否,1:是
     */
    private Integer tagMenhu;

    /*
     * 一级国标行业编码
     */
    private String firstIndustryCode;

    /**
     * 一级国标行业名称
     */
    private String firstIndustryName;

    /**
     * 二级国标行业编码
     */
    private String secondIndustryCode;

    /**
     * 二级国标行业名称
     */
    private String secondIndustryName;

    /**
     * 三级国标行业编码
     */
    private String thirdIndustryCode;
    /**
     * 三级国标行业名称
     */
    private String thirdIndustryName;
    /**
     * 四级国标行业编码
     */
    private String fourthIndustryCode;
    /**
     * 四级国标行业名称
     */
    private String fourthIndustryName;

    public CustomerLabel() {}

}
