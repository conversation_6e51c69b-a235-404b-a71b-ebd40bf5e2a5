spring:
  datasource:
    url: ********************************************************************************************************************************************************************************************************
    username: MYSQL(hs930YRVa7tBBmikUYezNw==)
    password: MYSQL(06NOLLrj3J7RK3LoWUJ5nczQkaXzG0MZ)
  redis:
    password: etRedis@019
    cluster:
      nodes: redis.ep:7000,redis.ep:7001,redis.ep:7002

dubbo:
  registry:
    address: ************:2181,************:2181,************:2181

xxl:
  job:
    accessToken:
    admin:
      #调度中心地址
      addresses: http://pre-omo.aiyouyi.cn/xxl-job-admin/
    executor:
      appname: scrm-customer-test
      address:
      ip:
      logpath: logs/xxl-job/jobhandler
      logretentiondays: 3
      oneTimesJob:
        timeout: 10000

rocketmq:
  name-server: mq.ep:9876
  producer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)
  consumer:
    accessKey: MQ(FfrMIKGPMF6RXbijftM0KlhdNgR3ExDn)
    secretKey: MQ(GNbhG41LwH1LYPWK8zklC1bDbGfGrFPCnDTrVsqVjAY=)

nacos:
  config:
    server-addr: common.et:8848
    username: NACOS(oWUVxx3OeK0iuxxdbVU4Hw==)
    password: NACOS(H9klTNl9ZJoALwkWQzmeST4Wx7eEnHUiDC7BdC8YfWU=)
    namespace: pre

logging:
  config: classpath:logback-dev.xml

robot:
  force-alarm-address: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f0e9bc76-e26e-451a-baf5-0b601b80433d

#配置cat发送机器人消息链接地址
sequence:
  zkAddress: common.ep
  zkPort: 2181

third:
  bigdata:
    searchByName: http://local-gateway.datauns.cn/ce22highlevelsearch/highSearch/getUncidByentName

elasticsearch:
  hosts: ***********:9200

channel:
  sourceSecret: 31b9763b659c49489d348c2e6a261a64