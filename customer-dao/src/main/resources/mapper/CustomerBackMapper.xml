<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ce.scrm.customer.dao.mapper.CustomerBackMapper">

    <resultMap id="BaseResultMap" type="com.ce.scrm.customer.dao.entity.CustomerBack">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
            <result property="sourceDataId" column="source_data_id" jdbcType="VARCHAR"/>
            <result property="customerType" column="customer_type" jdbcType="TINYINT"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="certificateType" column="certificate_type" jdbcType="TINYINT"/>
            <result property="certificateCode" column="certificate_code" jdbcType="VARCHAR"/>
            <result property="checkInState" column="check_in_state" jdbcType="VARCHAR"/>
            <result property="establishDate" column="establish_date" jdbcType="DATE"/>
            <result property="registerCapital" column="register_capital" jdbcType="VARCHAR"/>
            <result property="paidInCapital" column="paid_in_capital" jdbcType="VARCHAR"/>
            <result property="organizationCode" column="organization_code" jdbcType="VARCHAR"/>
            <result property="registerNo" column="register_no" jdbcType="VARCHAR"/>
            <result property="taxpayerNo" column="taxpayer_no" jdbcType="VARCHAR"/>
            <result property="enterpriseType" column="enterprise_type" jdbcType="VARCHAR"/>
            <result property="openStartTime" column="open_start_time" jdbcType="DATE"/>
            <result property="openEndTime" column="open_end_time" jdbcType="DATE"/>
            <result property="taxpayerQualification" column="taxpayer_qualification" jdbcType="VARCHAR"/>
            <result property="staffScale" column="staff_scale" jdbcType="VARCHAR"/>
            <result property="insuredNum" column="insured_num" jdbcType="VARCHAR"/>
            <result property="approveDate" column="approve_date" jdbcType="DATE"/>
            <result property="provinceCode" column="province_code" jdbcType="VARCHAR"/>
            <result property="provinceName" column="province_name" jdbcType="VARCHAR"/>
            <result property="cityCode" column="city_code" jdbcType="VARCHAR"/>
            <result property="cityName" column="city_name" jdbcType="VARCHAR"/>
            <result property="districtCode" column="district_code" jdbcType="VARCHAR"/>
            <result property="districtName" column="district_name" jdbcType="VARCHAR"/>
            <result property="registrationAuthority" column="registration_authority" jdbcType="VARCHAR"/>
            <result property="importExportEnterpriseCode" column="import_export_enterprise_code" jdbcType="VARCHAR"/>
            <result property="firstIndustryCode" column="first_industry_code" jdbcType="VARCHAR"/>
            <result property="firstIndustryName" column="first_industry_name" jdbcType="VARCHAR"/>
            <result property="secondIndustryCode" column="second_industry_code" jdbcType="VARCHAR"/>
            <result property="secondIndustryName" column="second_industry_name" jdbcType="VARCHAR"/>
            <result property="registerAddress" column="register_address" jdbcType="VARCHAR"/>
            <result property="businessScope" column="business_scope" jdbcType="VARCHAR"/>
            <result property="presentStage" column="present_stage" jdbcType="TINYINT"/>
            <result property="createWay" column="create_way" jdbcType="TINYINT"/>
            <result property="labelFrom" column="label_from" jdbcType="VARCHAR"/>
            <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="creatorKey" column="creator_key" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="operatorKey" column="operator_key" jdbcType="VARCHAR"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
            <result property="invalidFlag" column="invalid_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_id,source_data_id,
        customer_type,customer_name,certificate_type,
        certificate_code,check_in_state,establish_date,
        register_capital,paid_in_capital,organization_code,
        register_no,taxpayer_no,enterprise_type,
        open_start_time,open_end_time,taxpayer_qualification,
        staff_scale,insured_num,approve_date,
        province_code,province_name,city_code,
        city_name,district_code,district_name,
        registration_authority,import_export_enterprise_code,first_industry_code,
        first_industry_name,second_industry_code,second_industry_name,
        register_address,business_scope,present_stage,
        create_way,label_from,delete_flag,
        create_time,creator_key,creator,
        update_time,operator_key,operator,
        invalid_flag
    </sql>
</mapper>
