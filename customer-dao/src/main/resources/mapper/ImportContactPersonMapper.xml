<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ce.scrm.customer.dao.mapper.ImportContactPersonMapper">

    <resultMap id="BaseResultMap" type="com.ce.scrm.customer.dao.entity.ImportContactPerson">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
            <result property="contactPersonName" column="contact_person_name" jdbcType="VARCHAR"/>
            <result property="gender" column="gender" jdbcType="TINYINT"/>
            <result property="department" column="department" jdbcType="VARCHAR"/>
            <result property="position" column="position" jdbcType="VARCHAR"/>
            <result property="certificatesNumber" column="certificates_number" jdbcType="VARCHAR"/>
            <result property="provinceCode" column="province_code" jdbcType="VARCHAR"/>
            <result property="cityCode" column="city_code" jdbcType="VARCHAR"/>
            <result property="districtCode" column="district_code" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="sourceKey" column="source_key" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="wechat" column="wechat" jdbcType="VARCHAR"/>
            <result property="wecome" column="wecome" jdbcType="VARCHAR"/>
            <result property="qq" column="qq" jdbcType="VARCHAR"/>
            <result property="telephone" column="telephone" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_id,contact_person_name,
        gender,department,position,
        certificates_number,province_code,city_code,
        district_code,address,source_key,
        remarks,phone,email,
        wechat,wecome,qq,
        telephone,create_time,creator,
        update_time,operator
    </sql>
</mapper>
