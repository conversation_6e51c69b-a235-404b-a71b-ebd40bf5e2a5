<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ce.scrm.customer.dao.mapper.abm.CustomerDistributeSdrRecordMapper">

    <select id="countCustomerByEmp" resultType="com.ce.scrm.customer.dao.entity.abm.EmpCustomerCountVo">
        SELECT emp_id AS empId,
               emp_name AS empName,
               COUNT(DISTINCT customer_id) AS distributeCount
        FROM customer_distribute_sdr_record
        WHERE activity_id = #{activityId}
        GROUP BY emp_id, emp_name
    </select>
</mapper>
