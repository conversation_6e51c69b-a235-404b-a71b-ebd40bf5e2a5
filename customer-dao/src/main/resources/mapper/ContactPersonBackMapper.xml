<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ce.scrm.customer.dao.mapper.ContactPersonBackMapper">

    <resultMap id="BaseResultMap" type="com.ce.scrm.customer.dao.entity.ContactPersonBack">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="contactPersonId" column="contact_person_id" jdbcType="VARCHAR"/>
            <result property="contactPersonName" column="contact_person_name" jdbcType="VARCHAR"/>
            <result property="bindType" column="bind_type" jdbcType="TINYINT"/>
            <result property="unionId" column="union_id" jdbcType="VARCHAR"/>
            <result property="unionIdBindTime" column="union_id_bind_time" jdbcType="TIMESTAMP"/>
            <result property="gender" column="gender" jdbcType="TINYINT"/>
            <result property="department" column="department" jdbcType="VARCHAR"/>
            <result property="position" column="position" jdbcType="VARCHAR"/>
            <result property="certificatesType" column="certificates_type" jdbcType="TINYINT"/>
            <result property="certificatesNumber" column="certificates_number" jdbcType="VARCHAR"/>
            <result property="provinceCode" column="province_code" jdbcType="VARCHAR"/>
            <result property="provinceName" column="province_name" jdbcType="VARCHAR"/>
            <result property="cityCode" column="city_code" jdbcType="VARCHAR"/>
            <result property="cityName" column="city_name" jdbcType="VARCHAR"/>
            <result property="districtCode" column="district_code" jdbcType="VARCHAR"/>
            <result property="districtName" column="district_name" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="sourceKey" column="source_key" jdbcType="VARCHAR"/>
            <result property="sourceTag" column="source_tag" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="creatorKey" column="creator_key" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="operatorKey" column="operator_key" jdbcType="VARCHAR"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,contact_person_id,contact_person_name,
        bind_type,union_id,union_id_bind_time,
        gender,department,position,
        certificates_type,certificates_number,province_code,
        province_name,city_code,city_name,
        district_code,district_name,address,
        source_key,source_tag,remarks,
        delete_flag,create_time,creator_key,
        creator,update_time,operator_key,
        operator
    </sql>
</mapper>
