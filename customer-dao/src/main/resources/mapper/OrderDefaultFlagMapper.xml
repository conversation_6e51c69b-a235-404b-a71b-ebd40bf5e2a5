<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ce.scrm.customer.dao.mapper.OrderDefaultFlagMapper">

    <resultMap id="BaseResultMap" type="com.ce.scrm.customer.dao.entity.OrderDefaultFlag">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
            <result property="contactPersonId" column="contact_person_id" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_id,contact_person_id,
        phone,email,delete_flag,
        create_time,creator,update_time,
        operator
    </sql>
</mapper>
