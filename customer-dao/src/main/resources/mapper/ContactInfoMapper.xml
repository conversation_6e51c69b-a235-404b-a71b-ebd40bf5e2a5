<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ce.scrm.customer.dao.mapper.ContactInfoMapper">

    <resultMap id="BaseResultMap" type="com.ce.scrm.customer.dao.entity.ContactInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="contactInfoId" column="contact_info_id" jdbcType="VARCHAR"/>
            <result property="contactPersonId" column="contact_person_id" jdbcType="VARCHAR"/>
            <result property="contactType" column="contact_type" jdbcType="TINYINT"/>
            <result property="contactWay" column="contact_way" jdbcType="VARCHAR"/>
            <result property="phoneFlag" column="phone_flag" jdbcType="TINYINT"/>
            <result property="sourceKey" column="source_key" jdbcType="VARCHAR"/>
            <result property="signatoryFlag" column="signatory_flag" jdbcType="TINYINT"/>
            <result property="memberFlag" column="member_flag" jdbcType="TINYINT"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="creatorKey" column="creator_key" jdbcType="VARCHAR"/>
            <result property="creator" column="creator" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="operatorKey" column="operator_key" jdbcType="VARCHAR"/>
            <result property="operator" column="operator" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,contact_info_id,contact_person_id,
        contact_type,contact_way,phone_flag,
        source_key,signatory_flag,member_flag,
        remark,delete_flag,create_time,
        creator_key,creator,update_time,
        operator_key,operator
    </sql>
</mapper>
