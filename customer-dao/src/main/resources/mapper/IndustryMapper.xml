<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ce.scrm.customer.dao.mapper.IndustryMapper">

    <resultMap id="BaseResultMap" type="com.ce.scrm.customer.dao.entity.Industry">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="parentCode" column="parent_code" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="sortId" column="sort_id" jdbcType="INTEGER"/>
            <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,code,parent_code,
        name,sort_id,delete_flag,
        create_time,update_time
    </sql>
</mapper>
