<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ce.scrm.customer.dao.mapper.ChannelSourceMapper">

    <resultMap id="BaseResultMap" type="com.ce.scrm.customer.dao.entity.ChannelSource">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="sourceKey" column="source_key" jdbcType="VARCHAR"/>
            <result property="sourceName" column="source_name" jdbcType="VARCHAR"/>
            <result property="sourceShowName" column="source_show_name" jdbcType="VARCHAR"/>
            <result property="sourceSecret" column="source_secret" jdbcType="VARCHAR"/>
            <result property="validState" column="valid_state" jdbcType="TINYINT"/>
            <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,source_key,source_name,
        source_show_name,source_secret,valid_state,
        delete_flag,create_time,update_time
    </sql>
</mapper>
