package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("customer_share_temporary")
public class CustomerShareTemporary implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 商务代表ID
     */
    private String salerId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门ID
     */
    private String buId;

    /**
     * 分公司ID
     */
    private String subId;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 分享时间
     */
    private Date shareTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public CustomerShareTemporary() {}

}
