package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 客户备份表
 * @TableName customer_back
 */
@TableName(value ="customer_back")
@Data
public class CustomerBack implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户id：C、企业客户标识，P、个人客户标识，Q、其他客户标识（历史客户ID保持不变）
     */
    private String customerId;

    /**
     * 源数据ID
     */
    private String sourceDataId;

    /**
     * 客户类型：1、国内企业，2、个人，3、国外及港澳台
     */
    private Integer customerType;

    /**
     * 客户/企业名称
     */
    private String customerName;

    /**
     * 证件类型：1、营业执照，2、身份证，3、军人身份证，4、护照，5、港澳通行证
     */
    private Integer certificateType;

    /**
     * 证件编码（社会信用代码）
     */
    private String certificateCode;

    /**
     * 登记状态
     */
    private String checkInState;

    /**
     * 成立日期
     */
    private LocalDate establishDate;

    /**
     * 注册资本
     */
    private String registerCapital;

    /**
     * 实缴资本
     */
    private String paidInCapital;

    /**
     * 组织机构代码
     */
    private String organizationCode;

    /**
     * 工商注册号 
     */
    private String registerNo;

    /**
     * 纳税人识别号
     */
    private String taxpayerNo;

    /**
     * 企业类型
     */
    private String enterpriseType;

    /**
     * 营业开始时间
     */
    private LocalDate openStartTime;

    /**
     * 营业结束时间
     */
    private LocalDate openEndTime;

    /**
     * 纳税人资质
     */
    private String taxpayerQualification;

    /**
     * 人员规模
     */
    private String staffScale;

    /**
     * 参保人数
     */
    private String insuredNum;

    /**
     * 核准日期
     */
    private LocalDate approveDate;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 登记机关
     */
    private String registrationAuthority;

    /**
     * 进出口企业代码
     */
    private String importExportEnterpriseCode;

    /**
     * 一级国标行业编码
     */
    private String firstIndustryCode;

    /**
     * 一级国标行业名称
     */
    private String firstIndustryName;

    /**
     * 二级国标行业编码
     */
    private String secondIndustryCode;

    /**
     * 二级国标行业名称
     */
    private String secondIndustryName;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 客户当前阶段：1、线索，2、商机，3、保有客户，4、流失客户
     */
    private Integer presentStage;

    /**
     * 客户创建方式：1、大数据，2、商务创建，3、市场商机
     */
    private Integer createWay;

    /**
     * 客户来源
     */
    private String labelFrom;

    /**
     * 删除标记：1、删除，0、未删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者凭证（废弃）
     */
    private String creatorKey;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人凭证（废弃）
     */
    private String operatorKey;

    /**
     * 更新人
     */
    private String operator;

    /**
     * 无效标记：1、无效，0、有效
     */
    private Integer invalidFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CustomerBack other = (CustomerBack) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCustomerId() == null ? other.getCustomerId() == null : this.getCustomerId().equals(other.getCustomerId()))
            && (this.getSourceDataId() == null ? other.getSourceDataId() == null : this.getSourceDataId().equals(other.getSourceDataId()))
            && (this.getCustomerType() == null ? other.getCustomerType() == null : this.getCustomerType().equals(other.getCustomerType()))
            && (this.getCustomerName() == null ? other.getCustomerName() == null : this.getCustomerName().equals(other.getCustomerName()))
            && (this.getCertificateType() == null ? other.getCertificateType() == null : this.getCertificateType().equals(other.getCertificateType()))
            && (this.getCertificateCode() == null ? other.getCertificateCode() == null : this.getCertificateCode().equals(other.getCertificateCode()))
            && (this.getCheckInState() == null ? other.getCheckInState() == null : this.getCheckInState().equals(other.getCheckInState()))
            && (this.getEstablishDate() == null ? other.getEstablishDate() == null : this.getEstablishDate().equals(other.getEstablishDate()))
            && (this.getRegisterCapital() == null ? other.getRegisterCapital() == null : this.getRegisterCapital().equals(other.getRegisterCapital()))
            && (this.getPaidInCapital() == null ? other.getPaidInCapital() == null : this.getPaidInCapital().equals(other.getPaidInCapital()))
            && (this.getOrganizationCode() == null ? other.getOrganizationCode() == null : this.getOrganizationCode().equals(other.getOrganizationCode()))
            && (this.getRegisterNo() == null ? other.getRegisterNo() == null : this.getRegisterNo().equals(other.getRegisterNo()))
            && (this.getTaxpayerNo() == null ? other.getTaxpayerNo() == null : this.getTaxpayerNo().equals(other.getTaxpayerNo()))
            && (this.getEnterpriseType() == null ? other.getEnterpriseType() == null : this.getEnterpriseType().equals(other.getEnterpriseType()))
            && (this.getOpenStartTime() == null ? other.getOpenStartTime() == null : this.getOpenStartTime().equals(other.getOpenStartTime()))
            && (this.getOpenEndTime() == null ? other.getOpenEndTime() == null : this.getOpenEndTime().equals(other.getOpenEndTime()))
            && (this.getTaxpayerQualification() == null ? other.getTaxpayerQualification() == null : this.getTaxpayerQualification().equals(other.getTaxpayerQualification()))
            && (this.getStaffScale() == null ? other.getStaffScale() == null : this.getStaffScale().equals(other.getStaffScale()))
            && (this.getInsuredNum() == null ? other.getInsuredNum() == null : this.getInsuredNum().equals(other.getInsuredNum()))
            && (this.getApproveDate() == null ? other.getApproveDate() == null : this.getApproveDate().equals(other.getApproveDate()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getDistrictCode() == null ? other.getDistrictCode() == null : this.getDistrictCode().equals(other.getDistrictCode()))
            && (this.getDistrictName() == null ? other.getDistrictName() == null : this.getDistrictName().equals(other.getDistrictName()))
            && (this.getRegistrationAuthority() == null ? other.getRegistrationAuthority() == null : this.getRegistrationAuthority().equals(other.getRegistrationAuthority()))
            && (this.getImportExportEnterpriseCode() == null ? other.getImportExportEnterpriseCode() == null : this.getImportExportEnterpriseCode().equals(other.getImportExportEnterpriseCode()))
            && (this.getFirstIndustryCode() == null ? other.getFirstIndustryCode() == null : this.getFirstIndustryCode().equals(other.getFirstIndustryCode()))
            && (this.getFirstIndustryName() == null ? other.getFirstIndustryName() == null : this.getFirstIndustryName().equals(other.getFirstIndustryName()))
            && (this.getSecondIndustryCode() == null ? other.getSecondIndustryCode() == null : this.getSecondIndustryCode().equals(other.getSecondIndustryCode()))
            && (this.getSecondIndustryName() == null ? other.getSecondIndustryName() == null : this.getSecondIndustryName().equals(other.getSecondIndustryName()))
            && (this.getRegisterAddress() == null ? other.getRegisterAddress() == null : this.getRegisterAddress().equals(other.getRegisterAddress()))
            && (this.getBusinessScope() == null ? other.getBusinessScope() == null : this.getBusinessScope().equals(other.getBusinessScope()))
            && (this.getPresentStage() == null ? other.getPresentStage() == null : this.getPresentStage().equals(other.getPresentStage()))
            && (this.getCreateWay() == null ? other.getCreateWay() == null : this.getCreateWay().equals(other.getCreateWay()))
            && (this.getLabelFrom() == null ? other.getLabelFrom() == null : this.getLabelFrom().equals(other.getLabelFrom()))
            && (this.getDeleteFlag() == null ? other.getDeleteFlag() == null : this.getDeleteFlag().equals(other.getDeleteFlag()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getCreatorKey() == null ? other.getCreatorKey() == null : this.getCreatorKey().equals(other.getCreatorKey()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getOperatorKey() == null ? other.getOperatorKey() == null : this.getOperatorKey().equals(other.getOperatorKey()))
            && (this.getOperator() == null ? other.getOperator() == null : this.getOperator().equals(other.getOperator()))
            && (this.getInvalidFlag() == null ? other.getInvalidFlag() == null : this.getInvalidFlag().equals(other.getInvalidFlag()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCustomerId() == null) ? 0 : getCustomerId().hashCode());
        result = prime * result + ((getSourceDataId() == null) ? 0 : getSourceDataId().hashCode());
        result = prime * result + ((getCustomerType() == null) ? 0 : getCustomerType().hashCode());
        result = prime * result + ((getCustomerName() == null) ? 0 : getCustomerName().hashCode());
        result = prime * result + ((getCertificateType() == null) ? 0 : getCertificateType().hashCode());
        result = prime * result + ((getCertificateCode() == null) ? 0 : getCertificateCode().hashCode());
        result = prime * result + ((getCheckInState() == null) ? 0 : getCheckInState().hashCode());
        result = prime * result + ((getEstablishDate() == null) ? 0 : getEstablishDate().hashCode());
        result = prime * result + ((getRegisterCapital() == null) ? 0 : getRegisterCapital().hashCode());
        result = prime * result + ((getPaidInCapital() == null) ? 0 : getPaidInCapital().hashCode());
        result = prime * result + ((getOrganizationCode() == null) ? 0 : getOrganizationCode().hashCode());
        result = prime * result + ((getRegisterNo() == null) ? 0 : getRegisterNo().hashCode());
        result = prime * result + ((getTaxpayerNo() == null) ? 0 : getTaxpayerNo().hashCode());
        result = prime * result + ((getEnterpriseType() == null) ? 0 : getEnterpriseType().hashCode());
        result = prime * result + ((getOpenStartTime() == null) ? 0 : getOpenStartTime().hashCode());
        result = prime * result + ((getOpenEndTime() == null) ? 0 : getOpenEndTime().hashCode());
        result = prime * result + ((getTaxpayerQualification() == null) ? 0 : getTaxpayerQualification().hashCode());
        result = prime * result + ((getStaffScale() == null) ? 0 : getStaffScale().hashCode());
        result = prime * result + ((getInsuredNum() == null) ? 0 : getInsuredNum().hashCode());
        result = prime * result + ((getApproveDate() == null) ? 0 : getApproveDate().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getDistrictCode() == null) ? 0 : getDistrictCode().hashCode());
        result = prime * result + ((getDistrictName() == null) ? 0 : getDistrictName().hashCode());
        result = prime * result + ((getRegistrationAuthority() == null) ? 0 : getRegistrationAuthority().hashCode());
        result = prime * result + ((getImportExportEnterpriseCode() == null) ? 0 : getImportExportEnterpriseCode().hashCode());
        result = prime * result + ((getFirstIndustryCode() == null) ? 0 : getFirstIndustryCode().hashCode());
        result = prime * result + ((getFirstIndustryName() == null) ? 0 : getFirstIndustryName().hashCode());
        result = prime * result + ((getSecondIndustryCode() == null) ? 0 : getSecondIndustryCode().hashCode());
        result = prime * result + ((getSecondIndustryName() == null) ? 0 : getSecondIndustryName().hashCode());
        result = prime * result + ((getRegisterAddress() == null) ? 0 : getRegisterAddress().hashCode());
        result = prime * result + ((getBusinessScope() == null) ? 0 : getBusinessScope().hashCode());
        result = prime * result + ((getPresentStage() == null) ? 0 : getPresentStage().hashCode());
        result = prime * result + ((getCreateWay() == null) ? 0 : getCreateWay().hashCode());
        result = prime * result + ((getLabelFrom() == null) ? 0 : getLabelFrom().hashCode());
        result = prime * result + ((getDeleteFlag() == null) ? 0 : getDeleteFlag().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreatorKey() == null) ? 0 : getCreatorKey().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getOperatorKey() == null) ? 0 : getOperatorKey().hashCode());
        result = prime * result + ((getOperator() == null) ? 0 : getOperator().hashCode());
        result = prime * result + ((getInvalidFlag() == null) ? 0 : getInvalidFlag().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", customerId=").append(customerId);
        sb.append(", sourceDataId=").append(sourceDataId);
        sb.append(", customerType=").append(customerType);
        sb.append(", customerName=").append(customerName);
        sb.append(", certificateType=").append(certificateType);
        sb.append(", certificateCode=").append(certificateCode);
        sb.append(", checkInState=").append(checkInState);
        sb.append(", establishDate=").append(establishDate);
        sb.append(", registerCapital=").append(registerCapital);
        sb.append(", paidInCapital=").append(paidInCapital);
        sb.append(", organizationCode=").append(organizationCode);
        sb.append(", registerNo=").append(registerNo);
        sb.append(", taxpayerNo=").append(taxpayerNo);
        sb.append(", enterpriseType=").append(enterpriseType);
        sb.append(", openStartTime=").append(openStartTime);
        sb.append(", openEndTime=").append(openEndTime);
        sb.append(", taxpayerQualification=").append(taxpayerQualification);
        sb.append(", staffScale=").append(staffScale);
        sb.append(", insuredNum=").append(insuredNum);
        sb.append(", approveDate=").append(approveDate);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", cityName=").append(cityName);
        sb.append(", districtCode=").append(districtCode);
        sb.append(", districtName=").append(districtName);
        sb.append(", registrationAuthority=").append(registrationAuthority);
        sb.append(", importExportEnterpriseCode=").append(importExportEnterpriseCode);
        sb.append(", firstIndustryCode=").append(firstIndustryCode);
        sb.append(", firstIndustryName=").append(firstIndustryName);
        sb.append(", secondIndustryCode=").append(secondIndustryCode);
        sb.append(", secondIndustryName=").append(secondIndustryName);
        sb.append(", registerAddress=").append(registerAddress);
        sb.append(", businessScope=").append(businessScope);
        sb.append(", presentStage=").append(presentStage);
        sb.append(", createWay=").append(createWay);
        sb.append(", labelFrom=").append(labelFrom);
        sb.append(", deleteFlag=").append(deleteFlag);
        sb.append(", createTime=").append(createTime);
        sb.append(", creatorKey=").append(creatorKey);
        sb.append(", creator=").append(creator);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", operatorKey=").append(operatorKey);
        sb.append(", operator=").append(operator);
        sb.append(", invalidFlag=").append(invalidFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}