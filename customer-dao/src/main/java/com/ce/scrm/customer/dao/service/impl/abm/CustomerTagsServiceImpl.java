package com.ce.scrm.customer.dao.service.impl.abm;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.abm.CustomerTags;
import com.ce.scrm.customer.dao.service.abm.CustomerTagsService;
import com.ce.scrm.customer.dao.mapper.abm.CustomerTagsMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【customer_tags(客户基础标签字典表)】的数据库操作Service实现
* @createDate 2025-07-09 14:14:46
*/
@Service
public class CustomerTagsServiceImpl extends ServiceImpl<CustomerTagsMapper, CustomerTags> implements CustomerTagsService{

}




