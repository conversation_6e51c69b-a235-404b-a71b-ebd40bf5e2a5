package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户标签履历
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/2/21 14:54
 */
@TableName(value ="customer_tag_history")
public class CustomerTagHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户id
     */
    @TableField(value = "customer_id")
    private String customerId;

    /**
     * 商务ID
     */
    @TableField(value = "saler_id")
    private String salerId;

    /**
     * 部门ID
     */
    @TableField(value = "dept_id")
    private String deptId;

    /**
     * 分司ID
     */
    @TableField(value = "sub_id")
    private String subId;

    /**
     * 区域ID
     */
    @TableField(value = "area_id")
    private String areaId;

    /**
     * 是否门户连带客户 0:否，1:是
     */
    @TableField(value = "tag_menhu_related")
    private Integer tagMenhuRelated;

    /**
     * 是否生态转门户 0:否，1:是
     */
    @TableField(value = "tag_eco_to_menhu")
    private Integer tagEcoToMenhu;

    /**
     * 是否生态客户 0:否，1:是
     */
    @TableField(value = "tag_eco_cust")
    private Integer tagEcoCust;

    /**
     * 是否数字版本门户 0:否，1:是
     */
    @TableField(value = "tag_menhu_digital")
    private Integer tagMenhuDigital;

    /**
     * 是否2023版本门户 0:否，1:是
     */
    @TableField(value = "tag_menhu_2023")
    private Integer tagMenhu2023;

    /**
     * 是否低版本门户 0:否，1:是
     */
    @TableField(value = "tag_menhu_lowver")
    private Integer tagMenhuLowver;

    /**
     * 是否门户应升已升客户 0:否，1:是
     */
    @TableField(value = "tag_menhu_upgradeable_upgrade")
    private Integer tagMenhuUpgradeableUpgrade;

    /**
     * 是否门户应升级客户 0:否，1:是
     */
    @TableField(value = "tag_menhu_upgradeable")
    private Integer tagMenhuUpgradeable;

    /**
     * 是否门户应续已续客户 0:否，1:是
     */
    @TableField(value = "tag_menhu_renewable_renew")
    private Integer tagMenhuRenewableRenew;

    /**
     * 是否门户应续客户 0:否，1:是
     */
    @TableField(value = "tag_menhu_renewable")
    private Integer tagMenhuRenewable;

    /**
     * 是否交叉购买客户 0:否，1:是
     */
    @TableField(value = "tag_cross_buy")
    private Integer tagCrossBuy;

    /**
     * 是否纯门户客户 0:否，1:是
     */
    @TableField(value = "tag_pure_menhu")
    private Integer tagPureMenhu;

    /**
     * 门户新客户首次购买时间
     */
    @TableField(value = "tag_menhu_new_time")
    private Date tagMenhuNewTime;

    /**
     * 门户新客户首次购买产品类别 0:低版本 1:门户2023 2:数字门户
     */
    @TableField(value = "tag_menhu_new_category")
    private String tagMenhuNewCategory;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getSalerId() {
        return salerId;
    }

    public void setSalerId(String salerId) {
        this.salerId = salerId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public Integer getTagMenhuRelated() {
        return tagMenhuRelated;
    }

    public void setTagMenhuRelated(Integer tagMenhuRelated) {
        this.tagMenhuRelated = tagMenhuRelated;
    }

    public Integer getTagEcoToMenhu() {
        return tagEcoToMenhu;
    }

    public void setTagEcoToMenhu(Integer tagEcoToMenhu) {
        this.tagEcoToMenhu = tagEcoToMenhu;
    }

    public Integer getTagEcoCust() {
        return tagEcoCust;
    }

    public void setTagEcoCust(Integer tagEcoCust) {
        this.tagEcoCust = tagEcoCust;
    }

    public Integer getTagMenhuDigital() {
        return tagMenhuDigital;
    }

    public void setTagMenhuDigital(Integer tagMenhuDigital) {
        this.tagMenhuDigital = tagMenhuDigital;
    }

    public Integer getTagMenhu2023() {
        return tagMenhu2023;
    }

    public void setTagMenhu2023(Integer tagMenhu2023) {
        this.tagMenhu2023 = tagMenhu2023;
    }

    public Integer getTagMenhuLowver() {
        return tagMenhuLowver;
    }

    public void setTagMenhuLowver(Integer tagMenhuLowver) {
        this.tagMenhuLowver = tagMenhuLowver;
    }

    public Integer getTagMenhuUpgradeableUpgrade() {
        return tagMenhuUpgradeableUpgrade;
    }

    public void setTagMenhuUpgradeableUpgrade(Integer tagMenhuUpgradeableUpgrade) {
        this.tagMenhuUpgradeableUpgrade = tagMenhuUpgradeableUpgrade;
    }

    public Integer getTagMenhuUpgradeable() {
        return tagMenhuUpgradeable;
    }

    public void setTagMenhuUpgradeable(Integer tagMenhuUpgradeable) {
        this.tagMenhuUpgradeable = tagMenhuUpgradeable;
    }

    public Integer getTagMenhuRenewableRenew() {
        return tagMenhuRenewableRenew;
    }

    public void setTagMenhuRenewableRenew(Integer tagMenhuRenewableRenew) {
        this.tagMenhuRenewableRenew = tagMenhuRenewableRenew;
    }

    public Integer getTagMenhuRenewable() {
        return tagMenhuRenewable;
    }

    public void setTagMenhuRenewable(Integer tagMenhuRenewable) {
        this.tagMenhuRenewable = tagMenhuRenewable;
    }

    public Integer getTagCrossBuy() {
        return tagCrossBuy;
    }

    public void setTagCrossBuy(Integer tagCrossBuy) {
        this.tagCrossBuy = tagCrossBuy;
    }

    public Integer getTagPureMenhu() {
        return tagPureMenhu;
    }

    public void setTagPureMenhu(Integer tagPureMenhu) {
        this.tagPureMenhu = tagPureMenhu;
    }

    public Date getTagMenhuNewTime() {
        return tagMenhuNewTime;
    }

    public void setTagMenhuNewTime(Date tagMenhuNewTime) {
        this.tagMenhuNewTime = tagMenhuNewTime;
    }

    public String getTagMenhuNewCategory() {
        return tagMenhuNewCategory;
    }

    public void setTagMenhuNewCategory(String tagMenhuNewCategory) {
        this.tagMenhuNewCategory = tagMenhuNewCategory;
    }
}
