package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * topic 开关表
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/7/10 22:15
 */
@TableName(value ="rocket_topic_switch")
@Data
public class RocketTopicSwitch implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * topic 名称
     */
    @TableField(value = "topic")
    private String topic;

    /**
     * topic 状态
     * 0:rabbitmq
     * 1:rocketmq
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;


}
