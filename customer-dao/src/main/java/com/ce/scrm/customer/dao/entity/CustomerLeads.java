package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 客户leads
 * @TableName customer_leads
 */
@TableName(value ="customer_leads")
@Data
public class CustomerLeads implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 数据来源 0:crm原有渠道  1:营销活动
     */
    private Integer dataFromSource;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * leads类别
     */
    private String leadsType;

    /**
     * leads code
     */
    private String leadsCode;

    /**
     * leads 来源
     */
    private String leadsSource;

    /**
     * leads url
     */
    private String leadsUrl;

    /**
     * Leads来源说明
     */
    private String leadsDesc;

    /**
     * 创建者
     */
    private String createdId;

    /**
     * 创建时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}