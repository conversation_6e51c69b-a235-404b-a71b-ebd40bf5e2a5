package com.ce.scrm.customer.dao.entity.abm;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 营销活动效果执行分发记录表
 * @TableName customer_marketing_activities_record
 */
@TableName(value ="customer_marketing_activities_record")
@Data
public class CustomerMarketingActivitiesRecord implements Serializable {
    /**
     * 活动记录表主键ID
     */
    @TableId
    private Long id;

    /**
     * 营销活动表id, customer_marketing_activities.id
     */
    private Long activityId;

    /**
     * 客户ID（customer.customer_id）
     */
    private String customerId;

	/**
	 * 活动类型/触达方式 1-短信 2-邮件 3-电话 4-其他
	 */
	private Integer activityType;

	/**
	 * 活动内容/触达内容
	 */
	private String activityContent;

	/**
	 * 联系方式
	 */
	private String contactWay;

	/**
	 * 创建时间（拉取到客户信息的时间）
	 */
	private Date createTime;

	/**
	 * 是否执行活动 0-未执行 1-已执行
	 */
	private Integer isExecute;

	/**
	 * 是否分发SDR 0-未分发 1-已分发
	 */
	private Integer isDistribute;

    /**
     * 执行活动时间
     */
    private Date executeTime;

	/**
	 * 线索leadsId
	 */
	private Long leadsId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}