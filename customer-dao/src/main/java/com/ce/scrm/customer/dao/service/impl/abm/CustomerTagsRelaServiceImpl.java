package com.ce.scrm.customer.dao.service.impl.abm;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.abm.CustomerTagsRela;
import com.ce.scrm.customer.dao.service.abm.CustomerTagsRelaService;
import com.ce.scrm.customer.dao.mapper.abm.CustomerTagsRelaMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【customer_tags_rela(客户与标签关联表)】的数据库操作Service实现
* @createDate 2025-07-09 14:18:42
*/
@Service
public class CustomerTagsRelaServiceImpl extends ServiceImpl<CustomerTagsRelaMapper, CustomerTagsRela> implements CustomerTagsRelaService{

}




