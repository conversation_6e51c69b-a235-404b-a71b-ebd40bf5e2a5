package com.ce.scrm.customer.dao.entity.abm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户标签积分变更历史表
 * @TableName customer_tag_weight_history
 */
@TableName(value ="customer_tag_weight_history")
@Data
public class CustomerTagWeightHistory implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 旧值
     */
    private Integer oldValue;

    /**
     * 新值
     */
    private Integer newValue;

    /**
     * 变更原因
     */
    private String reason;

    /**
     * 操作人员工ID
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updatedTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}