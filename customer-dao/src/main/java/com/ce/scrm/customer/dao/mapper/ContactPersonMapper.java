package com.ce.scrm.customer.dao.mapper;

import com.ce.scrm.customer.dao.entity.ContactPerson;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ce.scrm.customer.dao.entity.view.ContactPersonLegal;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【contact_person(联系人)】的数据库操作Mapper
* @createDate 2023-07-21 10:42:00
* @Entity com.ce.scrm.customer.dao.entity.ContactPerson
*/
public interface ContactPersonMapper extends BaseMapper<ContactPerson> {

    /**
     * 获取联系人法人
     * <AUTHOR>
     * @date 2024/8/30
     * @return java.util.List<com.ce.scrm.customer.dao.entity.view.ContactPersonLegal>
     **/
    List<ContactPersonLegal> getContactPersonLegalList();

    /**
     * 获取联系人法人数量
     * <AUTHOR>
     * @date 2024/9/13
     * @return java.lang.Integer
     **/
    Integer getContactPersonLegalCount();

}




