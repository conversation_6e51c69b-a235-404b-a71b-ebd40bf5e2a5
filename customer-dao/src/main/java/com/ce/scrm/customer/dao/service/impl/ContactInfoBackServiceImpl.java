package com.ce.scrm.customer.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.ContactInfoBack;
import com.ce.scrm.customer.dao.service.ContactInfoBackService;
import com.ce.scrm.customer.dao.mapper.ContactInfoBackMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【contact_info_back(联系途径备份表)】的数据库操作Service实现
* @createDate 2023-08-24 10:25:27
*/
@Service
public class ContactInfoBackServiceImpl extends ServiceImpl<ContactInfoBackMapper, ContactInfoBack>
    implements ContactInfoBackService{

}




