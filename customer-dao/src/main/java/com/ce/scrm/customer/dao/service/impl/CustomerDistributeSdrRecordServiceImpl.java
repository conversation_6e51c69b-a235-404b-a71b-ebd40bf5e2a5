package com.ce.scrm.customer.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.abm.CustomerDistributeSdrRecord;
import com.ce.scrm.customer.dao.service.abm.CustomerDistributeSdrRecordService;
import com.ce.scrm.customer.dao.mapper.abm.CustomerDistributeSdrRecordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【customer_distribute_sdr_record(电话活动下发SDR的记录)】的数据库操作Service实现
* @createDate 2025-08-07 16:12:09
*/
@Service
public class CustomerDistributeSdrRecordServiceImpl extends ServiceImpl<CustomerDistributeSdrRecordMapper, CustomerDistributeSdrRecord> implements CustomerDistributeSdrRecordService{

}




