package com.ce.scrm.customer.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.dao.mapper.CustomerMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【customer(客户表)】的数据库操作Service实现
* @createDate 2023-06-26 11:10:31
*/
@Service
public class CustomerServiceImpl extends ServiceImpl<CustomerMapper, Customer>
    implements CustomerService{

    @Override
    public Long getNextMinId(Long currentId) {
        return baseMapper.getNextMinId(currentId);
    }
}




