package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 联系途径
 * @TableName contact_info
 */
@TableName(value ="contact_info")
@Data
public class ContactInfo implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 联系方式ID
     */
    @TableField(value = "contact_info_id")
    private String contactInfoId;

    /**
     * 联系人ID
     */
    @TableField(value = "contact_person_id")
    private String contactPersonId;

    /**
     * 联系类型：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
     */
    @TableField(value = "contact_type")
    private Integer contactType;

    /**
     * 联系方式
     */
    @TableField(value = "contact_way")
    private String contactWay;

    /**
     * 手机号标识：1、正常，2、黑名单，3、空号
     */
    @TableField(value = "phone_flag")
    private Integer phoneFlag;

    /**
     * 联系方式来源key
     */
    @TableField(value = "source_key")
    private String sourceKey;

    /**
     * 是否是签单人手机号：1、是，0、否
     */
    @TableField(value = "signatory_flag")
    private Integer signatoryFlag;

    /**
     * 是否是300会员手机号：1、是，0、否
     */
    @TableField(value = "member_flag")
    private Integer memberFlag;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 删除标记：1、删除，0、未删除
     */
    @TableField(value = "delete_flag")
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建者凭证
     */
    @TableField(value = "creator_key")
    private String creatorKey;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人凭证
     */
    @TableField(value = "operator_key")
    private String operatorKey;

    /**
     * 更新人
     */
    @TableField(value = "operator")
    private String operator;

    /**
     * 手机号是否已验证 1是 0否
     */
    @TableField(value = "phone_verified_flag")
    private Integer phoneVerifiedFlag;

    /**
     * 手机号验证时间
     */
    @TableField(value = "phone_verified_time")
    private LocalDateTime phoneVerifiedTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}