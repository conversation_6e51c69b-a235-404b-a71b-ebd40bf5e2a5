package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 * @TableName import_contact_person
 */
@TableName(value ="import_contact_person")
@Data
public class ImportContactPerson implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户id：C、企业客户标识，P、个人客户标识，Q、其他客户标识（历史客户ID保持不变）
     */
    @TableField(value = "customer_id")
    private String customerId;

    /**
     * 联系人姓名
     */
    @TableField(value = "contact_person_name")
    private String contactPersonName;

    /**
     * 性别：0、未知，1、男，2、女
     */
    @TableField(value = "gender")
    private Integer gender;

    /**
     * 联系人部门
     */
    @TableField(value = "department")
    private String department;

    /**
     * 职位
     */
    @TableField(value = "position")
    private String position;

    /**
     * 证件号码
     */
    @TableField(value = "certificates_number")
    private String certificatesNumber;

    /**
     * 省编码
     */
    @TableField(value = "province_code")
    private String provinceCode;

    /**
     * 市编码
     */
    @TableField(value = "city_code")
    private String cityCode;

    /**
     * 区编码
     */
    @TableField(value = "district_code")
    private String districtCode;

    /**
     * 详细地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 联系人来源key
     */
    @TableField(value = "source_key")
    private String sourceKey;

    /**
     * 个性标签
     */
    @TableField(value = "remarks")
    private String remarks;

    /**
     * 手机号
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 邮箱
     */
    @TableField(value = "email")
    private String email;

    /**
     * 微信
     */
    @TableField(value = "wechat")
    private String wechat;

    /**
     * 企业微信
     */
    @TableField(value = "wecome")
    private String wecome;

    /**
     * qq
     */
    @TableField(value = "qq")
    private String qq;

    /**
     * 电话
     */
    @TableField(value = "telephone")
    private String telephone;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(value = "operator")
    private String operator;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}