package com.ce.scrm.customer.dao.service.impl.abm;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.abm.CustomerMarketingActivitiesRecord;
import com.ce.scrm.customer.dao.service.abm.CustomerMarketingActivitiesRecordService;
import com.ce.scrm.customer.dao.mapper.abm.CustomerMarketingActivitiesRecordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【customer_marketing_activities_record(营销活动效果分发记录表)】的数据库操作Service实现
* @createDate 2025-07-09 14:20:48
*/
@Service
public class CustomerMarketingActivitiesRecordServiceImpl extends ServiceImpl<CustomerMarketingActivitiesRecordMapper, CustomerMarketingActivitiesRecord> implements CustomerMarketingActivitiesRecordService{

}




