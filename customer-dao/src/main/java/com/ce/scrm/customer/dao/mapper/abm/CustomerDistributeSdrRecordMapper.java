package com.ce.scrm.customer.dao.mapper.abm;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ce.scrm.customer.dao.entity.abm.CustomerDistributeSdrRecord;
import com.ce.scrm.customer.dao.entity.abm.EmpCustomerCountVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【customer_distribute_sdr_record(电话活动下发SDR的记录)】的数据库操作Mapper
* @createDate 2025-08-07 16:12:09
* @Entity com.ce.scrm.customer.dao.entity.abm.CustomerDistributeSdrRecord
*/
public interface CustomerDistributeSdrRecordMapper extends BaseMapper<CustomerDistributeSdrRecord> {


	List<EmpCustomerCountVo> countCustomerByEmp(@Param("activityId") Long activityId);

}




