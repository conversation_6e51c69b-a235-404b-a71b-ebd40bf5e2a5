package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 导入客户
 * @Author: lijinpeng
 * @Date: 2025/3/24 11:07
 */
@Data
@TableName("customer_import")
public class CustomerImport implements Serializable {

    @TableId(type = IdType.AUTO)
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户/企业名称
     */
    private String customerName;

    /**
     * 是否已处理 0:否，1:是
     */
    private Integer startFlag;

    /**
     * 添加是否成功 0:否，1:是
     */
    private Integer addFlag;

    /**
     * 添加是否成功 0:否，1:是
     */
    private String failCause;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
