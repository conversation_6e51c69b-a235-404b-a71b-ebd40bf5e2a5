package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 渠道来源表
 * @TableName channel_source
 */
@TableName(value ="channel_source")
@Data
public class ChannelSource implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 来源key
     */
    @TableField(value = "source_key")
    private String sourceKey;

    /**
     * 来源名称
     */
    @TableField(value = "source_name")
    private String sourceName;

    /**
     * 来源展示名称
     */
    @TableField(value = "source_show_name")
    private String sourceShowName;

    /**
     * 来源密钥
     */
    @TableField(value = "source_secret")
    private String sourceSecret;

    /**
     * 有效状态：1、有效，0、无效
     */
    @TableField(value = "valid_state")
    private Integer validState;

    /**
     * 删除标记：1、删除，0、未删除
     */
    @TableField(value = "delete_flag")
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}