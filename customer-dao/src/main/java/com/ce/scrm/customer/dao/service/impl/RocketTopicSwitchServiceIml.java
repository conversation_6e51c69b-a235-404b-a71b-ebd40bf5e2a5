package com.ce.scrm.customer.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.RocketTopicSwitch;
import com.ce.scrm.customer.dao.mapper.RocketTopicSwitchMapper;
import com.ce.scrm.customer.dao.service.RocketTopicSwitchService;
import org.springframework.stereotype.Service;

/**
 * 针对表【rocket_topic_switch(topic开关表)】的数据库操作Service实现
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/7/10 22:20
 */
@Service
public class RocketTopicSwitchServiceIml extends ServiceImpl<RocketTopicSwitchMapper, RocketTopicSwitch> implements RocketTopicSwitchService {
}
