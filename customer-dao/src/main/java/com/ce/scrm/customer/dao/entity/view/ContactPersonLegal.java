package com.ce.scrm.customer.dao.entity.view;

import lombok.Data;

import java.io.Serializable;

/**
 * 联系人表获取法人
 * <AUTHOR>
 * @date 2024/8/30
 * @version 1.0.0
 */
@Data
public class ContactPersonLegal implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 是否法人 0、否，1、是
     */
    private Integer legalPersonFlag;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 联系人姓名
     */
    private String contactPersonName;
}