package com.ce.scrm.customer.dao.entity.abm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 客户标签分类表
 * @TableName customer_tags_category
 */
@TableName(value ="customer_tags_category")
@Data
public class CustomerTagsCategory implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 父级分类id
     */
    private Long parentId;

    /**
     * 分类级别 1:一级 2:二级 3:三级...
     */
    private Integer categoryLevel;

    /**
     * 标签分类名称, 如：1-基本属性 2-行为(参展， 销售行为) 3-交易属性(是否合作)等
     */
    private String categoryName;

    /**
     * 分类是否启用：1=启用, 0=禁用
     */
    private Integer isActive;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}