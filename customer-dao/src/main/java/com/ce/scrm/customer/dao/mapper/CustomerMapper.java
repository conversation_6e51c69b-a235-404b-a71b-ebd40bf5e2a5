package com.ce.scrm.customer.dao.mapper;

import com.ce.scrm.customer.dao.entity.Customer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【customer(客户表)】的数据库操作Mapper
* @createDate 2023-06-26 11:10:31
* @Entity com.ce.scrm.customer.dao.entity.Customer
*/
public interface CustomerMapper extends BaseMapper<Customer> {

    @Select("<script>"
            + "SELECT id "
            + "FROM customer "
            + "WHERE id >= #{currentId} "
            + "LIMIT 1 "
            + "</script>")
    Long getNextMinId(Long currentId);
}




