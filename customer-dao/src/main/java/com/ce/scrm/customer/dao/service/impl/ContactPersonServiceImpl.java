package com.ce.scrm.customer.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.ContactPerson;
import com.ce.scrm.customer.dao.service.ContactPersonService;
import com.ce.scrm.customer.dao.mapper.ContactPersonMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【contact_person(联系人)】的数据库操作Service实现
* @createDate 2023-07-21 10:42:00
*/
@Service
public class ContactPersonServiceImpl extends ServiceImpl<ContactPersonMapper, ContactPerson>
    implements ContactPersonService{

}




