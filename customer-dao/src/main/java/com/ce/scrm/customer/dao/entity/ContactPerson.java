package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 联系人
 * @TableName contact_person
 */
@TableName(value ="contact_person")
@Data
public class ContactPerson implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 联系人姓名
     */
    private String contactPersonName;

    /**
     * 绑定类型：0、无，1、微信，2、企业微信
     */
    private Integer bindType;

    /**
     * 微信unionId
     */
    private String unionId;

    /**
     * 绑定人ID
     */
    private String bindEmpId;

    /**
     * 微信昵称
     */
    private String wechatNickName;

    /**
     * unionId绑定时间
     */
    private LocalDateTime unionIdBindTime;

    /**
     * 性别：0、未知，1、男，2、女
     */
    private Integer gender;

    /**
     * 联系人部门
     */
    private String department;

    /**
     * 职位
     */
    private String position;

    /**
     * 证件类型
     */
    private Integer certificatesType;

    /**
     * 证件号码
     */
    private String certificatesNumber;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系人来源key
     */
    private String sourceKey;

    /**
     * 联系人来源标签，逗号分隔（字段已废弃）
     */
    private String sourceTag;

    /**
     * 个性标签
     */
    private String remarks;

    /**
     * 删除标记：1、删除，0、未删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者凭证（字段已废弃）
     */
    private String creatorKey;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人凭证
     */
    private String operatorKey;

    /**
     * 更新人
     */
    private String operator;

    /**
     * 是否是法人 1是 0否
     */
    private Integer legalPersonFlag;

    /**
     * 签单联系人 0、不是，1、临时，2、正式
     */
    private Integer signingPersonFlag;

    /**
     * 签单联系人更新时间
     */
    private LocalDateTime signingPersonUpdate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ContactPerson other = (ContactPerson) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getContactPersonId() == null ? other.getContactPersonId() == null : this.getContactPersonId().equals(other.getContactPersonId()))
            && (this.getContactPersonName() == null ? other.getContactPersonName() == null : this.getContactPersonName().equals(other.getContactPersonName()))
            && (this.getBindType() == null ? other.getBindType() == null : this.getBindType().equals(other.getBindType()))
            && (this.getUnionId() == null ? other.getUnionId() == null : this.getUnionId().equals(other.getUnionId()))
            && (this.getUnionIdBindTime() == null ? other.getUnionIdBindTime() == null : this.getUnionIdBindTime().equals(other.getUnionIdBindTime()))
            && (this.getGender() == null ? other.getGender() == null : this.getGender().equals(other.getGender()))
            && (this.getDepartment() == null ? other.getDepartment() == null : this.getDepartment().equals(other.getDepartment()))
            && (this.getPosition() == null ? other.getPosition() == null : this.getPosition().equals(other.getPosition()))
            && (this.getCertificatesType() == null ? other.getCertificatesType() == null : this.getCertificatesType().equals(other.getCertificatesType()))
            && (this.getCertificatesNumber() == null ? other.getCertificatesNumber() == null : this.getCertificatesNumber().equals(other.getCertificatesNumber()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getDistrictCode() == null ? other.getDistrictCode() == null : this.getDistrictCode().equals(other.getDistrictCode()))
            && (this.getDistrictName() == null ? other.getDistrictName() == null : this.getDistrictName().equals(other.getDistrictName()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getSourceKey() == null ? other.getSourceKey() == null : this.getSourceKey().equals(other.getSourceKey()))
            && (this.getSourceTag() == null ? other.getSourceTag() == null : this.getSourceTag().equals(other.getSourceTag()))
            && (this.getRemarks() == null ? other.getRemarks() == null : this.getRemarks().equals(other.getRemarks()))
            && (this.getDeleteFlag() == null ? other.getDeleteFlag() == null : this.getDeleteFlag().equals(other.getDeleteFlag()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getCreatorKey() == null ? other.getCreatorKey() == null : this.getCreatorKey().equals(other.getCreatorKey()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getOperatorKey() == null ? other.getOperatorKey() == null : this.getOperatorKey().equals(other.getOperatorKey()))
            && (this.getOperator() == null ? other.getOperator() == null : this.getOperator().equals(other.getOperator()))
            && (this.getLegalPersonFlag() == null ? other.getLegalPersonFlag() == null : this.getLegalPersonFlag().equals(other.getLegalPersonFlag()))
            && (this.getSigningPersonFlag() == null ? other.getSigningPersonFlag() == null : this.getSigningPersonFlag().equals(other.getSigningPersonFlag()))
            && (this.getSigningPersonUpdate() == null ? other.getSigningPersonUpdate() == null : this.getSigningPersonUpdate().equals(other.getSigningPersonUpdate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getContactPersonId() == null) ? 0 : getContactPersonId().hashCode());
        result = prime * result + ((getContactPersonName() == null) ? 0 : getContactPersonName().hashCode());
        result = prime * result + ((getBindType() == null) ? 0 : getBindType().hashCode());
        result = prime * result + ((getUnionId() == null) ? 0 : getUnionId().hashCode());
        result = prime * result + ((getUnionIdBindTime() == null) ? 0 : getUnionIdBindTime().hashCode());
        result = prime * result + ((getGender() == null) ? 0 : getGender().hashCode());
        result = prime * result + ((getDepartment() == null) ? 0 : getDepartment().hashCode());
        result = prime * result + ((getPosition() == null) ? 0 : getPosition().hashCode());
        result = prime * result + ((getCertificatesType() == null) ? 0 : getCertificatesType().hashCode());
        result = prime * result + ((getCertificatesNumber() == null) ? 0 : getCertificatesNumber().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getDistrictCode() == null) ? 0 : getDistrictCode().hashCode());
        result = prime * result + ((getDistrictName() == null) ? 0 : getDistrictName().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getSourceKey() == null) ? 0 : getSourceKey().hashCode());
        result = prime * result + ((getSourceTag() == null) ? 0 : getSourceTag().hashCode());
        result = prime * result + ((getRemarks() == null) ? 0 : getRemarks().hashCode());
        result = prime * result + ((getDeleteFlag() == null) ? 0 : getDeleteFlag().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreatorKey() == null) ? 0 : getCreatorKey().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getOperatorKey() == null) ? 0 : getOperatorKey().hashCode());
        result = prime * result + ((getOperator() == null) ? 0 : getOperator().hashCode());
        result = prime * result + ((getLegalPersonFlag() == null) ? 0 : getLegalPersonFlag().hashCode());
        result = prime * result + ((getSigningPersonFlag() == null) ? 0 : getSigningPersonFlag().hashCode());
        result = prime * result + ((getSigningPersonUpdate() == null) ? 0 : getSigningPersonUpdate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", contactPersonId=").append(contactPersonId);
        sb.append(", contactPersonName=").append(contactPersonName);
        sb.append(", bindType=").append(bindType);
        sb.append(", unionId=").append(unionId);
        sb.append(", unionIdBindTime=").append(unionIdBindTime);
        sb.append(", gender=").append(gender);
        sb.append(", department=").append(department);
        sb.append(", position=").append(position);
        sb.append(", certificatesType=").append(certificatesType);
        sb.append(", certificatesNumber=").append(certificatesNumber);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", cityName=").append(cityName);
        sb.append(", districtCode=").append(districtCode);
        sb.append(", districtName=").append(districtName);
        sb.append(", address=").append(address);
        sb.append(", sourceKey=").append(sourceKey);
        sb.append(", sourceTag=").append(sourceTag);
        sb.append(", remarks=").append(remarks);
        sb.append(", deleteFlag=").append(deleteFlag);
        sb.append(", createTime=").append(createTime);
        sb.append(", creatorKey=").append(creatorKey);
        sb.append(", creator=").append(creator);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", operatorKey=").append(operatorKey);
        sb.append(", operator=").append(operator);
        sb.append(", legalPersonFlag=").append(legalPersonFlag);
        sb.append(", signingPersonFlag=").append(signingPersonFlag);
        sb.append(", signingPersonUpdate=").append(signingPersonUpdate);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}