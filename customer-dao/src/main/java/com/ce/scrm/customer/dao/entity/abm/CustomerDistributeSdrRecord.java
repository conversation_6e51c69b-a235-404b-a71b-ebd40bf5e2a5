package com.ce.scrm.customer.dao.entity.abm;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 电话活动下发SDR的记录
 * @TableName customer_distribute_sdr_record
 */
@TableName(value ="customer_distribute_sdr_record")
@Data
public class CustomerDistributeSdrRecord implements Serializable {
    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 员工id
     */
    private String empId;

    /**
     * 员工姓名
     */
    private String empName;

    /**
     * 职位 1-SDR 2-CC
     */
    private Integer positionType;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}