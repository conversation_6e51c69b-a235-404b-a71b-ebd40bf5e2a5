package com.ce.scrm.customer.dao.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CustomerNameMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * customer主键
     */
    private Long customerKey;

    /**
     * customerId
     */
    private String customerId;

    /**
     * pid
     */
    private String pid;

    /**
     * 现有名称
     */
    private String oldCustName;

    /**
     * 大数据名称
     */
    private String newCustName;

    /**
     * 大数据uncid
     */
    private String newUncid;


    public static final String ID = "id";

    public static final String CUSTOMER_KEY = "customer_key";

    public static final String CUSTOMER_ID = "customer_id";

    public static final String PID = "pid";

    public static final String OLD_CUST_NAME = "old_cust_name";

    public static final String NEW_CUST_NAME = "new_cust_name";

    public static final String NEW_UNCID = "new_uncid";

}
