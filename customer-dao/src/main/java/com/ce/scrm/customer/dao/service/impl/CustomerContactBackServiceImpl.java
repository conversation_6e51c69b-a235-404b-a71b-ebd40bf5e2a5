package com.ce.scrm.customer.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.CustomerContactBack;
import com.ce.scrm.customer.dao.service.CustomerContactBackService;
import com.ce.scrm.customer.dao.mapper.CustomerContactBackMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【customer_contact_back(客户与联系人关系备份表)】的数据库操作Service实现
* @createDate 2023-08-24 10:25:27
*/
@Service
public class CustomerContactBackServiceImpl extends ServiceImpl<CustomerContactBackMapper, CustomerContactBack>
    implements CustomerContactBackService{

}




