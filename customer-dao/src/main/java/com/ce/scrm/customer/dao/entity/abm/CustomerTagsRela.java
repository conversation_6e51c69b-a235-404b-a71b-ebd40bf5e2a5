package com.ce.scrm.customer.dao.entity.abm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户与标签关联表
 * @TableName customer_tags_rela
 */
@TableName(value ="customer_tags_rela")
@Data
public class CustomerTagsRela implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户ID（customer.customer_id）
     */
    private String customerId;

    /**
     * 标签code
     */
    private String tagCode;

    /**
     * 操作人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}