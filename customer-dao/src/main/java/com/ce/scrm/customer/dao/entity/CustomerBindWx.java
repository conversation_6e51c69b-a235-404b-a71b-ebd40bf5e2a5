package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * @version 1.0
 * @Description: 客户绑定微信实体
 * @Author: lijinpeng
 * @Date: 2025/2/11 14:52
 */
@TableName(value ="customer_bind_wx")
public class CustomerBindWx implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long bindSourceTableId;

    /**
     * 0:绑定,1:解绑
     */
    private Integer type;

    /**
     * 绑定类型 1:客户,2:联系人
     */
    private Integer bindType;

    /**
     * 企业ID
     */
    private String pid;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 联系人ID
     */
    private String contactPersonId;

    /**
     * 绑定客户类型 1:微信用户,2:企业微信用户
     */
    private Integer bindUserType;

    /**
     * bind_user_type=1:微信unionId
     */
    private String unionid;

    /**
     * bind_user_type=1:微信名称
     */
    private String wxName;

    /**
     * bind_user_type=2:企业微信外部联系人id
     */
    private String externalUserid;

    /**
     * 区域ID
     */
    private String areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 分公司ID
     */
    private String subId;

    /**
     * 分公司名称
     */
    private String subName;

    /**
     * BU 部门ID
     */
    private String buId;

    /**
     * BU 名称
     */
    private String buName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 0:否,1:是 保护商务和操作人是否同一人
     */
    private Integer isSameSalerWhenOperation;

    /**
     * 0:否,1:是 操作时是否为保有客户
     */
    private Integer isTradeWhenOperate;

    /**
     * 0:否,1:是 操作时是否保护中
     */
    private Integer isProtectedWhenOperate;

    /**
     * 操作人ID
     */
    private String createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 表记录写入时间
     */
    private Date dbInsertTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getBindType() {
        return bindType;
    }

    public void setBindType(Integer bindType) {
        this.bindType = bindType;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getContactPersonId() {
        return contactPersonId;
    }

    public void setContactPersonId(String contactPersonId) {
        this.contactPersonId = contactPersonId;
    }

    public Integer getBindUserType() {
        return bindUserType;
    }

    public void setBindUserType(Integer bindUserType) {
        this.bindUserType = bindUserType;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getWxName() {
        return wxName;
    }

    public void setWxName(String wxName) {
        this.wxName = wxName;
    }

    public String getExternalUserid() {
        return externalUserid;
    }

    public void setExternalUserid(String externalUserid) {
        this.externalUserid = externalUserid;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getBuId() {
        return buId;
    }

    public void setBuId(String buId) {
        this.buId = buId;
    }

    public String getBuName() {
        return buName;
    }

    public void setBuName(String buName) {
        this.buName = buName;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Integer getIsSameSalerWhenOperation() {
        return isSameSalerWhenOperation;
    }

    public void setIsSameSalerWhenOperation(Integer isSameSalerWhenOperation) {
        this.isSameSalerWhenOperation = isSameSalerWhenOperation;
    }

    public Integer getIsTradeWhenOperate() {
        return isTradeWhenOperate;
    }

    public void setIsTradeWhenOperate(Integer isTradeWhenOperate) {
        this.isTradeWhenOperate = isTradeWhenOperate;
    }

    public Integer getIsProtectedWhenOperate() {
        return isProtectedWhenOperate;
    }

    public void setIsProtectedWhenOperate(Integer isProtectedWhenOperate) {
        this.isProtectedWhenOperate = isProtectedWhenOperate;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDbInsertTime() {
        return dbInsertTime;
    }

    public void setDbInsertTime(Date dbInsertTime) {
        this.dbInsertTime = dbInsertTime;
    }

    public Long getBindSourceTableId() {
        return bindSourceTableId;
    }

    public void setBindSourceTableId(Long bindSourceTableId) {
        this.bindSourceTableId = bindSourceTableId;
    }
}
