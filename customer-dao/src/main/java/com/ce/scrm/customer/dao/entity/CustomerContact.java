package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 客户与联系人关系表
 * @TableName customer_contact
 */
@TableName(value ="customer_contact")
@Data
public class CustomerContact implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户id
     */
    @TableField(value = "customer_id")
    private String customerId;

    /**
     * 会员code，用于会员中心数据查询兼容
     */
    @TableField(value = "member_code")
    private String memberCode;

    /**
     * 联系人id
     */
    @TableField(value = "contact_person_id")
    private String contactPersonId;

    /**
     * 删除标记：1、删除，0、未删除
     */
    @TableField(value = "delete_flag")
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建者凭证
     */
    @TableField(value = "creator_key")
    private String creatorKey;

    /**
     * 创建者
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人凭证
     */
    @TableField(value = "operator_key")
    private String operatorKey;

    /**
     * 更新人
     */
    @TableField(value = "operator")
    private String operator;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}