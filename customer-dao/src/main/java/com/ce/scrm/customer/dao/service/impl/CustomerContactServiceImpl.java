package com.ce.scrm.customer.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.CustomerContact;
import com.ce.scrm.customer.dao.service.CustomerContactService;
import com.ce.scrm.customer.dao.mapper.CustomerContactMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【customer_contact(客户与联系人关系表)】的数据库操作Service实现
* @createDate 2023-05-05 11:16:08
*/
@Service
public class CustomerContactServiceImpl extends ServiceImpl<CustomerContactMapper, CustomerContact>
    implements CustomerContactService{

}




