package com.ce.scrm.customer.dao.service.impl.abm;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.abm.CustomerTagsCategory;
import com.ce.scrm.customer.dao.service.abm.CustomerTagsCategoryService;
import com.ce.scrm.customer.dao.mapper.abm.CustomerTagsCategoryMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【customer_tags_category(客户标签分类表)】的数据库操作Service实现
* @createDate 2025-07-09 14:14:46
*/
@Service
public class CustomerTagsCategoryServiceImpl extends ServiceImpl<CustomerTagsCategoryMapper, CustomerTagsCategory> implements CustomerTagsCategoryService{

}




