package com.ce.scrm.customer.dao.service.impl.abm;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.CustomerLeads;
import com.ce.scrm.customer.dao.service.abm.CustomerLeadsService;
import com.ce.scrm.customer.dao.mapper.abm.CustomerLeadsMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【customer_leads(客户leads)】的数据库操作Service实现
* @createDate 2025-07-17 09:32:04
*/
@Service
public class CustomerLeadsServiceImpl extends ServiceImpl<CustomerLeadsMapper, CustomerLeads>
    implements CustomerLeadsService{

}




