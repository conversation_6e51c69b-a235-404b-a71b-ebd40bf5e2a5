package com.ce.scrm.customer.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 联系途径备份表
 * @TableName contact_info_back
 */
@TableName(value ="contact_info_back")
@Data
public class ContactInfoBack implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 联系方式ID
     */
    private String contactInfoId;

    /**
     * 联系人ID
     */
    private String contactPersonId;

    /**
     * 联系类型：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
     */
    private Integer contactType;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 手机号标识：1、正常，2、黑名单，3、空号
     */
    private Integer phoneFlag;

    /**
     * 联系方式来源key
     */
    private String sourceKey;

    /**
     * 是否是签单人手机号：1、是，0、否（已废弃）
     */
    private Integer signatoryFlag;

    /**
     * 是否是300会员手机号：1、是，0、否（已废弃）
     */
    private Integer memberFlag;

    /**
     * 备注（已废弃）
     */
    private String remark;

    /**
     * 删除标记：1、删除，0、未删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者凭证（已废弃）
     */
    private String creatorKey;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人凭证（已废弃）
     */
    private String operatorKey;

    /**
     * 更新人
     */
    private String operator;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ContactInfoBack other = (ContactInfoBack) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getContactInfoId() == null ? other.getContactInfoId() == null : this.getContactInfoId().equals(other.getContactInfoId()))
            && (this.getContactPersonId() == null ? other.getContactPersonId() == null : this.getContactPersonId().equals(other.getContactPersonId()))
            && (this.getContactType() == null ? other.getContactType() == null : this.getContactType().equals(other.getContactType()))
            && (this.getContactWay() == null ? other.getContactWay() == null : this.getContactWay().equals(other.getContactWay()))
            && (this.getPhoneFlag() == null ? other.getPhoneFlag() == null : this.getPhoneFlag().equals(other.getPhoneFlag()))
            && (this.getSourceKey() == null ? other.getSourceKey() == null : this.getSourceKey().equals(other.getSourceKey()))
            && (this.getSignatoryFlag() == null ? other.getSignatoryFlag() == null : this.getSignatoryFlag().equals(other.getSignatoryFlag()))
            && (this.getMemberFlag() == null ? other.getMemberFlag() == null : this.getMemberFlag().equals(other.getMemberFlag()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getDeleteFlag() == null ? other.getDeleteFlag() == null : this.getDeleteFlag().equals(other.getDeleteFlag()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getCreatorKey() == null ? other.getCreatorKey() == null : this.getCreatorKey().equals(other.getCreatorKey()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getOperatorKey() == null ? other.getOperatorKey() == null : this.getOperatorKey().equals(other.getOperatorKey()))
            && (this.getOperator() == null ? other.getOperator() == null : this.getOperator().equals(other.getOperator()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getContactInfoId() == null) ? 0 : getContactInfoId().hashCode());
        result = prime * result + ((getContactPersonId() == null) ? 0 : getContactPersonId().hashCode());
        result = prime * result + ((getContactType() == null) ? 0 : getContactType().hashCode());
        result = prime * result + ((getContactWay() == null) ? 0 : getContactWay().hashCode());
        result = prime * result + ((getPhoneFlag() == null) ? 0 : getPhoneFlag().hashCode());
        result = prime * result + ((getSourceKey() == null) ? 0 : getSourceKey().hashCode());
        result = prime * result + ((getSignatoryFlag() == null) ? 0 : getSignatoryFlag().hashCode());
        result = prime * result + ((getMemberFlag() == null) ? 0 : getMemberFlag().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getDeleteFlag() == null) ? 0 : getDeleteFlag().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreatorKey() == null) ? 0 : getCreatorKey().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getOperatorKey() == null) ? 0 : getOperatorKey().hashCode());
        result = prime * result + ((getOperator() == null) ? 0 : getOperator().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", contactInfoId=").append(contactInfoId);
        sb.append(", contactPersonId=").append(contactPersonId);
        sb.append(", contactType=").append(contactType);
        sb.append(", contactWay=").append(contactWay);
        sb.append(", phoneFlag=").append(phoneFlag);
        sb.append(", sourceKey=").append(sourceKey);
        sb.append(", signatoryFlag=").append(signatoryFlag);
        sb.append(", memberFlag=").append(memberFlag);
        sb.append(", remark=").append(remark);
        sb.append(", deleteFlag=").append(deleteFlag);
        sb.append(", createTime=").append(createTime);
        sb.append(", creatorKey=").append(creatorKey);
        sb.append(", creator=").append(creator);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", operatorKey=").append(operatorKey);
        sb.append(", operator=").append(operator);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}