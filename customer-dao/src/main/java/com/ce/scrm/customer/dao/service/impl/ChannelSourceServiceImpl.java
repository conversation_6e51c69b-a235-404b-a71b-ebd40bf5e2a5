package com.ce.scrm.customer.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.ChannelSource;
import com.ce.scrm.customer.dao.service.ChannelSourceService;
import com.ce.scrm.customer.dao.mapper.ChannelSourceMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【channel_source(渠道来源表)】的数据库操作Service实现
* @createDate 2023-05-05 11:16:08
*/
@Service
public class ChannelSourceServiceImpl extends ServiceImpl<ChannelSourceMapper, ChannelSource>
    implements ChannelSourceService{

}




