package com.ce.scrm.customer.dao.entity.abm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * sdr推送销售审核表
 * @TableName sdr_push_sale_review
 */
@TableName(value ="sdr_push_sale_review")
@Data
public class SdrPushSaleReview implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 地址
     */
    private String address;

    /**
     * 跟进详情
     */
    private String followDetails;

    /**
     * 审核状态 0:待审核 1:审核通过
     */
    private Integer reviewStatus;

    /**
     * 创建者
     */
    private String createdId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 审核人id
     */
    private String reviewId;

    /**
     * 审核时间
     */
    private Date reviewTime;
}