package com.ce.scrm.customer.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.ImportContactPerson;
import com.ce.scrm.customer.dao.service.ImportContactPersonService;
import com.ce.scrm.customer.dao.mapper.ImportContactPersonMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【import_contact_person】的数据库操作Service实现
* @createDate 2023-05-24 15:38:59
*/
@Service
public class ImportContactPersonServiceImpl extends ServiceImpl<ImportContactPersonMapper, ImportContactPerson>
    implements ImportContactPersonService{

}




