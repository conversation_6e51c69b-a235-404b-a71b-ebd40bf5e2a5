package com.ce.scrm.customer.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.ContactInfo;
import com.ce.scrm.customer.dao.service.ContactInfoService;
import com.ce.scrm.customer.dao.mapper.ContactInfoMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【contact_info(联系途径)】的数据库操作Service实现
* @createDate 2023-04-17 16:07:05
*/
@Service
public class ContactInfoServiceImpl extends ServiceImpl<ContactInfoMapper, ContactInfo>
    implements ContactInfoService{

}




