package com.ce.scrm.customer.dao.service.impl.abm;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ce.scrm.customer.dao.entity.abm.CustomerTagWeightHistory;
import com.ce.scrm.customer.dao.service.abm.CustomerTagWeightHistoryService;
import com.ce.scrm.customer.dao.mapper.abm.CustomerTagWeightHistoryMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【customer_tag_weight_history(客户标签积分变更历史表)】的数据库操作Service实现
* @createDate 2025-07-09 14:14:46
*/
@Service
public class CustomerTagWeightHistoryServiceImpl extends ServiceImpl<CustomerTagWeightHistoryMapper, CustomerTagWeightHistory> implements CustomerTagWeightHistoryService{

}




