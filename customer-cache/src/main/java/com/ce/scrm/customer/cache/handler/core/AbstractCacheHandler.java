package com.ce.scrm.customer.cache.handler.core;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.cache.access.BaseManager;
import com.ce.scrm.customer.cache.constant.CacheConstant;
import com.ce.scrm.customer.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.customer.cache.enumeration.CacheVersionEnum;
import com.ce.scrm.customer.cache.lock.Lock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 缓存处理模板类
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
public abstract class AbstractCacheHandler<T, V, R> extends BaseManager {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(AbstractCacheHandler.class);

    /**
     * 删除缓存
     * @param customerKey   自定义缓存key
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public final void del(String customerKey) {
        delCache(getKey(customerKey));
    }

    /**
     * 批量删除缓存
     * @param customerKeyList   自定义缓存key列表
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public final void batchDel(List<String> customerKeyList) {
        batchDelCache(customerKeyList);
    }

    /**
     * 获取缓存数据
     * @param customerKey   自定义key
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return R
     **/
    public final R get(String customerKey) {
        return getAndSet(customerKey);
    }

    /**
     * 获取redis的key
     * @param customerKey   自定义key
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.lang.String
     **/
    public final String getKey(String customerKey) {
        return CacheConstant.CACHE_PREFIX + CacheConstant.CACHE_KEY_SEPARATOR + packageCacheKey(customerKey) + CacheConstant.CACHE_KEY_SEPARATOR + getVersion().getNumber();
    }

    /**
     * 根据缓存key获取自定义key
     * @param cacheKey  缓存key
     * <AUTHOR>
     * @date 2023/5/17 13:17
     * @return java.lang.String
     **/
    public final String getCustomerKey(String cacheKey) {
        return cacheKey.split(CacheConstant.CACHE_PREFIX + CacheConstant.CACHE_KEY_SEPARATOR + getCacheKey() + CacheConstant.CACHE_KEY_SEPARATOR)[1].split(CacheConstant.CACHE_KEY_SEPARATOR)[0];
    }

    /**
     * 获取缓存并设置
     * 如果缓存无数据则设置缓存
     *
     * @param customerKey   自定义缓存key
     * <AUTHOR>
     * @date 2023/4/6 17:19
     * @return R
     **/
    private R getAndSet(String customerKey) {
        String cacheKey = getKey(customerKey);
        if (StrUtil.isBlank(cacheKey)) {
            return null;
        }
        //获取缓存
        V returnValue = getCache(cacheKey);
        if (!isEmpty(returnValue)) {
            //校验是否为设置的空值
            return checkNullData(returnValue) ? null : parseCacheValue(returnValue);
        }
        //从数据源获取数据并设置到缓存
        Lock lock = null;
        try {
            //加入分布式锁，防止缓存击穿
            if (!(lock = getLock()).lock(cacheKey)) {
                LOGGER.error("获取分布式锁异常，当前使用的分布式锁为:{}", lock.getName());
                return null;
            }
            returnValue = getCache(cacheKey);
            if (!isEmpty(returnValue)) {
                //校验是否为设置的空值
                return checkNullData(returnValue) ? null : parseCacheValue(returnValue);
            }
            return setCache(customerKey, getExpire());
        } finally {
            if (lock != null) {
                lock.unLock(cacheKey);
            }
        }
    }

    /**
     * 查询缓存数据
     * @param cacheKey  缓存key
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return V
     **/
    private V getCache(String cacheKey) {
        return getCacheValue(cacheKey);
    }

    /**
     * 获取缓存数据
     * @param cacheKey  缓存key
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return V
     **/
    protected abstract V getCacheValue(String cacheKey);

    /**
     * 数据存入缓存
     * @param customerKey   自定义缓存key
     * @param expire    有效时间
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return R
     **/
    private R setCache(String customerKey, long expire) {
        String cacheKey = getKey(customerKey);
        R customerValue = queryDataBySource(customerKey);
        V cacheValue;
        if (ObjectUtil.isEmpty(customerValue)) {
            cacheValue = getNullData();
            expire = getNullDataExpire();
        } else {
            cacheValue = packageCacheValue(customerValue);
        }
        setCacheData(cacheKey, cacheValue, expire);
        return customerValue;
    }

    /**
     * 设置缓存数据
     * @param cacheKey  缓存key
     * @param cacheValue    缓存值
     * @param expire    缓存有效期
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    protected abstract void setCacheData(String cacheKey, V cacheValue, long expire);

    /**
     * 检查是否为空数据
     * @param returnValue  缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return boolean
     **/
    protected abstract boolean checkNullData(V returnValue);

    /**
     * 获取空数据
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return V
     **/
    protected abstract V getNullData();

    /**
     * 缓存数据判空
     * @param v 缓存数据
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return boolean
     **/
    protected abstract boolean isEmpty(V v);

    /**
     * 获取空数据有效期
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return long
     **/
    protected long getNullDataExpire() {
        return CacheConstant.CacheExpire.FIVE_MINUTES;
    }

    /**
     * 获取当前缓存的版本号
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return com.ce.scrm.customer.cache.enumeration.CacheVersionEnum
     **/
    protected CacheVersionEnum getVersion() {
        return CacheVersionEnum.INIT_VERSION;
    }

    /**
     * 封装缓存key
     *
     * @param customerKey 自定义传入的key
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return java.lang.String
     **/
    protected String packageCacheKey(String customerKey) {
        return getCacheKey() + CacheConstant.CACHE_KEY_SEPARATOR + customerKey;
    }

    /**
     * 获取业务缓存的key
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return com.ce.scrm.customer.cache.enumeration.CacheKeyEnum
     **/
    public abstract CacheKeyEnum getCacheKey();

    /**
     * 封装缓存value
     *
     * @param customerValue 自定义传入的value
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return V
     **/
    protected abstract V packageCacheValue(R customerValue);

    /**
     * 解析缓存数据
     *
     * @param cacheValue 缓存数据
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return R
     **/
    protected abstract R parseCacheValue(V cacheValue);

    /**
     * 设置缓存失效时间
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return long
     **/
    protected long getExpire() {
        return CacheConstant.CacheExpire.THREE_DAYS;
    }

    /**
     * 对象转String
     * @param objectData 对象数据
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return java.lang.String
     **/
    protected String objectToString(T objectData) {
        return JSON.toJSONString(objectData);
    }

    /**
     * String还原对象
     * @param cacheValue    缓存数据条目
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return T
     **/
    protected T stringToObject(String cacheValue) {
        return JSON.parseObject(cacheValue, getClazz());
    }

    /**
     * 指定对象类型
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return java.lang.Class<T>
     **/
    protected abstract Class<T> getClazz();

    /**
     * 从数据源查询数据
     *
     * @param customerKey 自定义key
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return R
     **/
    protected abstract R queryDataBySource(String customerKey);

    /**
     * 删除缓存数据
     *
     * @param cacheKey 缓存key
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    private void delCache(String cacheKey) {
        getCache().del(cacheKey);
    }

    /**
     * 批量删除缓存数据
     *
     * @param cacheKeyList 缓存key列表
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    private void batchDelCache(List<String> cacheKeyList) {
        getCache().batchDel(cacheKeyList);
    }
}
