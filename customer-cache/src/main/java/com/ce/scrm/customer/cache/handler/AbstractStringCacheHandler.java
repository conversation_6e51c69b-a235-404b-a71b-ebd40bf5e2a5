package com.ce.scrm.customer.cache.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.ce.scrm.customer.cache.constant.CacheConstant;
import com.ce.scrm.customer.cache.handler.core.AbstractCacheHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 字符串缓存处理模板类
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
public abstract class AbstractStringCacheHandler<T> extends AbstractCacheHandler<T, String, T> {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(AbstractStringCacheHandler.class);

    /**
     * 获取缓存数据
     * @param cacheKey  缓存key
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return String
     **/
    @Override
    protected String getCacheValue(String cacheKey) {
        return getCache().get(cacheKey);
    }


    /**
     * 批量删除缓存数据
     *
     * @param cacheKeyList 缓存key列表
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    public List<T> batchGet(List<String> cacheKeyList) {
        List<String> cacheList = getCache().batchGet(cacheKeyList);
        for (int i = 0; i < cacheKeyList.size(); i++) {
            String cacheValue = cacheList.get(i);
            if (StrUtil.isBlank(cacheValue) || CacheConstant.CacheNullData.STRING.equals(cacheValue)) {
                T t = get(getCustomerKey(cacheKeyList.get(i)));
                if (t != null) {
                    cacheList.set(i, packageCacheValue(t));
                }
            }
        }
        if (CollectionUtil.isEmpty(cacheList)) {
            return null;
        }
        return cacheList.stream().map(this::parseCacheValue).collect(Collectors.toList());
    }

    /**
     * 设置缓存数据
     * @param cacheKey  缓存key
     * @param cacheValue    缓存值
     * @param expire    缓存有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected void setCacheData(String cacheKey, String cacheValue, long expire) {
        getCache().set(cacheKey, cacheValue, expire);
    }

    /**
     * 检查是否为空数据
     * @param returnValue  返回数据
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    @Override
    protected boolean checkNullData(String returnValue) {
        return getNullData().equals(returnValue);
    }

    /**
     * 设置缓存默认数据
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return String
     **/
    @Override
    protected String getNullData() {
        return CacheConstant.CacheNullData.STRING;
    }

    /**
     * 缓存数据判空
     * @param data 缓存数据
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    @Override
    protected boolean isEmpty(String data) {
        return StrUtil.isBlank(data);
    }

    /**
     * 封装缓存value
     *
     * @param customerValue 自定义传入的value
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return String
     **/
    @Override
    protected String packageCacheValue(T customerValue) {
        return objectToString(customerValue);
    }

    /**
     * 解析缓存数据
     *
     * @param cacheValue 缓存数据
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return T
     **/
    @Override
    protected T parseCacheValue(String cacheValue) {
        return stringToObject(cacheValue);
    }
}
