package com.ce.scrm.customer.cache.enumeration;

/**
 * 缓存key枚举类
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/05/14 上午9:43
 */
public enum CacheKeyEnum {
    /**
     * demo
     */
    DEMO,
    /**
     * redis 心跳
     */
    REDIS_HEARTBEAT,

    /**
     * 任务处理器
     */
    JOB_HANDLER_CACHE,
    /**
     * 渠道来源
     */
    CHANNEL_SOURCE,

    /**
     * 客户
     */
    CUSTOMER,

    /**
     * 联系人
     */
    CONTACT_PERSON,

    /**
     * 客户联系人
     */
    CUSTOMER_CONTACT,

    /**
     * 行业
     */
    INDUSTRY,

    /**
     * 地域
     */
    AREA,
    /**
     * 根据地区code获取地区
     */
    AREA_BY_CODE,
    /**
     * rocket topic
     */
    ROCKET_TOPIC;
}
