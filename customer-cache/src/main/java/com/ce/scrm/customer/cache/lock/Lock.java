package com.ce.scrm.customer.cache.lock;

/**
 * 对锁的操作接口
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
public interface Lock {
    /**
     * 获取当前操作锁方式的名称
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return java.lang.String
     **/
    String getName();

    /**
     * 获取锁
     * @param key 锁的key
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    boolean lock(String key);

    /**
     * 获取锁
     * @param key 锁的key
     * @param waitTime 加锁等待时间
     * @param timeoutTime 锁超时时间
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    boolean tryLock(String key, long waitTime, long timeoutTime);

    /**
     * 释放锁
     * @param key 锁的key
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    boolean unLock(String key);
}
