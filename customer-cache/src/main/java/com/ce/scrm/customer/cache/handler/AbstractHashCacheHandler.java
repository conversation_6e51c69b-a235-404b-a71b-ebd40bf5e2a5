package com.ce.scrm.customer.cache.handler;

import cn.hutool.core.map.MapUtil;
import com.ce.scrm.customer.cache.constant.CacheConstant;
import com.ce.scrm.customer.cache.handler.core.AbstractCacheHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * hash缓存处理模板类
 * <AUTHOR>
 * @date 2023/4/6 17:18
 * @version 1.0.0
 */
public abstract class AbstractHashCacheHandler<T> extends AbstractCacheHandler<T, Map<String, String>, Map<String, T>> {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(AbstractHashCacheHandler.class);

    /**
     * 获取缓存数据
     * @param cacheKey  缓存key
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return Map<String, String>
     **/
    @Override
    protected Map<String, String> getCacheValue(String cacheKey) {
        return getCache().hGet(cacheKey);
    }

    /**
     * 设置缓存数据
     * @param cacheKey  缓存key
     * @param cacheValue    缓存值
     * @param expire    缓存有效期
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    @Override
    protected void setCacheData(String cacheKey, Map<String, String> cacheValue, long expire) {
        getCache().hSet(cacheKey, cacheValue, expire);
    }

    /**
     * 检查是否为空数据
     * @param cacheValue  缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    @Override
    protected boolean checkNullData(Map<String, String> cacheValue) {
        return getNullData().equals(cacheValue);
    }

    /**
     * 缓存数据判空
     * @param dataMap 缓存数据
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return boolean
     **/
    @Override
    protected boolean isEmpty(Map<String, String> dataMap) {
        return MapUtil.isEmpty(dataMap);
    }

    /**
     * 设置缓存默认数据
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return Map<String, String>
     **/
    @Override
    protected Map<String, String> getNullData() {
        return CacheConstant.CacheNullData.HASH;
    }

    /**
     * 封装缓存value
     *
     * @param customerValue 自定义传入的value
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return V
     **/
    @Override
    protected Map<String, String> packageCacheValue(Map<String, T> customerValue) {
        return customerValue.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> objectToString(entry.getValue())));
    }

    /**
     * 解析缓存数据
     *
     * @param cacheValue 缓存数据
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return R
     **/
    @Override
    protected Map<String, T> parseCacheValue(Map<String, String> cacheValue) {
        return cacheValue.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> stringToObject(entry.getValue())));
    }

    /**
     * 设置缓存数据
     * @param customerKey  自定义key
     * @param hashKey  hashKey
     * @param cacheValueItem  缓存值
     * <AUTHOR>
     * @date 2023/4/6 17:18
     **/
    public final void setCacheItem(String customerKey, String hashKey, T cacheValueItem) {
        getCache().hSetItem(getKey(customerKey), hashKey, objectToString(cacheValueItem));
    }

    /**
     * 获取单个缓存数据
     * @param customerKey  自定义key
     * @param hashKey  hashKey
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return T
     **/
    public final T getCacheItem(String customerKey, String hashKey) {
        return stringToObject(getCache().hGetItem(getKey(customerKey), hashKey));
    }
}
