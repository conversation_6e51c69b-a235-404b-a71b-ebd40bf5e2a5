package com.ce.scrm.customer.cache.handler.manager;

import com.ce.scrm.customer.cache.handler.core.AbstractCacheHandler;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存处理器的管理类
 * <AUTHOR>
 * @date 2023/8/21 11:06
 * @version 1.0.0
 **/
@Slf4j
@Component
@SuppressWarnings("all")
public class CacheHandlerManager implements ApplicationContextAware {
    /**
     * 应用上下文
     */
    private ApplicationContext applicationContext;


    private final Map<String, AbstractCacheHandler> allCacheHandler = new HashMap<>();

    @PostConstruct
    public void init() {
        Map<String, AbstractCacheHandler> cacheHandlerMap = applicationContext.getBeansOfType(AbstractCacheHandler.class);
        for (Map.Entry<String, AbstractCacheHandler> entry : cacheHandlerMap.entrySet()) {
            allCacheHandler.put(entry.getValue().getCacheKey().name(), entry.getValue());
        }
        if (allCacheHandler.isEmpty()) {
            log.warn("当前未获取到正在使用的缓存处理器");
        }
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 根据缓存处理器枚举名称获取特定类型的缓存处理器
     * @param cacheHandlerName  缓存处理器名称
     * <AUTHOR>
     * @date 2023/4/6 17:18
     * @return com.ce.scrm.customer.cache.handler.Cache
     **/
    public AbstractCacheHandler getCacheHandler(String cacheHandlerName) {
        return allCacheHandler.get(cacheHandlerName);
    }
}
