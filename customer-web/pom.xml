<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>scrm-customer</artifactId>
        <groupId>com.ce.scrm.customer</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>customer-web</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>customer-web</name>
    <description>Demo project for Spring Boot</description>

    <properties>
        <jvm.min>1024m</jvm.min>
        <jvm.max>2048m</jvm.max>
        <jvm.MaxDirectMemorySize>128m</jvm.MaxDirectMemorySize>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm.customer</groupId>
            <artifactId>customer-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm.customer</groupId>
            <artifactId>customer-dubbo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm.customer</groupId>
            <artifactId>customer-dubbo-api7</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm.extend</groupId>
            <artifactId>extend-dubbo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency><dependency>
        <groupId>com.github.sgroschupf</groupId>
        <artifactId>zkclient</artifactId>
    </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-achievement-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>test</id>
            <properties>
                <jvm.min>1024m</jvm.min>
                <jvm.max>1024m</jvm.max>
                <jvm.MaxDirectMemorySize>512m</jvm.MaxDirectMemorySize>
            </properties>
        </profile>
        <profile>
            <id>test2</id>
            <properties>
                <jvm.min>1024m</jvm.min>
                <jvm.max>1024m</jvm.max>
                <jvm.MaxDirectMemorySize>512m</jvm.MaxDirectMemorySize>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <jvm.min>1024m</jvm.min>
                <jvm.max>1024m</jvm.max>
                <jvm.MaxDirectMemorySize>512m</jvm.MaxDirectMemorySize>
            </properties>
        </profile>
        <profile>
            <id>release</id>
            <properties>
                <jvm.min>1024m</jvm.min>
                <jvm.max>2048m</jvm.max>
                <jvm.MaxDirectMemorySize>128m</jvm.MaxDirectMemorySize>
            </properties>
        </profile>
    </profiles>


    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <configuration>
                            <finalName>${project.build.finalName}</finalName>
                            <appendAssemblyId>false</appendAssemblyId>
                            <descriptors>${project.basedir}/src/main/assembly/assembly.xml</descriptors>
                        </configuration>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
