package com.ce.scrm.customer.web;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.dubbo.api7.IContactPersonDubbo;
import com.ce.scrm.customer.dubbo.api7.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.entity7.dto.CustomerDetailDubboDto;
import com.ce.scrm.customer.dubbo.entity7.dto.CustomerPageDubboDto;
import com.ce.scrm.customer.dubbo.entity7.dto.OrderDefaultFlagDubboDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest
class ScrmCustomerWebApplicationTests {

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private IContactPersonDubbo contactPersonDubbo;

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private ICustomerDubbo customerDubbo;

    @Test
    void get() {
        //获取客户下联系人数据
        CustomerDetailDubboDto customerDetailDubboDto = new CustomerDetailDubboDto();
        customerDetailDubboDto.setSourceKey("scrm");
        customerDetailDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        customerDetailDubboDto.setCustomerId("1KHWN2ZaJ0gqpw7wuc$hXE");
        log.info(JSON.toJSONString(contactPersonDubbo.getCustomerData(customerDetailDubboDto)));
    }

    @Test
    void getCustomer() {
        //获取客户下联系人数据
        CustomerPageDubboDto customerPageDubboDto = new CustomerPageDubboDto();
        customerPageDubboDto.setSourceKey("scrm");
        customerPageDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        customerPageDubboDto.setCustomerName("中企宝丝");
        log.info(JSON.toJSONString(customerDubbo.pageList(customerPageDubboDto)));
    }

    @Test
    void addDefaultFlag() {
        //获取客户下联系人数据
        OrderDefaultFlagDubboDto orderDefaultFlagDubboDto = new OrderDefaultFlagDubboDto();
        orderDefaultFlagDubboDto.setSourceKey("scrm");
        orderDefaultFlagDubboDto.setSourceSecret("31b9763b659c49489d348c2e6a261a64");
        orderDefaultFlagDubboDto.setContactPersonId("1660852593770889318");
        orderDefaultFlagDubboDto.setPhone("18819999999");
        orderDefaultFlagDubboDto.setEmail("<EMAIL>");
        orderDefaultFlagDubboDto.setOperator("10429");
        log.info(JSON.toJSONString(contactPersonDubbo.addDefaultFlag(orderDefaultFlagDubboDto)));
    }
}
