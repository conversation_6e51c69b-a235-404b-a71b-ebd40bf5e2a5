package com.ce.scrm.customer.web.entity.view;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 联系人数据
 * <AUTHOR>
 * @date 2023/4/7 17:31
 * @version 1.0.0
 */
@Data
public class ThirdContactPersonWebView {
    /**
     * 联系人id
     */
    private String contactPersonId;
    /**
     * 联系人姓名
     */
    private String contactPersonName;
    /**
     * 性别：1、未知，2、男，3、女
     */
    private Integer gender;
    /**
     * 职位
     */
    private String position;
    /**
     * 联系人部门
     */
    private String department;
    /**
     * 证件类型
     */
    private Integer certificatesType;
    /**
     * 证件号码
     */
    private String certificatesNumber;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区编码
     */
    private String districtCode;
    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系人来源key
     */
    private String sourceKey;

    /**
     * 证件号码
     */
    private String remarks;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 手机
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 微信
     */
    private String wechat;

    /**
     * 企业微信
     */
    private String wecome;

    /**
     * qq
     */
    private String qq;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 默认标记：1、默认，0、不默认
     */
    private Integer defaultFlag;
}