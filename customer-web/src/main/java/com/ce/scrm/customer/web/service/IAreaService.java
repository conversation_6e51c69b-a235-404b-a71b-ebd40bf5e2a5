package com.ce.scrm.customer.web.service;

import com.ce.scrm.customer.web.entity.response.WebResult;
import com.ce.scrm.customer.web.entity.view.AreaWebView;

import java.util.List;

/**
 * 地区业务接口
 * <AUTHOR>
 * @date 2023/4/8 14:45
 * @version 1.0.0
 */
public interface IAreaService {
    /**
     * 获取所有区域数据
     * <AUTHOR>
     * @date 2023/5/16 17:34
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.AreaWebView>>
     **/
    WebResult<List<AreaWebView>> getAllData();

    /**
     * 获取所有区域数据
     * @param sourceKey 来源key
     * @param sourceSecret 来源密钥
     * <AUTHOR>
     * @date 2023/5/16 17:34
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.AreaWebView>>
     **/
    WebResult<List<AreaWebView>> getAllData(String sourceKey, String sourceSecret);
}