package com.ce.scrm.customer.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.dubbo.api.IContactPersonDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.ContactPersonAddDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.ContactPersonDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.ContactPersonUpdateDubboDto;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerDetailDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.ContactPersonAddDubboView;
import com.ce.scrm.customer.dubbo.entity.view.ContactPersonDataDubboView;
import com.ce.scrm.customer.web.entity.dto.ThirdContactPersonAddWebDto;
import com.ce.scrm.customer.web.entity.dto.ThirdContactPersonQueryWebDto;
import com.ce.scrm.customer.web.entity.dto.ThirdContactPersonUpdateWebDto;
import com.ce.scrm.customer.web.entity.dto.ThirdCustomerQueryWebDto;
import com.ce.scrm.customer.web.entity.response.WebResult;
import com.ce.scrm.customer.web.entity.view.ThirdContactPersonAddWebView;
import com.ce.scrm.customer.web.entity.view.ThirdContactPersonWebView;
import com.ce.scrm.customer.web.service.IThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 三方调用业务实现
 * <AUTHOR>
 * @date 2023/4/8 14:47
 * @version 1.0.0
 */
@Slf4j
@Service
public class ThirdServiceImpl implements IThirdService {

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private IContactPersonDubbo contactPersonDubbo;


    /**
     * 获取客户下联系人数据
     *
     * @param thirdCustomerQueryWebDto 查询参数
     * <AUTHOR>
     * @date 2023/5/16 16:57
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.ContactPersonWebView>>
     **/
    @Override
    public WebResult<List<ThirdContactPersonWebView>> getCustomerContactPersonData(ThirdCustomerQueryWebDto thirdCustomerQueryWebDto) {
        CustomerDetailDubboDto customerDetailDubboDto = BeanUtil.copyProperties(thirdCustomerQueryWebDto, CustomerDetailDubboDto.class);
        DubboResult<List<ContactPersonDataDubboView>> contactPersonDubboCustomerData = contactPersonDubbo.getCustomerData(customerDetailDubboDto);
        if (!contactPersonDubboCustomerData.checkSuccess()) {
            log.warn("调用获取客户下联系人数据dubbo失败，返回值为:{}", JSON.toJSONString(contactPersonDubboCustomerData));
            return WebResult.error(contactPersonDubboCustomerData.getCode(), contactPersonDubboCustomerData.getMsg());
        }
        List<ContactPersonDataDubboView> contactPersonDataDubboViewList = contactPersonDubboCustomerData.getData();
        return WebResult.success(BeanUtil.copyToList(contactPersonDataDubboViewList, ThirdContactPersonWebView.class));
    }

    /**
     * 获取联系人数据
     *
     * @param thirdContactPersonQueryWebDto 查询参数
     * <AUTHOR>
     * @date 2023/5/16 16:57
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.ContactPersonWebView>>
     **/
    @Override
    public WebResult<List<ThirdContactPersonWebView>> getContactPersonData(ThirdContactPersonQueryWebDto thirdContactPersonQueryWebDto) {
        ContactPersonDubboDto contactPersonDubboDto = BeanUtil.copyProperties(thirdContactPersonQueryWebDto, ContactPersonDubboDto.class);
        DubboResult<List<ContactPersonDataDubboView>> contactPersonDubboCustomerData = contactPersonDubbo.getData(contactPersonDubboDto);
        if (!contactPersonDubboCustomerData.checkSuccess()) {
            log.warn("调用获取联系人数据dubbo失败，返回值为:{}", JSON.toJSONString(contactPersonDubboCustomerData));
            return WebResult.error(contactPersonDubboCustomerData.getCode(), contactPersonDubboCustomerData.getMsg());
        }
        List<ContactPersonDataDubboView> contactPersonDataDubboViewList = contactPersonDubboCustomerData.getData();
        return WebResult.success(BeanUtil.copyToList(contactPersonDataDubboViewList, ThirdContactPersonWebView.class));
    }

    /**
     * 添加联系人
     *
     * @param thirdContactPersonAddWebDto 联系人数据
     * <AUTHOR>
     * @date 2023/5/16 17:18
     * @return com.ce.scrm.customer.web.entity.response.WebResult<com.ce.scrm.customer.web.entity.view.ThirdContactPersonAddWebView>
     **/
    @Override
    public WebResult<ThirdContactPersonAddWebView> addContactPerson(ThirdContactPersonAddWebDto thirdContactPersonAddWebDto) {
        ContactPersonAddDubboDto contactPersonAddDubboDto = BeanUtil.copyProperties(thirdContactPersonAddWebDto, ContactPersonAddDubboDto.class);
        DubboResult<ContactPersonAddDubboView> contactPersonAddDubboViewDubboResult = contactPersonDubbo.add(contactPersonAddDubboDto);
        if (!contactPersonAddDubboViewDubboResult.checkSuccess()) {
            log.warn("调用添加联系人数据dubbo失败，返回值为:{}", JSON.toJSONString(contactPersonAddDubboViewDubboResult));
            return WebResult.error(contactPersonAddDubboViewDubboResult.getCode(), contactPersonAddDubboViewDubboResult.getMsg());
        }
        return WebResult.success(BeanUtil.copyProperties(contactPersonAddDubboViewDubboResult.getData(), ThirdContactPersonAddWebView.class));
    }

    /**
     * 更新联系人
     *
     * @param thirdContactPersonUpdateWebDto 联系人数据
     * <AUTHOR>
     * @date 2023/5/16 17:23
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @Override
    public WebResult<Boolean> updateContactPerson(ThirdContactPersonUpdateWebDto thirdContactPersonUpdateWebDto) {
        ContactPersonUpdateDubboDto contactPersonUpdateDubboDto = BeanUtil.copyProperties(thirdContactPersonUpdateWebDto, ContactPersonUpdateDubboDto.class);
        DubboResult<Boolean> contactPersonUpdateDubboViewDubboResult = contactPersonDubbo.update(contactPersonUpdateDubboDto);
        if (!contactPersonUpdateDubboViewDubboResult.checkSuccess()) {
            log.warn("调用更新联系人数据dubbo失败，返回值为:{}", JSON.toJSONString(contactPersonUpdateDubboViewDubboResult));
            return WebResult.error(contactPersonUpdateDubboViewDubboResult.getCode(), contactPersonUpdateDubboViewDubboResult.getMsg());
        }
        return WebResult.success(contactPersonUpdateDubboViewDubboResult.getData());
    }
}