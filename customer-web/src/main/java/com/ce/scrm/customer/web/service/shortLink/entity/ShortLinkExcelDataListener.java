package com.ce.scrm.customer.web.service.shortLink.entity;

import cn.hutool.core.lang.UUID;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.extend.dubbo.api.ShortUrlDubboService;
import com.ce.scrm.extend.dubbo.entity.dto.ShortUrlParam;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.ShortUrlView;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 短链表格数据读取监听
 * <AUTHOR>
 * @date 2024/5/10 下午2:56
 * @version 1.0.0
 **/
@Slf4j
public class ShortLinkExcelDataListener extends AnalysisEventListener<ShortLinkData> {
    /**
     * sheet页的名称
     */
    @Getter
    private String sheetName;
    /**
     * excel数据列表
     */
    @Getter
    private final List<ShortLinkData> dataList = new ArrayList<>();
    /**
     * 当前数据行数
     */
    private int rowNum = -1;
    /**
     * 短链生成服务
     */
    private final ShortUrlDubboService shortUrlDubboService;

    /**
     * 原始长链接url
     */
    private static String ORIGIN_URL;

    /**
     * 原始长链接url
     */
    private final static String TARGET_URL = "https://d.300.cn/";

    public ShortLinkExcelDataListener(ShortUrlDubboService shortUrlDubboService) {
        this.shortUrlDubboService = shortUrlDubboService;
    }

    @Override
    public void invoke(ShortLinkData data, AnalysisContext context) {
        rowNum++;
        if (rowNum == 0) {
            sheetName = context.readSheetHolder().getSheetName();
        }
        if (rowNum == 1) {
            ORIGIN_URL = data.getShortLink().split("：")[1];
        }
        if (rowNum <= 2) {
            return;
        }
        String longUrl = ORIGIN_URL + "&code=" + UUID.fastUUID().toString().replace("-", "");
        data.setLongLink(longUrl);
        ShortUrlParam shortUrlParam = new ShortUrlParam();
        shortUrlParam.setLongUrl(longUrl);
        DubboResult<ShortUrlView> shortUrlViewDubboResult = shortUrlDubboService.generateShortUrl(shortUrlParam);
        ShortUrlView shortUrlView;
        if (shortUrlViewDubboResult.checkSuccess() && (shortUrlView = shortUrlViewDubboResult.getData()) != null) {
            data.setShortLink(TARGET_URL + shortUrlView.getUniqueCode());
        }
        dataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("数据读取完成，数据列表的数据为:{}", JSON.toJSONString(dataList));
    }
}