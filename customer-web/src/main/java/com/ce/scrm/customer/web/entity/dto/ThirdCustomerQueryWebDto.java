package com.ce.scrm.customer.web.entity.dto;

import com.ce.scrm.customer.web.entity.base.ThirdBaseWebDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 客户
 * <AUTHOR>
 * @date 2023/4/8 14:41
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdCustomerQueryWebDto extends ThirdBaseWebDto {

    /**
     * 客户ID
     */
    @NotBlank(message = "客户ID不能为空")
    private String customerId;
}