package com.ce.scrm.customer;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import javax.annotation.PreDestroy;

/**
 * web启动类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/06/01 12:53 下午
 **/
@SpringBootApplication
@EnableDubbo
public class ScrmCustomerWebApplication {

    private final static Logger LOGGER = LoggerFactory.getLogger(ScrmCustomerWebApplication.class);

    /**
     * 项目启动方法
     * @param args  启动参数
     * <AUTHOR>
     * @date 2021/05/24 下午2:25
     **/
    public static void main(String[] args) {
        SpringApplication.run(ScrmCustomerWebApplication.class, args);
        LOGGER.error("ScrmCustomerWebApplication is started（项目启动成功）");
    }

    @PreDestroy
    public void destroy() {
        LOGGER.error("ScrmCustomerWebApplication is started（项目即将重启）");
    }
}
