package com.ce.scrm.customer.web.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 传入文件流读取（可多次获取）
 * <AUTHOR>
 * @date 2024/5/11 下午4:29
 * @version 1.0.0
 **/
public class MultipartFileInStreamRead {
    /**
     *  流字节数据
     */
    private final byte[] byteArray;

    public MultipartFileInStreamRead(MultipartFile multipartFile) {
        try {
            this.byteArray = multipartFile.getBytes();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取输入流
     * <AUTHOR>
     * @date 2024/5/11 下午4:32
     * @return java.io.InputStream
     **/
    public InputStream getInputStream() {
        return new ByteArrayInputStream(byteArray);
    }
}
