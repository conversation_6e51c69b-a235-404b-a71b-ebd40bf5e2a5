package com.ce.scrm.customer.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.dubbo.api.ICustomerDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.CustomerConditionDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView;
import com.ce.scrm.customer.web.entity.dto.CustomerQueryWebDto;
import com.ce.scrm.customer.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.customer.web.entity.response.WebResult;
import com.ce.scrm.customer.web.entity.view.CustomerDataWebView;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 客户相关接口
 * <AUTHOR>
 * @date 2024/3/19 21:09
 * @version 1.0.0
 **/
@Slf4j
@RestController
@RequestMapping("customer")
public class CustomerController {

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private ICustomerDubbo customerDubbo;

    /**
     * 根据参数获取客户数据
     * @param customerQueryWebDto   查询参数
     * <AUTHOR>
     * @date 2024/3/19 21:09
     * @return com.ce.scrm.customer.web.entity.response.WebResult<com.ce.scrm.customer.web.entity.view.CustomerDataWebView>
     **/
    @PostMapping("getCustomerDataByParam")
    public WebResult<CustomerDataWebView> getCustomerDataByParam(@Validated @RequestBody CustomerQueryWebDto customerQueryWebDto) {
        CustomerConditionDubboDto customerConditionDubboDto = BeanUtil.copyProperties(customerQueryWebDto, CustomerConditionDubboDto.class);
        DubboResult<List<CustomerDubboView>> customerDubboByConditionDubboResult = customerDubbo.findByCondition(customerConditionDubboDto);
        if (!customerDubboByConditionDubboResult.checkSuccess()) {
            log.error("根据条件获取客户数据异常，参数为:{}, 返回数据为:{}", JSON.toJSONString(customerConditionDubboDto), JSON.toJSONString(customerDubboByConditionDubboResult));
            return WebResult.error(WebCodeMessageEnum.RPC_EXCEPTION, customerDubboByConditionDubboResult.getMsg());
        }
        CustomerDataWebView customerDataWebView = null;
        List<CustomerDubboView> customerDubboViewList;
        if (CollectionUtil.isNotEmpty(customerDubboViewList = customerDubboByConditionDubboResult.getData())) {
            customerDataWebView = BeanUtil.copyProperties(customerDubboViewList.get(0), CustomerDataWebView.class);
        }
        return WebResult.success(customerDataWebView);
    }
}
