package com.ce.scrm.customer.web.entity.dto;

import com.ce.scrm.customer.web.entity.base.ThirdBaseWebDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 三方联系人更新参数
 * <AUTHOR>
 * @date 2023/5/16 17:22
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdContactPersonUpdateWebDto extends ThirdBaseWebDto {
    /**
     * 联系人ID
     */
    private String contactPersonId;
    /**
     * 联系人名称
     */
    private String contactPersonName;
    /**
     * 性别
     */
    private Integer gender;
    /**
     * 部门
     */
    private String department;
    /**
     * 职位
     */
    private String position;
    /**
     * 证件类型
     */
    private Integer certificatesType;
    /**
     * 证件号
     */
    private String certificatesNumber;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区编码
     */
    private String districtCode;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 手机号(新老数据:分隔，多个手机号,分隔)
     */
    private String phone;
    /**
     * 邮箱(新老数据:分隔)
     */
    private String email;
    /**
     * 微信(新老数据:分隔)
     */
    private String wechat;
    /**
     * 企业微信(新老数据:分隔)
     */
    private String wecome;
    /**
     * qq(新老数据:分隔)
     */
    private String qq;
    /**
     * 电话(新老数据:分隔)
     */
    private String telephone;
    /**
     * 操作人
     */
    private String operator;
}