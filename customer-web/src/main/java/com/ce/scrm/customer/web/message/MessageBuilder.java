package com.ce.scrm.customer.web.message;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * 消息构建类
 *  压制警告是因为企业微信的接口文档限制命名
 * <AUTHOR>
 * @date 2023/4/6 16:28
 * @version 1.0.0
 **/
@SuppressWarnings("all")
public class MessageBuilder {

    /**
     * 消息
     */
    public static class Message {

        private String msgtype;

        private JSONObject text;

        public String getMsgtype() {
            return msgtype;
        }

        public void setMsgtype(String messageType) {
            this.msgtype = messageType;
        }

        public JSONObject getText() {
            return text;
        }

        public void setText(JSONObject text) {
            this.text = text;
        }
    }

    /**
     * 消息枚举类
     */
    public enum MessageTypeEnum {
        /**
         * 文本类型
         */
        TEXT(1, "text"),
        ;

        private final int value;

        private final String param;


        MessageTypeEnum(int value, String param) {
            this.value = value;
            this.param = param;
        }

        /**
         * 根据值返回枚举
         * @param value 消息类型值
         * <AUTHOR>
         * @date 2023/4/6 16:28
         * @return com.ce.scrm.customer.support.message.MessageBuilder.MessageTypeEnum
         **/
        public static MessageTypeEnum getEnumByValue(int value) {
            for (MessageTypeEnum messageTypeEnum : MessageTypeEnum.values()) {
                if (messageTypeEnum.getValue() == value) {
                    return messageTypeEnum;
                }
            }
            return null;
        }

        public int getValue() {
            return value;
        }

        public String getParam() {
            return param;
        }
    }

    /**
     * 文本消息
     **/
    public static class TextMessage {
        /**
         * 文本内容，最长不超过2048个字节，必须是utf8编码(必填)
         */
        private String content;

        /**
         * userId的列表，提醒群中的指定成员(@某个成员)，@all表示提醒所有人，如果开发者获取不到userId，可以使用mentioned_mobile_list(非必填)
         */
        private List<String> mentioned_list;

        /**
         * 手机号列表，提醒手机号对应的群成员(@某个成员)，@all表示提醒所有人
         */
        private List<String> mentioned_mobile_list;

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public List<String> getMentioned_list() {
            return mentioned_list;
        }

        public void setMentioned_list(List<String> mentioned_list) {
            this.mentioned_list = mentioned_list;
        }

        public List<String> getMentioned_mobile_list() {
            return mentioned_mobile_list;
        }

        public void setMentioned_mobile_list(List<String> mentioned_mobile_list) {
            this.mentioned_mobile_list = mentioned_mobile_list;
        }
    }
}
