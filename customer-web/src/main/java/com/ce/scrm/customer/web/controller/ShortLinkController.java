package com.ce.scrm.customer.web.controller;

import cn.hutool.core.util.StrUtil;
import com.ce.scrm.customer.web.entity.dto.ShortLinkCompleteDataWebDto;
import com.ce.scrm.customer.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.customer.web.entity.response.WebResult;
import com.ce.scrm.customer.web.service.shortLink.ShortLinkService;
import com.ce.scrm.extend.dubbo.entity.dto.ShortUrlParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 短链相关接口
 * <AUTHOR>
 * @date 2024/5/10 上午10:52
 * @version 1.0.0
 **/
@Slf4j
@RestController
@RequestMapping("short")
public class ShortLinkController {

    @Resource
    private ShortLinkService shortLinkService;

    /**
     * 完善导入的excel中的短链数据
     * @param shortLinkCompleteDataWebDto   短链完善数据参数
     * <AUTHOR>
     * @date 2024/5/10 下午5:44
     * @return void
     **/
    @PostMapping("completeData")
    public void completeData(@Validated ShortLinkCompleteDataWebDto shortLinkCompleteDataWebDto) throws IOException {
        shortLinkService.readDataAndCompleteLink(shortLinkCompleteDataWebDto.getFile());
    }

    /**
     * 根据短链code重定向到真实连接
     * @param uniqueCode    唯一code
     * <AUTHOR>
     * @date 2024/5/11 下午7:47
     * @return org.springframework.web.servlet.view.RedirectView
     **/
    @GetMapping("/{uniqueCode}")
    public RedirectView getOriginUrl(@PathVariable String uniqueCode) {
        if (StrUtil.isBlank(uniqueCode)){
            return new RedirectView("https://www.300.cn/");
        }
        String code = uniqueCode;
        if (uniqueCode.length()>6){
            code = uniqueCode.substring(0,6);
        }
        String originUrl = shortLinkService.getOriginUrl(code);
        if(StrUtil.isBlank(originUrl)){
            log.info("根据短链随机码获取长链接，短链随机码不存在,uniqueCode={}",uniqueCode);
            if (uniqueCode.contains("拒收请回复")){
                return new RedirectView("https://www.ceglobal.cn/");
            }else{
                return new RedirectView("https://www.300.cn/");
            }
        }
        return new RedirectView(originUrl);
    }

    @PostMapping("/getShortUrl")
    public WebResult<String> generateShortUrl(@RequestBody ShortUrlParam shortUrlParam){
        if (shortUrlParam==null || StrUtil.isBlank(shortUrlParam.getLongUrl())){
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
        return WebResult.success(shortLinkService.generateShortUrl(shortUrlParam.getLongUrl()));
    }
}
