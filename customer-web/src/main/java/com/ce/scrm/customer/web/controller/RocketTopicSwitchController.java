package com.ce.scrm.customer.web.controller;

import com.ce.scrm.customer.dubbo.api.IRocketTopicSwitchDubbo;
import com.ce.scrm.customer.dubbo.entity.dto.RocketTopicSwitchDubboDto;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.RocketTopicSwitchView;
import com.ce.scrm.customer.web.entity.dto.RocketTopicSwitchQueryWebDto;
import com.ce.scrm.customer.web.entity.dto.RocketTopicSwitchUpdateWebDto;
import com.ce.scrm.customer.web.entity.response.WebResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * rocket topic switch
 * <AUTHOR>
 * @date 2023/5/16 16:35
 * @version 1.0.0
 **/
@Slf4j
@RestController
@RequestMapping("/rocket/topic/switch")
public class RocketTopicSwitchController {

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private IRocketTopicSwitchDubbo rocketTopicSwitchDubbo;


    @GetMapping("list")
    public WebResult<List<RocketTopicSwitchView>> list(@Validated RocketTopicSwitchQueryWebDto rocketTopicSwitchQueryWebDto) {
        RocketTopicSwitchDubboDto rocketTopicSwitchDubboDto =new RocketTopicSwitchDubboDto();
        rocketTopicSwitchDubboDto.setTopic(rocketTopicSwitchQueryWebDto.getTopic());
        rocketTopicSwitchDubboDto.setStatus(rocketTopicSwitchQueryWebDto.getStatus());
        DubboResult<List<RocketTopicSwitchView>> dubboResult = rocketTopicSwitchDubbo.getTopicSwitchByConditation(rocketTopicSwitchDubboDto);
        return WebResult.success(dubboResult.getData());
    }

    @PostMapping("update")
    public WebResult<Boolean> addContactPerson(@Validated @RequestBody RocketTopicSwitchUpdateWebDto rocketTopicSwitchUpdateWebDto) {
        RocketTopicSwitchDubboDto rocketTopicSwitchDubboDto = new RocketTopicSwitchDubboDto();
        rocketTopicSwitchDubboDto.setTopic(rocketTopicSwitchUpdateWebDto.getTopic());
        rocketTopicSwitchDubboDto.setStatus(rocketTopicSwitchUpdateWebDto.getStatus());
        DubboResult<Boolean> dubboResult=rocketTopicSwitchDubbo.update(rocketTopicSwitchDubboDto);
        return WebResult.success(dubboResult.getData());
    }


}
