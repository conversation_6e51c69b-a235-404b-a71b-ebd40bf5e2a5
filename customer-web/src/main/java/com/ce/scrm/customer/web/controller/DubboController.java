package com.ce.scrm.customer.web.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.ce.scrm.customer.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.customer.web.entity.response.WebResult;
import com.ce.scrm.customer.web.init.ChannelConfig;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.utils.ReferenceConfigCache;
import org.apache.dubbo.rpc.service.GenericService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 直接调用dubbo接口返回数据
 * <AUTHOR>
 * @date 2023/6/20 16:17
 * @version 1.0.0
 **/
@RestController
@RequestMapping("dubbo")
public class DubboController {

    /**
     * 调用dubbo接口
     * @param dubboInvokeParam  调用参数
     * <AUTHOR>
     * @date 2023/6/20 16:48
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.lang.Object>
     **/
    @PostMapping("invoke")
    public WebResult<Object> get(@RequestBody DubboInvokeParam dubboInvokeParam) {
        if(dubboInvokeParam == null){
            return WebResult.error(WebCodeMessageEnum.REQUEST_DUBBO_GENERIC_PASSWORD_ERROR);
        }
        if (StringUtils.isBlank(dubboInvokeParam.getInterfaceClass()) || StringUtils.isBlank(dubboInvokeParam.getMethodName())) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_NOT_NULL);
        }
        //泛化调用
        ReferenceConfig<GenericService> referenceConfig = new ReferenceConfig<>();
        if (StrUtil.isNotBlank(dubboInvokeParam.getGroup())) {
            referenceConfig.setGroup(dubboInvokeParam.getGroup());
        }
        //接口全限定名
        referenceConfig.setInterface(GenericService.class.getName());
        referenceConfig.setInterface(dubboInvokeParam.getInterfaceClass());
        //声明为泛化接口
        referenceConfig.setGeneric(Boolean.TRUE.toString());
        referenceConfig.setId(dubboInvokeParam.getMethodName());
        if(StrUtil.isNotBlank(dubboInvokeParam.getVersion())){
            referenceConfig.setVersion(dubboInvokeParam.getVersion());
        }
        //加入缓存处理
        ReferenceConfigCache cache = ReferenceConfigCache.getCache();
        GenericService genericService = cache.get(referenceConfig);
        if (CollectionUtil.isEmpty(dubboInvokeParam.getParamList())) {
            dubboInvokeParam.setParamList(new ArrayList<>());
        }
        int paramSize = dubboInvokeParam.getParamList().size();
        String[] invokeParamTypeArray = new String[paramSize];
        Object[] invokeParamValueArray = new Object[paramSize];
        DubboInvokeParam.packageDubboInvokeParam(dubboInvokeParam.getParamList(), invokeParamTypeArray, invokeParamValueArray);
        return WebResult.success(genericService.$invoke(dubboInvokeParam.getMethodName(), invokeParamTypeArray, invokeParamValueArray));
    }

    /**
     * dubbo调用参数对象
     */
    public static class DubboInvokeParam implements Serializable {

        private static final long serialVersionUID = 3658517289822349366L;

        /**
         * 调用密码
         */
        private String invokePassword;
        /**
         * 服务组
         */
        private String group;

        /**
         * dubbo版本
         */
        private String version;
        /**
         * 接口类全限定名
         */
        private String interfaceClass;
        /**
         * 方法名
         */
        private String methodName;

        /**
         * 参数列表（指定类型和）
         */
        private List<DubboParam> paramList;

        /**
         * dubbo参数
         */
        public static class DubboParam implements Serializable {

            private static final long serialVersionUID = 1721311409194365751L;
            /**
             * 参数类型
             */
            private String paramType;
            /**
             * 参数值
             */
            private Object paramValue;

            public String getParamType() {
                return paramType;
            }

            public void setParamType(String paramType) {
                this.paramType = paramType;
            }

            public Object getParamValue() {
                return paramValue;
            }

            public void setParamValue(Object paramValue) {
                this.paramValue = paramValue;
            }
        }

        public String getInvokePassword() {
            return invokePassword;
        }

        public void setInvokePassword(String invokePassword) {
            this.invokePassword = invokePassword;
        }

        public String getGroup() {
            return group;
        }

        public void setGroup(String group) {
            this.group = group;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getInterfaceClass() {
            return interfaceClass;
        }

        public void setInterfaceClass(String interfaceClass) {
            this.interfaceClass = interfaceClass;
        }

        public String getMethodName() {
            return methodName;
        }

        public void setMethodName(String methodName) {
            this.methodName = methodName;
        }

        public List<DubboParam> getParamList() {
            return paramList;
        }

        public void setParamList(List<DubboParam> paramList) {
            this.paramList = paramList;
        }

        /**
         * 拼装dubbo调用参数
         * @param dubboParamList    dubbo参数列表
         * @param invokeParamTypeArray  调用方法参数类型数组
         * @param invokeParamValueArray 调用方法参数值数组
         * <AUTHOR>
         **/
        public static void packageDubboInvokeParam(List<DubboParam> dubboParamList, String[] invokeParamTypeArray, Object[] invokeParamValueArray) {
            if (CollectionUtil.isEmpty(dubboParamList)) {
                return;
            }
            for (int i = 0; i < dubboParamList.size(); i++) {
                invokeParamTypeArray[i] = dubboParamList.get(i).getParamType();
                invokeParamValueArray[i] = dubboParamList.get(i).getParamValue();
            }
        }
    }
}


