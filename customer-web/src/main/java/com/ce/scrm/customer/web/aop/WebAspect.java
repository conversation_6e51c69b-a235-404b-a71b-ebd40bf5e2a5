package com.ce.scrm.customer.web.aop;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.util.constant.UtilConstant;
import com.ce.scrm.customer.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.customer.web.entity.response.WebResult;
import com.ce.scrm.customer.web.log.LogObject;
import com.ce.scrm.customer.web.log.MdcUtil;
import com.ce.scrm.customer.web.util.RequestUtil;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * dubbo业务切面
 * <AUTHOR>
 * @date 2023/4/6 20:48
 * @version 1.0.0
 **/
@Aspect
@Component
public class WebAspect {

    /**
     * 日志
     */
    private final static Logger LOGGER = LoggerFactory.getLogger(WebAspect.class);

    /**
     * 执行成功消息
     */
    private final static String EXECUTE_SUCCESS = "success";

    /**
     * 默认代指前端调用
     */
    public final static String WEB_INVOKE = "webInvoke";

    /**
     * 定义cat监控扫描路径
     * 定义切点
     * <AUTHOR>
     * @date 2023/4/6 20:48
     **/
    @Pointcut("execution(public * com.ce.scrm.customer.web.controller..*.*(..))")
    public void requestConfigPointCut() {
    }

    /**
     * 对于监控到的方法进行监控增强处理
     * 定义环绕类型的Advise（增强器）
     * @param joinPoint 切面连接点（被代理方法的相关封装）
     * <AUTHOR>
     * @date 2023/4/6 20:48
     * @return java.lang.Object
     **/
    @Around("requestConfigPointCut()")
    public Object around(ProceedingJoinPoint joinPoint) {
        MdcUtil.initMdc();
        //添加cat监控
        Class<?> clazz = joinPoint.getTarget().getClass();
        String className = clazz.getSimpleName();
        Signature signature = joinPoint.getSignature();
        String methodName = signature.getName();
        Transaction serviceCat = Cat.newTransaction("Controller", className + UtilConstant.CAT_SEPARATOR + methodName);
        serviceCat.setStatus(Transaction.SUCCESS);
        Object proceed = null;
        LogObject logObject = new LogObject();
        String traceId = MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME);
        Object[] args = joinPoint.getArgs();
        logObject.setInvokerName(WEB_INVOKE)
                .setInvokerIp(RequestUtil.getIp())
                .setEventName(className + UtilConstant.DATA_SEPARATOR + methodName)
                .setTraceId(traceId)
                .setRequest(JSON.toJSONString(args));
        String msg = EXECUTE_SUCCESS;
        try {
            proceed = joinPoint.proceed();
        } catch (Throwable e) {
            WebResult<Object> webResult = WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
            webResult.setData(traceId);
            msg = e.getMessage();
            serviceCat.setStatus(e);
            Cat.logError(e);
            LOGGER.error(className + UtilConstant.DATA_SEPARATOR + methodName + "方法异常:", e);
            //全局异常捕获
            return (proceed = webResult);
        } finally {
            if (proceed instanceof WebResult) {
                WebResult<Object> resultEntity = (WebResult<Object>) proceed;
                //非正常返回，则返回traceId
                if (!resultEntity.checkSuccess()) {
                    resultEntity.setData(traceId);
                    logObject.setResponse(JSON.toJSONString(proceed));
                }
            }
            logObject.setMsg(msg).setCostTime(System.currentTimeMillis() - Long.parseLong(MDC.get(UtilConstant.Mdc.START_TIME)));
            LOGGER.info(JSON.toJSONString(logObject));
            serviceCat.complete();
        }
        return proceed;
    }
}
