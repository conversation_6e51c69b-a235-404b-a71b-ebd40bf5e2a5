package com.ce.scrm.customer.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.dubbo.api.IAreaDubbo;
import com.ce.scrm.customer.dubbo.entity.base.SignData;
import com.ce.scrm.customer.dubbo.entity.response.DubboResult;
import com.ce.scrm.customer.dubbo.entity.view.AreaDubboView;
import com.ce.scrm.customer.web.entity.response.WebResult;
import com.ce.scrm.customer.web.entity.view.AreaWebView;
import com.ce.scrm.customer.web.service.IAreaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 地区业务实现
 * <AUTHOR>
 * @date 2023/5/16 17:31
 * @version 1.0.0
 */
@Slf4j
@Service
public class AreaServiceImpl implements IAreaService {

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private IAreaDubbo areaDubbo;

    /**
     * 获取所有区域数据
     *
     * <AUTHOR>
     * @date 2023/5/16 17:34
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.AreaWebView>>
     **/
    @Override
    public WebResult<List<AreaWebView>> getAllData() {
        return getAllData(null, null);
    }

    /**
     * 获取所有区域数据
     *
     * @param sourceKey    来源key
     * @param sourceSecret 来源密钥
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.AreaWebView>>
     * <AUTHOR>
     * @date 2023/5/16 17:34
     **/
    @Override
    public WebResult<List<AreaWebView>> getAllData(String sourceKey, String sourceSecret) {
        SignData signData = new SignData();
        if (StrUtil.isNotBlank(sourceKey)) {
            signData.setSourceKey(sourceKey);
        }
        if (StrUtil.isNotBlank(sourceSecret)) {
            signData.setSourceSecret(sourceSecret);
        }
        DubboResult<List<AreaDubboView>> areaDubboAllData = areaDubbo.getAllData(signData);
        if (!areaDubboAllData.checkSuccess()) {
            log.warn("调用dubbo获取所有区域数据失败，返回数据为:{}", JSON.toJSONString(areaDubboAllData));
            return WebResult.error(areaDubboAllData.getCode(), areaDubboAllData.getMsg());
        }
        return WebResult.success(BeanUtil.copyToList(areaDubboAllData.getData(), AreaWebView.class));
    }
}