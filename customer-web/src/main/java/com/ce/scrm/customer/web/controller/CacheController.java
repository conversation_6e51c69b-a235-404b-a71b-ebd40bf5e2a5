package com.ce.scrm.customer.web.controller;

import com.ce.scrm.customer.dubbo.api.ICacheDubbo;
import com.ce.scrm.customer.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.customer.web.entity.response.WebResult;
import com.ce.scrm.customer.web.init.ChannelConfig;
import com.ce.scrm.customer.web.util.RequestUtil;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Set;

/**
 * 缓存相关
 * <AUTHOR>
 * @date 2023/6/20 12:56
 * @version 1.0.0
 **/
@RestController
@RequestMapping("cache")
public class CacheController {

    @DubboReference(group = "scrm-customer-api", version = "1.0.0", check = false)
    private ICacheDubbo cacheDubbo;

    /**
     * 获取缓存数据
     * @param key   缓存key
     * <AUTHOR>
     * @date 2023/6/25 10:57
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.lang.String>
     **/
    @GetMapping("get")
    public WebResult<String> get(@RequestParam("key") String key) {
        if (!checkInvokePassword()) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_DUBBO_GENERIC_PASSWORD_ERROR);
        }
        return WebResult.success(cacheDubbo.get(key));
    }

    /**
     * 删除缓存数据
     * @param key   缓存key
     * <AUTHOR>
     * @date 2023/6/25 10:57
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @GetMapping("del")
    public WebResult<Boolean> del(@RequestParam("key") String key) {
        if (!checkInvokePassword()) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_DUBBO_GENERIC_PASSWORD_ERROR);
        }
        return WebResult.success(cacheDubbo.del(key));
    }

    /**
     * 根据key前缀获取所有的缓存key
     * @param key   缓存key（支持模糊）
     * <AUTHOR>
     * @date 2023/6/25 10:57
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.Set < java.lang.String>>
     **/
    @GetMapping("getKeys")
    public WebResult<Set<String>> getKeys(@RequestParam("key") String key) {
        if (!checkInvokePassword()) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_DUBBO_GENERIC_PASSWORD_ERROR);
        }
        return WebResult.success(cacheDubbo.getKeys(key));
    }

    /**
     * 根据key前缀删除所有的缓存key
     * @param key  缓存key（支持模糊）
     * <AUTHOR>
     * @date 2023/6/25 10:57
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @GetMapping("delKeys")
    public WebResult<Boolean> delKeys(@RequestParam("key") String key) {
        if (!checkInvokePassword()) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_DUBBO_GENERIC_PASSWORD_ERROR);
        }
        return WebResult.success(cacheDubbo.delKeys(key));
    }

    /**
     * 校验当前请求的调用密码
     * <AUTHOR>
     * @date 2023/6/25 10:54
     * @return boolean
     **/
    private boolean checkInvokePassword() {
        HttpServletRequest request = RequestUtil.getRequest();
        String invokePassword = request.getHeader("invokePassword");
        if (!ChannelConfig.channelConfig.getSourceSecret().equals(invokePassword)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 获取缓存枚举类型
     * <AUTHOR>
     * @date 2023/8/21 11:09
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < java.lang.String>>
     **/
    @GetMapping("getCacheEnumType")
    public WebResult<List<String>> getCacheEnumType() {
        if (!checkInvokePassword()) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_DUBBO_GENERIC_PASSWORD_ERROR);
        }
        return WebResult.success(cacheDubbo.getCacheEnumType());
    }

    /**
     * 获取缓存key
     * @param keyType   缓存枚举类型
     * @param serviceId 业务ID
     * <AUTHOR>
     * @date 2023/8/21 11:09
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.lang.String>
     **/
    @GetMapping("getKey")
    public WebResult<String> getKey(String keyType, String serviceId) {
        if (!checkInvokePassword()) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_DUBBO_GENERIC_PASSWORD_ERROR);
        }
        return WebResult.success(cacheDubbo.getKey(keyType, serviceId));
    }
}


