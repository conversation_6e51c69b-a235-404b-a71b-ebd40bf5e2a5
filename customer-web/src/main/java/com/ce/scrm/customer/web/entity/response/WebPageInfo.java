package com.ce.scrm.customer.web.entity.response;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * web返回分页信息
 * <AUTHOR>
 * @date 2021/6/2 上午11:02
 * @version 1.0.0
 **/
@Data
public class WebPageInfo<T> implements Serializable {

    private static final long serialVersionUID = -6455234157678276815L;
    protected long total;
    private long pageNum;
    private long pageSize;
    private long pages;

    protected List<T> list;

    /**
     * mybatis-plus分页转换
     *
     * @param page mybatis-plus分页信息
     * @return com.wuyi.cicd.server.config.response.WebPageInfo<N>
     * <AUTHOR>
     * @date 2022/6/27 11:36
     **/
    public static <O, N> WebPageInfo<N> pageConversion(DubboPageInfo<O> page) {
        WebPageInfo<N> pageInfo = new WebPageInfo<>();
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setPages(page.getPages());
        List<O> records = page.getList();
        List<N> list = new ArrayList<>();
        BeanUtils.copyProperties(records, list);
        pageInfo.setList(list);
        return pageInfo;
    }

    public static <O> WebPageInfo<O> convertToPageInfo(DubboPageInfo<O> page) {
        WebPageInfo<O> pageInfo = new WebPageInfo<>();
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setPages(page.getPages());
        List<O> records = page.getList();
        pageInfo.setList(records);
        return pageInfo;
    }

    public static <O, N> WebPageInfo<N> pageConversion(DubboPageInfo<O> page, Class<N> clazz) {
        WebPageInfo<N> pageInfo = new WebPageInfo<>();
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setPages(page.getPages());
        List<O> records = page.getList();
        List<N> list = BeanUtil.copyToList(records, clazz);
        pageInfo.setList(list);
        return pageInfo;
    }
}
