package com.ce.scrm.customer.web.dubbo;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.ce.scrm.customer.dubbo.entity.base.SignData;
import com.ce.scrm.customer.util.constant.UtilConstant;
import com.ce.scrm.customer.util.sign.SignUtils;
import com.ce.scrm.customer.web.init.ChannelConfig;
import com.ce.scrm.customer.web.init.InitConsole;
import org.apache.dubbo.rpc.*;
import org.slf4j.MDC;

import java.util.Map;

/**
 * 自定义dubbo过滤器
 * <AUTHOR>
 * @date 2023/4/6 14:02
 * @version 1.0.0
 **/
public class CustomDubboFilter implements Filter {

    /**
     * 随机字符串的长度
     */
    private final static int RANDOM_STRING_LENGTH = 6;

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        UtilConstant.Mdc.MDC_LIST.forEach((key, value) -> {
            if (RpcContext.getContext().isProviderSide()) {
                String rpcValue = RpcContext.getContext().getAttachment(key);
                if (StrUtil.isBlank(rpcValue)) {
                    rpcValue = value.get();
                }
                MDC.put(key, rpcValue);
            } else {
                RpcContext.getContext().setAttachment(key, MDC.get(key));
            }
        });
        if (RpcContext.getContext().isConsumerSide()) {
            RpcContext.getContext().setRemoteApplicationName(InitConsole.APPLICATION_NAME);
            //添加参数签名验证
            addRequestInfo();
        }
        return invoker.invoke(invocation);
    }

    /**
     * 仅添加请求密钥相关信息
     * <AUTHOR>
     * @date 2023/4/15 12:45
     **/
    private static void addRequestInfo() {
        Object[] arguments = RpcContext.getContext().getArguments();
        for (Object argument : arguments) {
            if (argument instanceof SignData) {
                SignData signData = (SignData) argument;
                if (StrUtil.isBlank(signData.getSourceKey())) {
                    signData.setSourceKey(ChannelConfig.channelConfig.getSourceKey());
                    signData.setSourceSecret(ChannelConfig.channelConfig.getSourceSecret());
                }
            }
        }
    }

    /**
     * dubbo消费者生成签名
     * <AUTHOR>
     * @date 2023/4/10 16:45
     **/
    private static void sign() {
        Object[] arguments = RpcContext.getContext().getArguments();
        for (Object argument : arguments) {
            if (argument instanceof SignData) {
                SignData signData = (SignData) argument;
                signData.setSourceKey(ChannelConfig.channelConfig.getSourceKey());
                signData.setSourceSecret(ChannelConfig.channelConfig.getSourceSecret());
                signData.setTimestamp(DateUtil.currentSeconds());
                signData.setRandomStr(RandomUtil.randomString(RANDOM_STRING_LENGTH));
            }
        }
        Map<String, String> paramMap = SignUtils.getParamMap(arguments);
        for (Object argument : arguments) {
            if (argument instanceof SignData) {
                SignData signData = (SignData) argument;
                String methodName = RpcContext.getContext().getMethodName();
                String interfaceName = RpcContext.getContext().getUrl().getPath();
                String requestPath = interfaceName + UtilConstant.CAT_SEPARATOR + methodName;
                signData.setSign(SignUtils.generateSign(requestPath, paramMap));
                signData.setSourceSecret(null);
            }
        }
    }
}
