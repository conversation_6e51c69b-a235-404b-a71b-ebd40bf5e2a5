package com.ce.scrm.customer.web.service.shortLink;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.web.service.shortLink.entity.ShortLinkData;
import com.ce.scrm.customer.web.service.shortLink.entity.ShortLinkExcelDataListener;
import com.ce.scrm.customer.web.util.MultipartFileInStreamRead;
import com.ce.scrm.customer.web.util.RequestUtil;
import com.ce.scrm.extend.dubbo.api.ShortUrlDubboService;
import com.ce.scrm.extend.dubbo.entity.dto.ShortUrlParam;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.ShortUrlView;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.rmi.RemoteException;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 短链业务
 * <AUTHOR>
 * @date 2024/5/10 下午1:58
 * @version 1.0.0
 */
@Slf4j
@Service
public class ShortLinkService {

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", timeout = 10000)
    private ShortUrlDubboService shortUrlDubboService;

    private final static String TARGET_URL = "https://d.300.cn/";

    /**
     * 读取文件并填充长短链接
     * @param multipartFile 文件
     * <AUTHOR>
     * @date 2024/5/10 下午2:05
     **/
    public void readDataAndCompleteLink(MultipartFile multipartFile) throws IOException {
        String fileName = multipartFile.getOriginalFilename();
        if (fileName == null || !fileName.endsWith(".xlsx")) {
            log.error("完善短链数据，传入文件有误，文件名称为:{}", fileName);
            throw new RemoteException("完善短链数据，传入文件有误");
        }
        fileName = decodeHtmlUnicode(fileName);
        MultipartFileInStreamRead multipartFileInStreamRead = new MultipartFileInStreamRead(multipartFile);
        ShortLinkExcelDataListener shortLinkExcelDataListener = new ShortLinkExcelDataListener(shortUrlDubboService);
        EasyExcel.read(multipartFileInStreamRead.getInputStream(), ShortLinkData.class, shortLinkExcelDataListener).headRowNumber(0).sheet(0).doRead();
        HttpServletResponse response = RequestUtil.getResponse();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        List<Map<String, Object>> fillData = shortLinkExcelDataListener.getDataList().stream().map(shortLinkData -> JSON.parseObject(JSON.toJSONString(shortLinkData)).getInnerMap()).collect(Collectors.toList());
        EasyExcel.write(response.getOutputStream()).withTemplate(multipartFileInStreamRead.getInputStream()).excelType(ExcelTypeEnum.XLSX).sheet(shortLinkExcelDataListener.getSheetName()).doFill(fillData);
    }

    /**
     * html10进制unicode解码
     * @param str
     * <AUTHOR>
     * @date 2024/5/11 下午6:22
     * @return java.lang.String
     **/
    private static String decodeHtmlUnicode(String str) {
        Matcher matcher = Pattern.compile("&#(\\d+);").matcher(str);
        StringBuffer sb = new StringBuffer(str.length());
        while (matcher.find()) {
            String unicodeStr = matcher.group(1);
            int codePoint = Integer.parseInt(unicodeStr);
            matcher.appendReplacement(sb, Character.toString((char) codePoint));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 根据短链随机码获取长链接
     * @param uniqueCode    短链随机码
     * <AUTHOR>
     * @date 2024/5/11 上午9:03
     * @return java.lang.String
     **/
    public String getOriginUrl(String uniqueCode) {
        if (StrUtil.isBlank(uniqueCode)) {
            log.error("根据短链随机码获取长链接，短链随机码为空");
            return null;
        }
        DubboResult<ShortUrlView> longUrlByUniqueCode = shortUrlDubboService.getLongUrlByUniqueCode(uniqueCode);
        ShortUrlView shortUrlView;
        String longUrl;
        if (!longUrlByUniqueCode.checkSuccess() || (shortUrlView = longUrlByUniqueCode.getData()) == null || StrUtil.isBlank(longUrl = shortUrlView.getLongUrl())) {
            log.info("根据短链随机码获取长链接，返回数据失败，请求随机码为:{}, 返回值为:{}", uniqueCode, longUrlByUniqueCode);
            return null;
        }
        return longUrl;
    }

    public String generateShortUrl(String longUrl){
        ShortUrlParam shortUrlParam = new ShortUrlParam();
        shortUrlParam.setLongUrl(longUrl);
        DubboResult<ShortUrlView> shortUrlViewDubboResult = shortUrlDubboService.generateShortUrl(shortUrlParam);
        if (shortUrlViewDubboResult.checkSuccess() && (shortUrlViewDubboResult.getData()) != null) {
            String shortLink = TARGET_URL + shortUrlViewDubboResult.getData().getUniqueCode();
            return shortLink;
        }
        return null;
    }
}