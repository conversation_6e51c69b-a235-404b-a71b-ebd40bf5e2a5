package com.ce.scrm.customer.web.entity.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 短链完善数据web参数
 * <AUTHOR>
 * @date 2024/5/10 上午11:14
 * @version 1.0.0
 */
@Data
public class ShortLinkCompleteDataWebDto implements Serializable {
    @JSONField(serialize = false)
    @NotNull(message = "导入文件不能为空")
    private transient MultipartFile file;


}