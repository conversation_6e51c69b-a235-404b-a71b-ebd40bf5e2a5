package com.ce.scrm.customer.web.controller;

import com.ce.scrm.customer.web.entity.response.WebResult;
import com.ce.scrm.customer.web.entity.view.AreaWebView;
import com.ce.scrm.customer.web.service.IAreaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 地区相关接口
 * <AUTHOR>
 * @date 2023/5/16 16:35
 * @version 1.0.0
 **/
@Slf4j
@RestController
@RequestMapping("area")
public class AreaController {

    @Resource
    private IAreaService areaService;

    /**
     * 获取所有区域数据
     * <AUTHOR>
     * @date 2023/5/16 17:34
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.AreaWebView>>
     **/
    @GetMapping("getAllData")
    public WebResult<List<AreaWebView>> getAllData() {
        return areaService.getAllData();
    }
}
