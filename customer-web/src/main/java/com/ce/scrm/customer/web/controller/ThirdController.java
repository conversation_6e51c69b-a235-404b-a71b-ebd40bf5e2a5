package com.ce.scrm.customer.web.controller;

import com.ce.scrm.customer.web.entity.base.ThirdBaseWebDto;
import com.ce.scrm.customer.web.entity.dto.ThirdContactPersonAddWebDto;
import com.ce.scrm.customer.web.entity.dto.ThirdContactPersonQueryWebDto;
import com.ce.scrm.customer.web.entity.dto.ThirdContactPersonUpdateWebDto;
import com.ce.scrm.customer.web.entity.dto.ThirdCustomerQueryWebDto;
import com.ce.scrm.customer.web.entity.response.WebResult;
import com.ce.scrm.customer.web.entity.view.AreaWebView;
import com.ce.scrm.customer.web.entity.view.ThirdContactPersonAddWebView;
import com.ce.scrm.customer.web.entity.view.ThirdContactPersonWebView;
import com.ce.scrm.customer.web.service.IAreaService;
import com.ce.scrm.customer.web.service.IThirdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 三方相关接口
 * <AUTHOR>
 * @date 2023/5/16 16:35
 * @version 1.0.0
 **/
@Slf4j
@RestController
@RequestMapping("third")
public class ThirdController {

    @Resource
    private IThirdService thirdService;

    @Resource
    private IAreaService areaService;

    /**
     * 获取客户下联系人数据
     * @param thirdCustomerQueryWebDto   查询参数
     * <AUTHOR>
     * @date 2023/5/16 16:56
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.ContactPersonWebView>>
     **/
    @GetMapping("getCustomerContactPersonData")
    public WebResult<List<ThirdContactPersonWebView>> getCustomerContactPersonData(@Validated ThirdCustomerQueryWebDto thirdCustomerQueryWebDto) {
        return thirdService.getCustomerContactPersonData(thirdCustomerQueryWebDto);
    }

    /**
     * 获取联系人数据
     * @param thirdContactPersonQueryWebDto 查询参数
     * <AUTHOR>
     * @date 2023/5/16 17:11
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.ContactPersonWebView>>
     **/
    @GetMapping("getContactPersonData")
    public WebResult<List<ThirdContactPersonWebView>> getContactPersonData(@Validated ThirdContactPersonQueryWebDto thirdContactPersonQueryWebDto) {
        return thirdService.getContactPersonData(thirdContactPersonQueryWebDto);
    }

    /**
     * 添加联系人
     * @param thirdContactPersonAddWebDto   联系人数据
     * <AUTHOR>
     * @date 2023/5/16 17:16
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.ContactPersonWebView>>
     **/
    @PostMapping("contactPerson/add")
    public WebResult<ThirdContactPersonAddWebView> addContactPerson(@Validated @RequestBody ThirdContactPersonAddWebDto thirdContactPersonAddWebDto) {
        return thirdService.addContactPerson(thirdContactPersonAddWebDto);
    }

    /**
     * 更新联系人
     * @param thirdContactPersonUpdateWebDto    联系人数据
     * <AUTHOR>
     * @date 2023/5/16 17:23
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.lang.Boolean>
     **/
    @PostMapping("contactPerson/update")
    public WebResult<Boolean> updateContactPerson(@Validated @RequestBody ThirdContactPersonUpdateWebDto thirdContactPersonUpdateWebDto) {
        return thirdService.updateContactPerson(thirdContactPersonUpdateWebDto);
    }

    /**
     * 获取所有区域数据
     * <AUTHOR>
     * @date 2023/5/16 17:34
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.AreaWebView>>
     **/
    @GetMapping("area/getAllData")
    public WebResult<List<AreaWebView>> getAllData(@Validated ThirdBaseWebDto thirdBaseWebDto) {
        return areaService.getAllData(thirdBaseWebDto.getSourceKey(), thirdBaseWebDto.getSourceSecret());
    }
}
