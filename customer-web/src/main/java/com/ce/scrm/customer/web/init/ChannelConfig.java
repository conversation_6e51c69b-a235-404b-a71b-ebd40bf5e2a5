package com.ce.scrm.customer.web.init;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 常量数据数据配置类
 * <AUTHOR>
 * @date 2021/7/4 下午12:19
 * @version 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "channel")
public class ChannelConfig {

    /**
     * 初始化渠道对象
     */
    public static ChannelConfig channelConfig;

    /**
     * 来源key
     */
    private String sourceKey;

    /**
     * 来源密钥
     */
    private String sourceSecret;
}
