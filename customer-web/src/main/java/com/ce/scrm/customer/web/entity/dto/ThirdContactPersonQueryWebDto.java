package com.ce.scrm.customer.web.entity.dto;

import com.ce.scrm.customer.web.entity.base.ThirdBaseWebDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 联系人查询参数
 * <AUTHOR>
 * @date 2023/5/16 17:09
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdContactPersonQueryWebDto extends ThirdBaseWebDto {

    /**
     * 联系人id（此参数与下面的客户ID和手机号，存在互斥关系，优先获取联系人ID）
     */
    private String contactPersonId;

    /**
     * 客户ID（仅业务系统可用）
     */
    private String customerId;

    /**
     * 手机号（仅业务系统可用）
     */
    private String phone;
}