package com.ce.scrm.customer.web.exception;

import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.util.constant.UtilConstant;
import com.ce.scrm.customer.web.aop.WebAspect;
import com.ce.scrm.customer.web.entity.response.WebCodeMessageEnum;
import com.ce.scrm.customer.web.entity.response.WebResult;
import com.ce.scrm.customer.web.log.LogObject;
import com.ce.scrm.customer.web.util.RequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;

/**
 * 全局异常处理器
 * <AUTHOR>
 * @date 2023/4/8 15:48
 * @version 1.0.0
 **/
@RestControllerAdvice
@RequestMapping("error")
public class GlobalExceptionHandler {

    private final static Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @RequestMapping("400")
    public WebResult<?> error400() {
        return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
    }

    @RequestMapping("404")
    public WebResult<?> error404() {
        return WebResult.error(WebCodeMessageEnum.REQUEST_ADDRESS_EXCEPTION);
    }

    @RequestMapping("500")
    public WebResult<?> error500() {
        return WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
    }

    @ExceptionHandler(Exception.class)
    public WebResult<?> error(HttpServletRequest request, Exception exception) {
        if (exception instanceof HttpRequestMethodNotSupportedException) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_METHOD_EXCEPTION);
        }
        if (exception instanceof MethodArgumentTypeMismatchException || exception instanceof MissingServletRequestParameterException) {
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION);
        }
        //传入参数异常
        WebResult<?> resultEntity = handlerParamValid(exception);
        if (!resultEntity.checkSuccess()) {
            return resultEntity;
        }
        String traceId = MDC.get(UtilConstant.Mdc.REQUEST_ID_NAME);
        WebResult<Object> errorWebResult = WebResult.error(WebCodeMessageEnum.SERVER_INTERNAL_EXCEPTION);
        errorWebResult.setData(traceId);
        LogObject logObject = new LogObject();
        logObject.setInvokerName(WebAspect.WEB_INVOKE)
                .setInvokerIp(RequestUtil.getIp())
                .setEventName(request.getRequestURI() + UtilConstant.DATA_VALUE_SEPARATOR + request.getMethod())
                .setTraceId(traceId)
                .setRequest(JSON.toJSONString(request.getParameterMap()))
                .setResponse(JSON.toJSONString(errorWebResult))
                .setMsg(exception.getMessage())
                .setCostTime(System.currentTimeMillis() - Long.parseLong(MDC.get(UtilConstant.Mdc.START_TIME)));
        LOGGER.error("请求异常, 请求数据为:{},异常信息为:", JSON.toJSONString(logObject), exception);
        return errorWebResult;
    }

    /**
     * 传入参数异常
     * @param exception 异常信息
     * <AUTHOR>
     * @date 2023/4/8 15:48
     * @return com.ce.distribution.web.config.response.WebResult<?>
     **/
    private WebResult<?> handlerParamValid(Exception exception) {
        StringBuilder message = new StringBuilder();
        //（方法参数校验异常）如实体类中的@Size注解配置和数据库中该字段的长度不统一等问题
        if (exception instanceof ConstraintViolationException) {
            Set<ConstraintViolation<?>> violations = ((ConstraintViolationException) exception).getConstraintViolations();
            if (violations != null) {
                violations.forEach(item -> message.append(item.getMessage()).append(UtilConstant.DATA_SEPARATOR));
            }
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, message.substring(0, message.length() - 1));
        }
        //（参数绑定异常）
        if (exception instanceof BindException) {
            List<FieldError> fieldErrorList = ((BindException) exception).getFieldErrors();
            fieldErrorList.forEach(item -> message.append(item.getDefaultMessage()).append(UtilConstant.DATA_SEPARATOR));
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, message.substring(0, message.length() - 1));
        }
        //（Bean 校验异常）
        if (exception instanceof MethodArgumentNotValidException) {
            List<FieldError> fieldErrorList = ((MethodArgumentNotValidException) exception).getBindingResult().getFieldErrors();
            fieldErrorList.forEach(item -> message.append(item.getDefaultMessage()).append(UtilConstant.DATA_SEPARATOR));
            return WebResult.error(WebCodeMessageEnum.REQUEST_PARAM_EXCEPTION, message.substring(0, message.length() - 1));
        }
        return WebResult.success();
    }
}
