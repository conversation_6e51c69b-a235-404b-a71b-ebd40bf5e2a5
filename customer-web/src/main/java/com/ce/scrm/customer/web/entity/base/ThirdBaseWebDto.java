package com.ce.scrm.customer.web.entity.base;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 三方必传参数
 * <AUTHOR>
 * @date 2023/5/16 16:48
 * @version 1.0.0
 */
@Data
public class ThirdBaseWebDto implements Serializable {

    /**
     * 来源key
     */
    @NotBlank(message = "来源key不能为空")
    private String sourceKey;

    /**
     * 来源密钥
     */
    @NotBlank(message = "来源密钥不能为空")
    private String sourceSecret;
}