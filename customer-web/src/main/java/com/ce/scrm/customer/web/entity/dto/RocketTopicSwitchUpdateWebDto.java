package com.ce.scrm.customer.web.entity.dto;

import com.ce.scrm.customer.web.entity.base.ThirdBaseWebDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * rocket topic switch
 * <AUTHOR>
 * @date 2023/4/8 14:41
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RocketTopicSwitchUpdateWebDto extends ThirdBaseWebDto {

    /**
     * topic 名称
     */
    private String topic;

    /**
     * topic 状态
     * 0:rabbitmq
     * 1:rocketmq
     */
    private Integer status;
}