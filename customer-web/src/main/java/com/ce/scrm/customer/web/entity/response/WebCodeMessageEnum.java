package com.ce.scrm.customer.web.entity.response;

/**
 * 返回码枚举类
 * <AUTHOR>
 * @date 2021/05/28 下午3:39
 * @version 1.0.0
 */
public enum WebCodeMessageEnum {
    //成功返回码
    REQUEST_SUCCESS("200", "请求成功"),
    //会员登录异常，特殊处理，兼容之前的校验码
    //异常返回码，从100001开始
    SERVER_INTERNAL_EXCEPTION("100001", "网络超时，请稍后重试哦～"),
    REQUEST_METHOD_EXCEPTION("100002", "请求方式异常"),
    REQUEST_ADDRESS_EXCEPTION("100003", "请求地址不存在"),
    REQUEST_PARAM_EXCEPTION("100004", "请求参数异常"),
    RPC_EXCEPTION("100005", "网络连接超时，请稍后重试哦～"),
    REQUEST_PARAM_NOT_NULL("100006", "请求参数不能为空"),
    REQUEST_DUBBO_GENERIC_PASSWORD_ERROR("100007", "请确认当前接口的正确性"),
    ;

    private final String code;
    private final String msg;

    WebCodeMessageEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
