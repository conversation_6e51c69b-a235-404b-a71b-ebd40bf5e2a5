package com.ce.scrm.customer.web.service;

import com.ce.scrm.customer.web.entity.dto.ThirdContactPersonAddWebDto;
import com.ce.scrm.customer.web.entity.dto.ThirdContactPersonQueryWebDto;
import com.ce.scrm.customer.web.entity.dto.ThirdContactPersonUpdateWebDto;
import com.ce.scrm.customer.web.entity.dto.ThirdCustomerQueryWebDto;
import com.ce.scrm.customer.web.entity.response.WebResult;
import com.ce.scrm.customer.web.entity.view.ThirdContactPersonAddWebView;
import com.ce.scrm.customer.web.entity.view.ThirdContactPersonWebView;

import java.util.List;

/**
 * 三方调用业务接口
 * <AUTHOR>
 * @date 2023/4/8 14:45
 * @version 1.0.0
 */
public interface IThirdService {

    /**
     * 获取客户下联系人数据
     * @param thirdCustomerQueryWebDto   查询参数
     * <AUTHOR>
     * @date 2023/5/16 16:57
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.ContactPersonWebView>>
     **/
    WebResult<List<ThirdContactPersonWebView>> getCustomerContactPersonData(ThirdCustomerQueryWebDto thirdCustomerQueryWebDto);

    /**
     * 获取联系人数据
     * @param thirdContactPersonQueryWebDto   查询参数
     * <AUTHOR>
     * @date 2023/5/16 16:57
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.util.List < com.ce.scrm.customer.web.entity.view.ContactPersonWebView>>
     **/
    WebResult<List<ThirdContactPersonWebView>> getContactPersonData(ThirdContactPersonQueryWebDto thirdContactPersonQueryWebDto);

    /**
     * 添加联系人
     * @param thirdContactPersonAddWebDto   联系人数据
     * <AUTHOR>
     * @date 2023/5/16 17:18
     * @return com.ce.scrm.customer.web.entity.response.WebResult<com.ce.scrm.customer.web.entity.view.ThirdContactPersonAddWebView>
     **/
    WebResult<ThirdContactPersonAddWebView> addContactPerson(ThirdContactPersonAddWebDto thirdContactPersonAddWebDto);

    /**
     * 更新联系人
     * @param thirdContactPersonUpdateWebDto    联系人数据
     * <AUTHOR>
     * @date 2023/5/16 17:23
     * @return com.ce.scrm.customer.web.entity.response.WebResult<java.lang.Boolean>
     **/
    WebResult<Boolean> updateContactPerson(ThirdContactPersonUpdateWebDto thirdContactPersonUpdateWebDto);
}