package com.ce.scrm.customer.web.entity.dto;

import com.ce.scrm.customer.web.entity.base.ThirdBaseWebDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 三方联系人添加参数
 * <AUTHOR>
 * @date 2023/5/16 17:15
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdContactPersonAddWebDto extends ThirdBaseWebDto {

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 联系人名称
     */
    private String contactPersonName;

    /**
     * 性别：0、未知，1、男，2、女
     */
    private Number gender;

    /**
     * 部门
     */
    private String department;

    /**
     * 职位
     */
    private String position;

    /**
     * 证件类型：0、未知，1、身份证
     */
    private Number certificatesType;

    /**
     * 证件号码（证件类型非0，此字段不能为空）
     */
    private String certificatesNumber;

    /**
     * 省
     */
    private String provinceCode;

    /**
     * 市
     */
    private String cityCode;

    /**
     * 区
     */
    private String districtCode;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 手机号(多个手机号，逗号分隔)
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 微信
     */
    private String wechat;

    /**
     * 企业微信
     */
    private String wecome;

    /**
     * QQ
     */
    private String qq;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 操作人
     */
    private String operator;
}