<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>scrm-customer</artifactId>
        <groupId>com.ce.scrm.customer</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>customer-service</artifactId>
    <name>customer-service</name>
    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ce.scrm.customer</groupId>
            <artifactId>customer-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm.customer</groupId>
            <artifactId>customer-support</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-emp-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-newcustomer-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-newcustclue-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.plugin</groupId>
            <artifactId>aggs-matrix-stats-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm.extend</groupId>
            <artifactId>extend-dubbo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ce.scrm.center</groupId>
            <artifactId>center-dubbo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.ce.cesupport</groupId>
            <artifactId>appservice-gj-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>easyexcel</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>
