package com.ce.scrm.customer.service.business.entity.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class CallDetailsBusinessDto implements Serializable {

    /**
     * 保护天数筛选
     */
    private Integer dayEnum;

    /**
     * 本月打卡次数
     */
    private Integer currentMonthClockCountEnum;

    /**
     * 打卡次数筛选
     */
    private Integer clockCountEnum;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 分司id
     */
    private String subId;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 商务id
     */
    private String salerId;

    /**
     * 员工ID
     */
    private String loginEmployeeId;
    /**
     * 员工名称
     */
    private String loginEmployeeName;
    /**
     * 分司ID
     */
    private String loginSubId;
    /**
     * 分司名称
     */
    private String loginSubName;
    /**
     * 部门ID
     */
    private String loginOrgId;
    /**
     * 部门名称
     */
    private String loginOrgName;
    /**
     * 区域ID
     */
    private String loginAreaId;
    /**
     * 区域名称
     */
    private String loginAreaName;
    /**
     * 手机号
     */
    private String loginMobile;
    /**
     * 工作邮箱
     */
    private String loginWorkMail;
    /**
     * 职位
     */
    private String loginPosition;
    /**
     * 职级
     */
    private String loginJobGrade;
    /**
     * 高呈员工标记
     */
    private Boolean loginGcEmployeeFlag;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页数量
     */
    private Integer pageSize;

}
