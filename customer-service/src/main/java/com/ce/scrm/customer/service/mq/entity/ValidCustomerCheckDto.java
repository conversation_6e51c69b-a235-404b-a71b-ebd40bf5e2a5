package com.ce.scrm.customer.service.mq.entity;

import lombok.Data;

import java.util.List;

/**
 * 有效客户检查参数
 * <AUTHOR>
 * @date 2023/7/11 15:36
 * @version 1.0.0
 */
@Data
public class ValidCustomerCheckDto {
    /**
     * 客户ID
     */
    private List<String> customerIdList;
    /**
     * 精查标记
     */
    private Boolean exactQueryFlag;
    /**
     * 调用ip
     */
    private String invokeIp;
    /**
     * 调用方法
     */
    private String invokeMethod;
    /**
     * dubbo调用的traceId
     */
    private String invokeTraceId;
}