package com.ce.scrm.customer.service.business.entity.view;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 客户业务数据
 * <AUTHOR>
 * @date 2023/4/7 17:31
 * @version 1.0.0
 */
@Data
public class ContactInfoBusinessView {

    /**
     * 联系人id
     */
    private  String contactPersonId;

    /**
     * 联系方式id
     */
    private  String contactInfoId;

    /**
     * 联系类型：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
     */
    private Integer contactType;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 手机号标识：1、正常，2、黑名单，3、空号
     */
    private Integer phoneFlag;

    /**
     * 联系方式来源key
     */
    private String sourceKey;

    /**
     * 是否是签单人手机号：1、是，0、否
     */
    private Integer signatoryFlag;

    /**
     * 是否是300会员手机号：1、是，0、否
     */
    private Integer memberFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 手机号是否已验证 1是 0否
     */
    private Number phoneVerifiedFlag;

    /**
     * 手机号验证时间
     */
    private LocalDateTime phoneVerifiedTime;

}