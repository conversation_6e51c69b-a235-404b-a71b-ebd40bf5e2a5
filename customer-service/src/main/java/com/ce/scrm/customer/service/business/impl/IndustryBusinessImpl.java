package com.ce.scrm.customer.service.business.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.ce.scrm.customer.dao.entity.Industry;
import com.ce.scrm.customer.dao.service.IndustryService;
import com.ce.scrm.customer.service.business.IIndustryBusiness;
import com.ce.scrm.customer.service.business.entity.view.IndustryBusinessView;
import com.ce.scrm.customer.service.cache.entity.IndustryCacheData;
import com.ce.scrm.customer.service.cache.entity.IndustryListCacheData;
import com.ce.scrm.customer.service.cache.handler.IndustryCacheHandler;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 客户业务实现
 * <AUTHOR>
 * @date 2023/4/13 13:56
 * @version 1.0.0
 */
@Slf4j
@Service
public class IndustryBusinessImpl implements IIndustryBusiness {

    @Resource
    private IndustryCacheHandler industryCacheHandler;

    @Resource
    private IndustryService industryService;

    /**
     * 获取行业数据
     *
     * <AUTHOR>
     * @date 2023/4/13 13:58
     * @return com.ce.scrm.customer.support.business.entity.view.IndustryBusinessView
     **/
    @Override
    public List<IndustryBusinessView> getAllData() {

        IndustryListCacheData industryListCacheData = industryCacheHandler.get("");
        if (industryListCacheData == null) {
            return null;
        }

        List<IndustryCacheData> industryCacheDatalist = industryListCacheData.getList();
        List<IndustryBusinessView> industryBusinessViewList = Optional.of(industryCacheDatalist).orElse(Lists.newArrayList()).stream().map(record -> {
            IndustryBusinessView industryBusinessView = new IndustryBusinessView();
            BeanUtil.copyProperties(record, industryBusinessView);
            return industryBusinessView;
        }).collect(Collectors.toList());

        return industryBusinessViewList;
    }

    /**
     * 根据行业名称获取行业信息
     * @param name  行业名称
     * @param isFirst   是否一级行业
     * <AUTHOR>
     * @date 2024/4/28 下午1:49
     * @return com.ce.scrm.customer.service.business.entity.view.IndustryBusinessView
     **/
    @Override
    public IndustryBusinessView getIndustryByName(String name, boolean isFirst) {
        LambdaQueryChainWrapper<Industry> industryLambdaQueryChainWrapper = industryService.lambdaQuery().eq(Industry::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).eq(Industry::getName, name);
        industryLambdaQueryChainWrapper.or(isFirst, (wrapper) -> wrapper.isNull(Industry::getParentCode).eq(Industry::getParentCode, "").eq(Industry::getParentCode, "0"));
        industryLambdaQueryChainWrapper.and(!isFirst, wrapper -> wrapper.isNotNull(Industry::getParentCode).ne(Industry::getParentCode, "").ne(Industry::getParentCode, "0"));
        Industry industry = industryLambdaQueryChainWrapper.last("limit 1").one();
        IndustryBusinessView industryBusinessView = new IndustryBusinessView();
        BeanUtil.copyProperties(industry, industryBusinessView);
        return industryBusinessView;
    }
}