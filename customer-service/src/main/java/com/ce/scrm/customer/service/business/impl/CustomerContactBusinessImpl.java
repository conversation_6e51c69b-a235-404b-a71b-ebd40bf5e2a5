package com.ce.scrm.customer.service.business.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ce.scrm.customer.dao.entity.CustomerContact;
import com.ce.scrm.customer.dao.entity.OrderDefaultFlag;
import com.ce.scrm.customer.dao.service.CustomerContactService;
import com.ce.scrm.customer.service.business.IChannelSourceBusiness;
import com.ce.scrm.customer.service.business.IContactPersonBusiness;
import com.ce.scrm.customer.service.business.ICustomerContactBusiness;
import com.ce.scrm.customer.service.business.IOrderDefaultFlagBusiness;
import com.ce.scrm.customer.service.business.entity.dto.ChannelSourceBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.ContactPersonBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.CustomerContactBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.*;
import com.ce.scrm.customer.service.cache.entity.ContactInfoCacheData;
import com.ce.scrm.customer.service.cache.entity.ContactPersonCacheData;
import com.ce.scrm.customer.service.cache.entity.CustomerCacheData;
import com.ce.scrm.customer.service.cache.entity.CustomerContactCacheData;
import com.ce.scrm.customer.service.cache.handler.CustomerContactCacheHandler;
import com.ce.scrm.customer.service.enums.ContactInfoEnum;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 客户业务实现
 * <AUTHOR>
 * @date 2023/4/13 13:56
 * @version 1.0.0
 */
@Slf4j
@Service
public class CustomerContactBusinessImpl implements ICustomerContactBusiness {

    @Resource
    private CustomerContactCacheHandler customerContactCacheHandler;

    @Resource
    private CustomerContactService customerContactService;

    @Resource
    private IChannelSourceBusiness channelSourceBusiness;


    @Resource
    private IContactPersonBusiness contactPersonBusiness;

    @Resource
    private IOrderDefaultFlagBusiness orderDefaultFlagBusiness;

    @Override
    public CustomerBusinessView customerContactDetail(CustomerContactBusinessDto customerContactBusinessDto) {
        CustomerContactCacheData customerContactCacheData = customerContactCacheHandler.get(customerContactBusinessDto.getCustomerId());
        if (customerContactCacheData == null) {
            return null;
        }
        CustomerCacheData customerCacheData = customerContactCacheData.getCustomerCacheData();
        CustomerBusinessView customerBusinessView = BeanUtil.copyProperties(customerCacheData, CustomerBusinessView.class);

        List<ContactPersonCacheData> contactPersonCacheDataList = customerCacheData.getContactPersonCacheDataList();
        if (CollectionUtil.isNotEmpty(contactPersonCacheDataList)) {
            List<ContactPersonBusinessView> contactPersonBusinessViewList = new ArrayList<>();
            for (ContactPersonCacheData contactPersonCacheData : contactPersonCacheDataList) {
                ContactPersonBusinessView contactPersonBusinessView = BeanUtil.copyProperties(contactPersonCacheData, ContactPersonBusinessView.class);
                List<ContactInfoCacheData> contactInfoList = contactPersonCacheData.getContactInfoList();
                List<ContactInfoBusinessView> contactInfoBusinessViewList = Optional.of(contactInfoList).orElse(Lists.newArrayList()).stream().map(record -> {
                    ContactInfoBusinessView contactInfoBusinessView = new ContactInfoBusinessView();
                    BeanUtil.copyProperties(record, contactInfoBusinessView);
                    return contactInfoBusinessView;
                }).collect(Collectors.toList());
                contactPersonBusinessView.setContactInfoList(contactInfoBusinessViewList);
                // 设置联系人sourceTag
                String sourceKey = contactPersonBusinessView.getSourceKey();
                AtomicReference<String> sourceTag = new AtomicReference<>(sourceKey);
                if ("scrm".equals(sourceKey) && CollectionUtil.isNotEmpty(contactInfoList)) {
                    List<String> phoneList = contactInfoList.stream().filter(contactInfoCacheData -> ContactInfoEnum.mobile.getType().equals(contactInfoCacheData.getContactType())).map(ContactInfoCacheData::getContactWay).collect(Collectors.toList());
                    List<String> emailList = contactInfoList.stream().filter(contactInfoCacheData -> ContactInfoEnum.email.getType().equals(contactInfoCacheData.getContactType())).map(ContactInfoCacheData::getContactWay).collect(Collectors.toList());
                    Optional<OrderDefaultFlag> orderDefaultFlag = Optional.ofNullable(orderDefaultFlagBusiness.get(contactPersonBusinessView.getContactPersonId(), phoneList, emailList));
                    orderDefaultFlag.ifPresent(o -> sourceTag.set(sourceTag.get() + ",order"));
                }
                if (StringUtils.isNotBlank(sourceTag.get())) {
                    String[] sourceTagArray = sourceTag.get().split(",");
                    StringBuilder sbSourceTag = new StringBuilder();
                    for (int i = 0; i < sourceTagArray.length; i++) {
                        ChannelSourceBusinessDto channelSourceBusinessDto = new ChannelSourceBusinessDto();
                        channelSourceBusinessDto.setSourceKey(sourceTagArray[i]);
                        ChannelSourceBusinessView bySourceKey = channelSourceBusiness.getBySourceKey(channelSourceBusinessDto);
                        if (bySourceKey != null) {
                            sbSourceTag.append(bySourceKey.getSourceShowName());
                            if (i != sourceTagArray.length - 1) {
                                sbSourceTag.append(",");
                            }
                        }
                    }
                    contactPersonBusinessView.setSourceTag(sbSourceTag.toString());
                }
                ChannelSourceBusinessDto channelSourceBusinessDto = new ChannelSourceBusinessDto();
                channelSourceBusinessDto.setSourceKey(sourceKey);
                ChannelSourceBusinessView bySourceKey = channelSourceBusiness.getBySourceKey(channelSourceBusinessDto);
                if (bySourceKey != null) {
                    contactPersonBusinessView.setSourceKey(bySourceKey.getSourceName());
                }
                contactPersonBusinessViewList.add(contactPersonBusinessView);
            }
            customerBusinessView.setContactPersonList(contactPersonBusinessViewList);
        }
        return customerBusinessView;
    }

    /**
     * 获取客户下联系人数据
     * @param customerId    客户ID
     * <AUTHOR>
     * @date 2023/5/17 10:53
     * @return java.util.List<ContactPersonBusinessView>
     **/
    @Override
    public List<ContactPersonBusinessView> customerContactPersonDetail(String customerId) {
        CustomerContactCacheData customerContactCacheData = customerContactCacheHandler.get(customerId);
        if (customerContactCacheData == null) {
            return null;
        }
        CustomerCacheData customerCacheData = customerContactCacheData.getCustomerCacheData();
        List<ContactPersonCacheData> contactPersonCacheDataList = customerCacheData.getContactPersonCacheDataList();
        List<ContactPersonBusinessView> contactPersonBusinessViewList = new ArrayList<>();
        for (ContactPersonCacheData contactPersonCacheData : contactPersonCacheDataList) {
            ContactPersonBusinessView contactPersonBusinessView = BeanUtil.copyProperties(contactPersonCacheData, ContactPersonBusinessView.class);
            contactPersonBusinessView.setContactInfoList(
                    Optional.of(contactPersonCacheData.getContactInfoList()).orElse(Lists.newArrayList())
                            .stream().map(record -> BeanUtil.copyProperties(record, ContactInfoBusinessView.class))
                            .collect(Collectors.toList()));
            contactPersonBusinessViewList.add(contactPersonBusinessView);
        }
        return contactPersonBusinessViewList;
    }

    /**
     * 获取客户数据
     *
     * @param customerContactBusinessDto 查询参数
     * @return com.ce.scrm.customer.support.business.entity.view.CustomerBusinessDto
     * <AUTHOR>
     * @date 2023/4/13 13:35
     **/
    @Override
    public CustomerContactBusinessView customerContactByContactId(CustomerContactBusinessDto customerContactBusinessDto) {

        CustomerContact customerContact = customerContactService.lambdaQuery().eq(CustomerContact::getContactPersonId, customerContactBusinessDto.getContactPersonId())
                .eq(CustomerContact::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).last("limit 1").one();
        return BeanUtil.copyProperties(customerContact, CustomerContactBusinessView.class);
    }

    /**
     * 获取客户数据根据
     *
     * @param customerContactBusinessDto 查询参数
     * @return com.ce.scrm.customer.support.business.entity.view.CustomerBusinessDto
     * <AUTHOR>
     * @date 2023/4/13 13:35
     **/
    @Override
    public List<CustomerBusinessView> customerContactByMemberCodes(CustomerContactBusinessDto customerContactBusinessDto) {

        List<CustomerBusinessView> customerBusinessViewList = new ArrayList<>();
        List<CustomerContact> customerContactList = customerContactService.lambdaQuery().in(CustomerContact::getMemberCode, customerContactBusinessDto.getMemberCodeList())
                .eq(CustomerContact::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();

        Map<String, List<CustomerContact>> collect = customerContactList.stream().collect(Collectors.groupingBy(e -> e.getCustomerId()));
        for (Map.Entry<String, List<CustomerContact>> entry : collect.entrySet()) {
            CustomerBusinessView customerBusinessView = new CustomerBusinessView();
            String customerId = entry.getKey();
            customerBusinessView.setCustomerId(customerId);
            List<CustomerContact> customerContacts = entry.getValue();
            List<String> contactPersonIdList = customerContacts.stream().map(CustomerContact::getContactPersonId).collect(Collectors.toList());
            List<ContactPersonBusinessView> contactPersonBusinessViewList = new ArrayList<>();
            Map<String, String> personToMemberCodeMap = customerContacts.stream().collect(Collectors.toMap(CustomerContact::getContactPersonId, CustomerContact::getMemberCode, (x, y) -> x));
            for (String contactPersonId : contactPersonIdList) {
                ContactPersonBusinessDto contactPersonBusinessDto = new ContactPersonBusinessDto();
                contactPersonBusinessDto.setContactPersonId(contactPersonId);
                ContactPersonBusinessView contactPersonBusinessView = contactPersonBusiness.detail(contactPersonBusinessDto);
                if (contactPersonBusinessView == null) {
                    continue;
                }
                contactPersonBusinessView.setMemberCode(personToMemberCodeMap.getOrDefault(contactPersonId, ""));
                contactPersonBusinessViewList.add(contactPersonBusinessView);
            }
            customerBusinessView.setContactPersonList(contactPersonBusinessViewList);
            customerBusinessViewList.add(customerBusinessView);
        }
        return customerBusinessViewList;
    }
}