package com.ce.scrm.customer.service.third.invoke;

import cn.ce.cesupport.framework.base.vo.CmCustProtect;
import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.center.dubbo.api.CustomerProtectDubbo;
import com.ce.scrm.center.dubbo.entity.dto.AddProtectTimeEventDubboDto;
import com.ce.scrm.center.dubbo.entity.dto.CustomerProtectDubboDto;
import com.ce.scrm.center.dubbo.entity.response.DubboResult;
import com.ce.scrm.center.dubbo.entity.view.CustomerProtectDubboVew;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ProtectThirdService {

    @DubboReference(group = "scrm-center-api", version = "1.0.0", timeout = 10000, check = false)
    private CustomerProtectDubbo customerProtectDubbo;

    public CustomerProtectDubboVew selectCustomerById(String customerId) {

        if (StringUtils.isBlank(customerId)) {
            return null;
        }

        DubboResult<CustomerProtectDubboVew> dubboResult = customerProtectDubbo.selectCustomerById(customerId);
        if (!dubboResult.checkSuccess()) {
            log.error("selectCustomerById,错误msg={}",dubboResult.getMsg());
            return null;
        }

        return dubboResult.getData();
    }

    public Integer updateByCustId(CustomerProtectDubboDto customerProtectDubboDto) {

        if (customerProtectDubboDto == null) {
            return -1;
        }

        DubboResult<Integer> dubboResult = customerProtectDubbo.updateByCustId(customerProtectDubboDto);
        if (!dubboResult.checkSuccess()) {
            log.error("updateByCustId,错误msg={}",dubboResult.getMsg());
            return -1;
        }

        return dubboResult.getData();
    }



    public Boolean addProtectTimeByEvent(AddProtectTimeEventDubboDto addProtectTimeEventDubboDto) {

        DubboResult<Boolean> booleanDubboResult = customerProtectDubbo.addProtectTimeByEvent(addProtectTimeEventDubboDto);
        if (!booleanDubboResult.checkSuccess()) {
            log.error("addProtectTimeByEvent,msg={}",booleanDubboResult.getMsg());
            return false;
        }

        return booleanDubboResult.getData();
    }



}
