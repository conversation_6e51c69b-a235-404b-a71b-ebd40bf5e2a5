package com.ce.scrm.customer.service.business;

import com.ce.scrm.customer.service.business.entity.view.AreaBusinessView;

import java.util.List;

/**
 * 客户业务接口
 * <AUTHOR>
 * @date 2023/4/13
 * @version 1.0.0
 */
public interface IAreaBusiness {

    /**
     * 获取客户数据
     * <AUTHOR>
     * @date 2023/4/13 13:35
     **/
    List<AreaBusinessView> getAllData();

    /**
     * 根据code获取地区数据
     *
     * @param code  地区code
     * <AUTHOR>
     * @date 2023/5/15 14:28
     * @return com.ce.scrm.customer.service.business.entity.view.AreaBusinessView
     **/
    AreaBusinessView get(String code);

}