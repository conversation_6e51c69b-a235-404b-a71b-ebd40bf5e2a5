package com.ce.scrm.customer.service.third.invoke;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.service.third.entity.view.BigDataCompanyDetail;
import com.ce.scrm.extend.dubbo.api.IBigDataDubbo;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.BigDataCompanyDetailView;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 大数据三方接口
 * <AUTHOR>
 * @date 2024/1/15 11:16
 * @version 1.0.0
 */
@Slf4j
@Service
public class BigDataThirdService {

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", timeout = 10000, check = false)
    private IBigDataDubbo bigDataDubbo;

    public BigDataCompanyDetail getBigDataCustomerInfo(String companyName) {
        Transaction t = Cat.newTransaction("service", "bigdata.getCompanyDetailByName");
        t.setStatus(Transaction.SUCCESS);
        try {
            if (StringUtils.isEmpty(companyName)) {
                return null;
            }
            DubboResult<BigDataCompanyDetailView> bigDataCompanyDetailViewDubboResult = bigDataDubbo.getCompanyDetailByName(companyName);
            log.info("调用获取企业基本信息数据dubbo成功，返回值为:{}", JSON.toJSONString(bigDataCompanyDetailViewDubboResult));
            if (!bigDataCompanyDetailViewDubboResult.checkSuccess()) {
                log.warn("调用获取企业基本信息数据dubbo失败，返回值为:{}", JSON.toJSONString(bigDataCompanyDetailViewDubboResult));
                return null;
            }
            BigDataCompanyDetailView bigDataCompanyDetailView = bigDataCompanyDetailViewDubboResult.getData();
            if (bigDataCompanyDetailView == null) {
                log.warn("调用获取企业基本信息数据dubbo返回值为空，customerName: {}", companyName);
                return null;
            }
            return BeanUtil.copyProperties(bigDataCompanyDetailView, BigDataCompanyDetail.class);
        } catch (Exception e) {
            log.error("大数据接口根据名称获取企业基本信息失败，param={}", companyName, e);
            t.setStatus(e);
            Cat.logError(e);
        } finally {
            t.complete();
        }
        return null;
    }
}