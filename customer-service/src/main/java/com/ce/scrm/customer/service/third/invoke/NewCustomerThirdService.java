package com.ce.scrm.customer.service.third.invoke;

import cn.ce.cesupport.framework.base.vo.MapResultBean;
import cn.ce.cesupport.newcustomer.service.NewCustomerInfoAppService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 新客户三方业务接口
 * <AUTHOR>
 * @date 2023/7/11 14:17
 * @version 1.0.0
 */
@Slf4j
@Service
public class NewCustomerThirdService {

    @DubboReference
    private NewCustomerInfoAppService newCustomerInfoAppService;

    /**
     * 校验有效客户
     * @param customerId 客户ID
     * <AUTHOR>
     * @date 2023/7/11 15:07
     * @return boolean
     **/
    public boolean checkValidCustomer(String customerId) {
        MapResultBean mapResultBean = newCustomerInfoAppService.checkProtectedByCustIdIncludeCustPool(customerId);
        if (mapResultBean.getStatus() != 101) {
            log.error("校验客户是否有效，请求异常，返回值为:{}", JSON.toJSONString(mapResultBean));
            return false;
        }
        Object isExist = mapResultBean.getData().get("isExist");
        return isExist != null && (int) isExist == 1;
    }
}