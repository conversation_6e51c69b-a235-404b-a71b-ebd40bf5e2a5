package com.ce.scrm.customer.service.business.entity.view;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 联系人业务数据
 * <AUTHOR>
 * @date 2023/4/7 17:31
 * @version 1.0.0
 */
@Data
public class ContactPersonBusinessView {

    /**
     * 联系人id
     */
    private String contactPersonId;
    /**
     * 联系人姓名
     */
    private String contactPersonName;

    /**
     * 绑定类型：0、无，1、微信，2、企业微信
     */
    private Integer bindType;

    /**
     * 微信unionId
     */
    private String unionId;

    /**
     * 微信昵称
     */
    private String wechatNickName;

    /**
     * unionId绑定时间
     */
    private LocalDateTime unionIdBindTime;
    /**
     * 性别：1、未知，2、男，3、女
     */
    private Integer gender;
    /**
     * 职位
     */
    private String position;

    /**
     * 职位名称
     */
    private String positionName;
    /**
     * 联系人来源key
     */
    private String sourceKey;
    /**
     * 联系人来源标签，逗号分隔
     */
    private String sourceTag;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区编码
     */
    private String districtCode;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 联系人部门
     */
    private String department;
    /**
     * 证件类型
     */
    private Integer certificatesType;
    /**
     * 证件号码
     */
    private String certificatesNumber;

    /**
     * 个性标签
     */
    private String remarks;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 会员号
     */
    private String memberCode;

    /**
     * 是否是法人 1是 0否
     */
    private Integer legalPersonFlag;

    /**
     * 签单联系人 0、不是，1、临时，2、正式
     */
    private Integer signingPersonFlag;

    /**
     * 签单联系人更新时间
     */
    private LocalDateTime signingPersonUpdate;

    /**
     * 联系方式列表
     */
    private List<ContactInfoBusinessView> contactInfoList;

    /**
     * 更新人
     */
    private String operator;

}