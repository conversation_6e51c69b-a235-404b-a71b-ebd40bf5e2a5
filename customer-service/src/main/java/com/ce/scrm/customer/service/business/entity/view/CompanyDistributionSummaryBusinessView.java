package com.ce.scrm.customer.service.business.entity.view;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * description: 存续企业分布汇总表
 * @author: DD.Jiu
 * date: 2025/6/27.
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CompanyDistributionSummaryBusinessView implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 地区编码
     */
    private String areaCode;
    /**
     * 客户数量
     */
    private Integer total;
}
