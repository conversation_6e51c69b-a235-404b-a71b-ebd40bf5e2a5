package com.ce.scrm.customer.service.business;

import com.ce.scrm.customer.service.business.entity.dto.CustomerContactBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView;
import com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView;
import com.ce.scrm.customer.service.business.entity.view.CustomerContactBusinessView;

import java.util.List;

/**
 * 客户业务接口
 * <AUTHOR>
 * @date 2023/4/13
 * @version 1.0.0
 */
public interface ICustomerContactBusiness {

    /**
     * 获取客户联系人关系数据根据customerId
     * @param customerContactBusinessDto 查询参数
     * <AUTHOR>
     * @date 2023/4/13 13:35
     * @return com.ce.scrm.customer.support.business.entity.view.CustomerBusinessDto
     **/
    CustomerBusinessView customerContactDetail(CustomerContactBusinessDto customerContactBusinessDto);

    /**
     * 获取客户下联系人数据
     * @param customerId    客户ID
     * <AUTHOR>
     * @date 2023/5/17 10:53
     * @return java.util.List<ContactPersonBusinessView>
     **/
    List<ContactPersonBusinessView> customerContactPersonDetail(String customerId);

    /**
     * 获取客户联系人关系数据根据contactPersonId
     * @param customerContactBusinessDto 查询参数
     * <AUTHOR>
     * @date 2023/4/13 13:35
     * @return com.ce.scrm.customer.support.business.entity.view.CustomerContactBusinessView
     **/
    CustomerContactBusinessView customerContactByContactId(CustomerContactBusinessDto customerContactBusinessDto);

    /**
     * 获取客户联系人关系数据根据会员code列表
     * @param customerContactBusinessDto 查询参数
     * <AUTHOR>
     * @date 2023/5/5 13:35
     * @return com.ce.scrm.customer.support.business.entity.view.CustomerContactBusinessView
     **/
    List<CustomerBusinessView> customerContactByMemberCodes(CustomerContactBusinessDto customerContactBusinessDto);

}