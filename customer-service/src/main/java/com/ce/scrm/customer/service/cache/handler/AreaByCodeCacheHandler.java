package com.ce.scrm.customer.service.cache.handler;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.customer.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.customer.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.customer.dao.entity.Area;
import com.ce.scrm.customer.dao.service.AreaService;
import com.ce.scrm.customer.service.cache.entity.AreaCacheData;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 根据code获取地域数据缓存
 *
 * <AUTHOR>
 * @date 2023/5/15 14:30
 * @version 1.0.0
 **/
@Service
public class AreaByCodeCacheHandler extends AbstractStringCacheHandler<AreaCacheData> {

    @Resource
    private AreaService areaService;

    /**
     * 获取业务缓存的key
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return com.ce.scrm.customer.cache.enumeration.CacheKeyEnum
     **/
    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.AREA_BY_CODE;
    }

    /**
     * 指定对象类型
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return java.lang.Class<T>
     **/
    @Override
    protected Class<AreaCacheData> getClazz() {
        return AreaCacheData.class;
    }

    /**
     * 从数据源查询数据
     *
     * @param code 自定义key
     * @return R
     * <AUTHOR>
     * @date 2023/4/6 17:21
     **/
    @Override
    protected AreaCacheData queryDataBySource(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        Area area = areaService.lambdaQuery().eq(Area::getCode, code).eq(Area::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).one();
        if (area == null) {
            return null;
        }
        return BeanUtil.copyProperties(area, AreaCacheData.class);
    }
}