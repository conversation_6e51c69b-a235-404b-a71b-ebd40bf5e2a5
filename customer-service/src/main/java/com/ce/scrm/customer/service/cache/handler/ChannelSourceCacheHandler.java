package com.ce.scrm.customer.service.cache.handler;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.customer.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.customer.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.customer.dao.entity.ChannelSource;
import com.ce.scrm.customer.dao.service.ChannelSourceService;
import com.ce.scrm.customer.service.cache.entity.ChannelSourceCacheData;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import com.ce.scrm.customer.service.enums.ValidStateEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 渠道来源缓存处理器
 * <AUTHOR>
 * @date 2023/4/10 13:43
 * @version 1.0.0
 */
@Service
public class ChannelSourceCacheHandler extends AbstractStringCacheHandler<ChannelSourceCacheData> {

    @Resource
    private ChannelSourceService channelSourceService;

    /**
     * 获取业务缓存的key
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return com.ce.scrm.customer.cache.enumeration.CacheKeyEnum
     **/
    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.CHANNEL_SOURCE;
    }

    /**
     * 指定对象类型
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return java.lang.Class<T>
     **/
    @Override
    protected Class<ChannelSourceCacheData> getClazz() {
        return ChannelSourceCacheData.class;
    }

    /**
     * 从数据源查询数据
     *
     * @param sourceKey 渠道来源key
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return R
     **/
    @Override
    protected ChannelSourceCacheData queryDataBySource(String sourceKey) {
        ChannelSource channelSource = channelSourceService.lambdaQuery()
                .eq(ChannelSource::getSourceKey, sourceKey)
                .eq(ChannelSource::getValidState, ValidStateEnum.VALID.getCode())
                .eq(ChannelSource::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .last("limit 1").one();
        if (channelSource == null) {
            return null;
        }
        return BeanUtil.copyProperties(channelSource, ChannelSourceCacheData.class);
    }
}