package com.ce.scrm.customer.service.third.invoke;

import cn.ce.cesupport.emp.service.EmployeeAppService;
import cn.ce.cesupport.emp.service.OrgAppService;
import cn.ce.cesupport.emp.vo.EmployeeVo;
import cn.ce.cesupport.emp.vo.OrgVo;
import cn.ce.cesupport.framework.base.vo.EmployeeVO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ce.scrm.customer.service.third.entity.view.CompleteOrgInfo;
import com.ce.scrm.customer.service.third.entity.view.EmployeeThirdData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 员工三方业务
 * <AUTHOR>
 * @date 2023/5/15 16:14
 * @version 1.0.0
 */
@Service
@Slf4j
public class EmployeeThirdService {

    @DubboReference
    private EmployeeAppService employeeAppService;

    @DubboReference
    private OrgAppService orgAppService;

    /***
     * 获取员工完整数据
     * @param employeeId
     * <AUTHOR>
     * @date 2025/7/3 22:20
     * @version 1.0.0
     * @return com.ce.scrm.customer.service.third.entity.view.EmployeeThirdInfoData
    **/
    public CompleteOrgInfo getParentOrgByEmployeeId(String employeeId) {

        EmployeeVO empAndOrgByEmpId = employeeAppService.findEmpAndOrgByEmpId(employeeId);
        if (Objects.nonNull(empAndOrgByEmpId)) {
            CompleteOrgInfo completeOrgInfo = BeanUtil.copyProperties(empAndOrgByEmpId, CompleteOrgInfo.class);
            completeOrgInfo.setEmployeeId(empAndOrgByEmpId.getId());
            completeOrgInfo.setEmployeeName(empAndOrgByEmpId.getName());
            return completeOrgInfo;
        }else {
            CompleteOrgInfo completeOrgInfo = new CompleteOrgInfo();
            completeOrgInfo.setEmployeeId(employeeId);
            EmployeeVo employeeVo;
            if (StrUtil.isBlank(employeeId) || (employeeVo = employeeAppService.selectOneNofilter(employeeId)) == null) {
                return null;
            }
            String orgId = employeeVo.getOrgId();
            completeOrgInfo.setOrgId(orgId);
            List<OrgVo> orgVos = orgAppService.selectListByIdsNoStateLimit(Lists.newArrayList(orgId));
            if (!CollectionUtils.isEmpty(orgVos)) {
                OrgVo orgVo = orgVos.get(0);
                completeOrgInfo.setOrgName(orgVo.getName());
                String parentId = orgVo.getParentId();
                String type = orgVo.getType();
                List<OrgVo> parentVos = orgAppService.selectListByIdsNoStateLimit(Lists.newArrayList(parentId));
                if (!CollectionUtils.isEmpty(parentVos)) {
                    OrgVo parentDeptOrg = parentVos.get(0);
                    if ("DEPT".equals(type)){
                        completeOrgInfo.setOrgId(parentDeptOrg.getId());
                        completeOrgInfo.setOrgName(parentDeptOrg.getName());
                    }else if ("BU".equals(type)){
                        completeOrgInfo.setBuId(parentDeptOrg.getId());
                        completeOrgInfo.setBuName(parentDeptOrg.getName());
                    }
                }
            }

            OrgVo subOrg = orgAppService.selectSubByOrgId(orgId);
            if (Objects.nonNull(subOrg)) {
                completeOrgInfo.setSubId(subOrg.getId());
                completeOrgInfo.setSubName(subOrg.getName());
            }
            OrgVo areaOrg = orgAppService.selectAreaByOrgId(orgId);
            if (Objects.nonNull(areaOrg)) {
                completeOrgInfo.setAreaId(areaOrg.getId());
                completeOrgInfo.setAreaName(areaOrg.getName());
            }
            return completeOrgInfo;
        }



    }

    /**
     * 获取员工数据
     * @param employeeId    员工ID
     * <AUTHOR>
     * @date 2023/5/15 16:25
     * @return com.ce.scrm.customer.service.third.entity.view.EmployeeThirdData
     **/
    public EmployeeThirdData get(String employeeId) {
        EmployeeVo employeeVo;
        if (StrUtil.isBlank(employeeId) || (employeeVo = employeeAppService.selectOne(employeeId)) == null) {
            return null;
        }
        return BeanUtil.copyProperties(employeeVo, EmployeeThirdData.class);
    }

    /**
     * 校验手机号是否是员工绑定的手机号
     * @param mobile    手机号
     * <AUTHOR>
     * @date 2023/5/15 16:25
     * @return com.ce.scrm.customer.service.third.entity.view.EmployeeThirdData
     **/
    public boolean check(String mobile) {
        if (StringUtils.isEmpty(mobile)){
            return false;
        }
        try {
            EmployeeVo employeeVo =employeeAppService.selectOneByBindMobile(mobile);
            if (employeeVo!=null){
                return true;
            }
        }catch (TooManyResultsException tooManyResultsException){
            log.error("根据手机号查询出来的员工有多个,mobile={}",mobile);
            return true;
        }catch (Exception e){
            log.error("根据手机号查询员工失败,mobile={}",mobile);
        }
        return false;
    }
}