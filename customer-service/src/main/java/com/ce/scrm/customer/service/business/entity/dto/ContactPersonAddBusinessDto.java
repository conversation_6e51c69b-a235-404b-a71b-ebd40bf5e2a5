package com.ce.scrm.customer.service.business.entity.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 联系人添加业务参数
 * <AUTHOR>
 * @date 2023/5/15 11:38
 * @version 1.0.0
 */
@Data
public class ContactPersonAddBusinessDto {

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 联系人姓名
     */
    private String contactPersonName;

    /**
     * 性别：0、未知，1、男，2、女
     */
    private Integer gender;

    /**
     * 联系人部门
     */
    private String department;

    /**
     * 职位
     */
    private String position;

    /**
     * 证件类型
     */
    private Integer certificatesType;

    /**
     * 证件号码
     */
    private String certificatesNumber;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系人来源key
     */
    private String sourceKey;

    /**
     * 个性标签
     */
    private String remarks;

    /**
     * 更新人
     */
    private String operator;

    /**
     * 是否是法人 1是 0否
     */
    private Number legalPersonFlag;


    /**
     * 绑定类型：0、无，1、微信，2、企业微信
     */
    private Integer bindType;

    /**
     * 微信unionId
     */
    private String unionId;

    /**
     * 微信昵称
     */
    private String wechatNickName;

    /**
     * unionId绑定时间
     */
    private LocalDateTime unionIdBindTime;



    /**
     * 联系方式列表
     */
    private List<ContactInfoAddBusinessDto> contactInfoList;

    /**
     * 联系途径
     */
    @Data
    public static class ContactInfoAddBusinessDto {

        /**
         * 联系类型：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
         */
        private Integer contactType;

        /**
         * 联系方式
         */
        private String contactWay;

        /**
         * 手机号标识：1、正常，2、黑名单，3、空号
         */
        private Integer phoneFlag;

        /**
         * 备注
         */
        private String remark;

        /**
         * 手机号是否已验证 1是 0否
         */
        private Number phoneVerifiedFlag;

        /**
         * 手机号验证时间
         */
        private LocalDateTime phoneVerifiedTime;
    }
}