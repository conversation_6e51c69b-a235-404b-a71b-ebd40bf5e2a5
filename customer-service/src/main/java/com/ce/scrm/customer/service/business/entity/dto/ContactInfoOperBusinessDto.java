package com.ce.scrm.customer.service.business.entity.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 联系方式操作数据
 * <AUTHOR>
 * @date 2023/4/7 17:31
 * @version 1.0.0
 */
@Data
public class ContactInfoOperBusinessDto {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 联系方式ID
     */
    private String contactInfoId;

    /**
     * 联系人ID
     */
    private String contactPersonId;

    /**
     * 联系类型：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
     */
    private Integer contactType;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 手机号标识：1、正常，2、黑名单，3、空号
     */
    private Integer phoneFlag;

    /**
     * 联系方式来源key
     */
    private String sourceKey;

    /**
     * 是否是签单人手机号：1、是，0、否
     */
    private Integer signatoryFlag;

    /**
     * 是否是300会员手机号：1、是，0、否
     */
    private Integer memberFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标记：1、删除，0、未删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者凭证
     */
    private String creatorKey;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人凭证
     */
    private String operatorKey;

    /**
     * 更新人
     */
    private String operator;
}