package com.ce.scrm.customer.service.config;

import cn.ce.sequence.api.request.BusinessTypeEnum;
import cn.ce.sequence.api.request.SequenceIdRequest;
import cn.ce.sequence.api.response.SequenceResult;
import cn.ce.sequence.api.service.SequenceService;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.ce.scrm.customer.util.thread.core.TaskManager;
import com.ce.scrm.customer.util.thread.core.ThreadFactoryManager;
import com.ce.scrm.customer.util.thread.core.ThreadPoolManager;
import com.ce.scrm.customer.util.thread.enums.ThreadFactoryNameEnum;
import com.ce.scrm.customer.util.thread.enums.ThreadRunnerTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Deque;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.stream.IntStream;

/**
 * 唯一ID获取类
 * <AUTHOR>
 * @date 2023/5/15 13:47
 * @version 1.0.0
 **/
@Slf4j
@Service
public class UniqueIdService {

    /**
     * 获取发号器成功码
     */
    private final static int SUCCESS_CODE = 200;

    /**
     * 唯一ID最大缓存数量
     */
    private final static int UNIQUE_ID_MAX_CACHE_NUM = 100;

    /**
     * 唯一ID缓存阈值
     */
    private final static int UNIQUE_ID_CACHE_THRESHOLD_VALUE = 80;

    /**
     * 获取ID的最大数量
     */
    private final static int MAX_GET_ID_NUM = 20;

    /**
     * 唯一ID缓存池
     */
    private static final Deque<String> UNIQUE_ID_CACHE_POOL = new ConcurrentLinkedDeque<>();

    /**
     * 更新唯一ID缓存线程池
     */
    private final ThreadPoolManager updateIdCacheThreadPool = new ThreadPoolManager(ThreadRunnerTypeEnum.IO, new ThreadFactoryManager(ThreadFactoryNameEnum.INVOKE_SEQUENCE_RPC_SERVICE.toString(), true));

    /**
     * 更新唯一ID任务
     */
    private final static String UPDATE_UNIQUE_ID_RUNNER = "更新唯一ID";

    @Resource
    private SequenceService sequenceService;

    @PostConstruct
    public void init() {
        //初始化缓存的数量
        checkCacheNum();
    }

    /**
     * 生成单个id
     * <AUTHOR>
     * @date 2023/5/15 13:49
     * @return java.lang.String
     **/
    public String getId() {
        String id;
        if (CollectionUtil.isEmpty(UNIQUE_ID_CACHE_POOL) || UNIQUE_ID_CACHE_POOL.size() < 1) {
            getUniqueId();
        }
        if (StringUtils.isBlank(id = UNIQUE_ID_CACHE_POOL.removeFirst())) {
            log.error("获取唯一ID失败，分布式ID不能为空");
            throw new RuntimeException("分布式ID不能为空");
        }
        checkCacheNum();
        return id;
    }

    /**
     * 生成多个id
     * @param size  生成数量
     * <AUTHOR>
     * @date 2023/5/15 13:49
     * @return java.util.List<java.lang.String>
     **/
    public List<String> getIdList(int size) {
        if (size < 1) {
            log.error("获取唯一ID失败，参数必须为自然数");
            throw new RuntimeException("获取唯一ID失败，参数必须为自然数");
        }
        if (size >= MAX_GET_ID_NUM) {
            log.error("获取唯一ID失败，数量超过获取ID最大数量");
            throw new RuntimeException("获取唯一ID失败，数量超过获取ID最大数量");
        }
        if (CollectionUtil.isEmpty(UNIQUE_ID_CACHE_POOL) || UNIQUE_ID_CACHE_POOL.size() < size) {
            getUniqueId();
        }
        List<String> idList = new ArrayList<>(size);
        IntStream.range(0, size).boxed().forEach(integer -> idList.add(UNIQUE_ID_CACHE_POOL.removeFirst()));
        checkCacheNum();
        return idList;
    }

    /**
     * 检查缓存数量
     *
     * <AUTHOR>
     * @date 2023/5/15 13:50
     **/
    private void checkCacheNum() {
        if (UNIQUE_ID_CACHE_POOL.size() > UNIQUE_ID_CACHE_THRESHOLD_VALUE) {
            return;
        }
        updateIdCacheThreadPool.addExecuteTask(new TaskManager(UPDATE_UNIQUE_ID_RUNNER, this::getUniqueId));
    }

    /**
     * 获取唯一ID
     *
     * <AUTHOR>
     * @date 2023/5/15 13:50
     **/
    private void getUniqueId() {
        if (UNIQUE_ID_CACHE_POOL.size() > UNIQUE_ID_CACHE_THRESHOLD_VALUE) {
            return;
        }
        synchronized (this) {
            if (UNIQUE_ID_CACHE_POOL.size() > UNIQUE_ID_CACHE_THRESHOLD_VALUE) {
                return;
            }
            log.info("调用rpc获取分布式ID，获取数量为:{}", UNIQUE_ID_MAX_CACHE_NUM - UNIQUE_ID_CACHE_POOL.size());
            SequenceResult<List<Long>> listSequenceResult = sequenceService.batchGenerateId(new SequenceIdRequest(BusinessTypeEnum.distribution.getCode(), UNIQUE_ID_MAX_CACHE_NUM - UNIQUE_ID_CACHE_POOL.size()));
            //log.info("调用rpc获取分布式ID，结果为:{}", JSON.toJSONString(listSequenceResult));
            if (listSequenceResult.getStatus() != SUCCESS_CODE) {
                log.error("调用发号器失败:{}", listSequenceResult.getMessage());
                return;
            }
            listSequenceResult.getData().stream().filter(Objects::nonNull).map(String::valueOf).forEach(UNIQUE_ID_CACHE_POOL::addLast);
        }
    }
}
