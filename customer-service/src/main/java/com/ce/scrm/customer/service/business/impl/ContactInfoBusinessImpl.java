package com.ce.scrm.customer.service.business.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.ce.scrm.customer.dao.entity.ContactInfo;
import com.ce.scrm.customer.dao.entity.ContactPerson;
import com.ce.scrm.customer.dao.entity.CustomerContact;
import com.ce.scrm.customer.dao.service.ContactInfoService;
import com.ce.scrm.customer.dao.service.ContactPersonService;
import com.ce.scrm.customer.dao.service.CustomerContactService;
import com.ce.scrm.customer.service.business.IContactInfoBusiness;
import com.ce.scrm.customer.service.business.entity.dto.ContactInfoBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.ContactInfoBusinessView;
import com.ce.scrm.customer.service.enums.ContactInfoEnum;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 联系方式业务实现
 * <AUTHOR>
 * @date 2023/4/13 13:56
 * @version 1.0.0
 */
@Slf4j
@Service
public class ContactInfoBusinessImpl implements IContactInfoBusiness {

    @Resource
    private ContactInfoService contactInfoService;

    @Resource
    private ContactPersonService contactPersonService;

    @Resource
    private CustomerContactService customerContactService;

    /**
     * 获取联系方式数据
     * @param contactInfoBusinessDto 查询参数
     * <AUTHOR>
     * @date 2023/4/13 13:35
     * @return com.ce.scrm.customer.support.business.entity.view.ContactPersonBusinessView
     **/
    @Override
    public ContactInfoBusinessView getCustomerContactInfoData(ContactInfoBusinessDto contactInfoBusinessDto) {

        LambdaQueryChainWrapper<ContactInfo> contactInfoLambdaQueryChainWrapper = contactInfoService.lambdaQuery();

        if (StringUtils.isNotBlank(contactInfoBusinessDto.getContactInfoId())) {
            contactInfoLambdaQueryChainWrapper.eq(ContactInfo::getContactInfoId, contactInfoBusinessDto.getContactInfoId());
        }
        if (contactInfoBusinessDto.getContactType() != null) {
            contactInfoLambdaQueryChainWrapper.eq(ContactInfo::getContactType, contactInfoBusinessDto.getContactType());
        }
        if (StringUtils.isNotBlank(contactInfoBusinessDto.getContactWay())) {
            contactInfoLambdaQueryChainWrapper.eq(ContactInfo::getContactWay, contactInfoBusinessDto.getContactWay());
        }
        if(StrUtil.isNotBlank(contactInfoBusinessDto.getContactPersonId())){
            contactInfoLambdaQueryChainWrapper.eq(ContactInfo::getContactPersonId, contactInfoBusinessDto.getContactPersonId());
        }
        if(StrUtil.isNotBlank(contactInfoBusinessDto.getSourceKey())){
            contactInfoLambdaQueryChainWrapper.eq(ContactInfo::getSourceKey, contactInfoBusinessDto.getSourceKey());
        }
        contactInfoLambdaQueryChainWrapper.eq(ContactInfo::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        ContactInfo contactInfo = contactInfoLambdaQueryChainWrapper.last("limit 1").one();
        return BeanUtil.copyProperties(contactInfo, ContactInfoBusinessView.class);
    }

    /**
     * 根据联系方式和来源获取客户下联系人方式数据
     * @param customerId 客户ID
     * @param contactPersonName 联系人名称
     * @param sourceKey 来源key
     * @param phoneList 手机号列表
     * <AUTHOR>
     * @date 2023/5/17 13:02
     * @return java.util.Optional<com.ce.scrm.customer.service.business.entity.view.ContactInfoBusinessView>
     **/
    @Override
    public Optional<ContactInfoBusinessView> getCustomerContactInfoData(String customerId, String contactPersonName, String sourceKey, List<String> phoneList) {
        if("scrm".equals(sourceKey)){
            return Optional.empty();
        }
        LambdaQueryChainWrapper<ContactInfo> contactInfoLambdaQueryChainWrapper = contactInfoService.lambdaQuery().eq(ContactInfo::getSourceKey, sourceKey).eq(ContactInfo::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        //获取客户下所有联系人ID
        List<CustomerContact> customerContactList = customerContactService.lambdaQuery().eq(CustomerContact::getCustomerId, customerId).eq(CustomerContact::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();
        if (CollectionUtil.isEmpty(customerContactList)) {
            return Optional.empty();
        }
        List<String> contactPersonIdList = customerContactList.stream().map(CustomerContact::getContactPersonId).collect(Collectors.toList());
        //获取当前来源下所有联系人名称所对应的联系人
        List<ContactPerson> contactPersonList = contactPersonService.lambdaQuery().eq(ContactPerson::getContactPersonName, contactPersonName).eq(ContactPerson::getSourceKey, sourceKey).eq(ContactPerson::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();
        if (CollectionUtil.isEmpty(contactPersonList)) {
            return Optional.empty();
        }
        contactPersonIdList.addAll(contactPersonList.stream().map(ContactPerson::getContactPersonId).collect(Collectors.toList()));
        //联系人ID筛选
        contactInfoLambdaQueryChainWrapper.in(ContactInfo::getContactPersonId, contactPersonIdList.stream().distinct().collect(Collectors.toList()));
        //手机号筛选
        if (CollectionUtil.isNotEmpty(phoneList)) {
            contactInfoLambdaQueryChainWrapper.eq(ContactInfo::getContactType, ContactInfoEnum.mobile.getType()).in(ContactInfo::getContactWay, phoneList);
        }
        List<ContactInfo> contactInfoList = contactInfoLambdaQueryChainWrapper.list();
        if (CollectionUtil.isEmpty(contactInfoList)) {
            return Optional.empty();
        }
        return contactInfoList.stream().sorted(Comparator.comparing(ContactInfo::getUpdateTime).reversed()).map(contactInfo -> BeanUtil.copyProperties(contactInfo, ContactInfoBusinessView.class)).findFirst();
    }

    /**
     * 获取联系方式列表数据
     *
     * @param contactInfoBusinessDto 查询参数
     * @return com.ce.scrm.customer.support.business.entity.view.ContactPersonBusinessView
     * <AUTHOR>
     * @date 2023/4/13 13:35
     **/
    @Override
    public List<ContactInfoBusinessView> detailList(ContactInfoBusinessDto contactInfoBusinessDto) {

        LambdaQueryChainWrapper<ContactInfo> contactInfoLambdaQueryChainWrapper = contactInfoService.lambdaQuery();

        if (StringUtils.isNotBlank(contactInfoBusinessDto.getContactPersonId())) {
            contactInfoLambdaQueryChainWrapper.eq(ContactInfo::getContactPersonId, contactInfoBusinessDto.getContactPersonId());
        }
        if (StringUtils.isNotBlank(contactInfoBusinessDto.getContactInfoId())) {
            contactInfoLambdaQueryChainWrapper.eq(ContactInfo::getContactInfoId, contactInfoBusinessDto.getContactInfoId());
        }
        if (contactInfoBusinessDto.getContactType() != null) {
            contactInfoLambdaQueryChainWrapper.eq(ContactInfo::getContactType, contactInfoBusinessDto.getContactType());
        }
        if (StringUtils.isNotBlank(contactInfoBusinessDto.getContactWay())) {
            contactInfoLambdaQueryChainWrapper.eq(ContactInfo::getContactWay, contactInfoBusinessDto.getContactWay());
        }
        if (CollectionUtils.isNotEmpty(contactInfoBusinessDto.getContactWayList())) {
            contactInfoLambdaQueryChainWrapper.in(ContactInfo::getContactWay, contactInfoBusinessDto.getContactWayList());
        }
        if (CollectionUtil.isNotEmpty(contactInfoBusinessDto.getContactTypeList())) {
            contactInfoLambdaQueryChainWrapper.in(ContactInfo::getContactType, contactInfoBusinessDto.getContactTypeList());
        }
        contactInfoLambdaQueryChainWrapper.eq(ContactInfo::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        List<ContactInfo> contactInfoList = contactInfoLambdaQueryChainWrapper.list();

        List<ContactInfoBusinessView> contactInfoBusinessViewList = Optional.of(contactInfoList).orElse(Lists.newArrayList()).stream().map(record -> {
            ContactInfoBusinessView contactInfoBusinessView = new ContactInfoBusinessView();
            BeanUtil.copyProperties(record, contactInfoBusinessView);
            return contactInfoBusinessView;
        }).collect(Collectors.toList());
        return contactInfoBusinessViewList;
    }
}