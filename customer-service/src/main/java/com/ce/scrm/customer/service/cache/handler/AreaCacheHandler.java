package com.ce.scrm.customer.service.cache.handler;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.customer.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.customer.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.customer.dao.entity.Area;
import com.ce.scrm.customer.dao.service.AreaService;
import com.ce.scrm.customer.service.cache.entity.AreaCacheData;
import com.ce.scrm.customer.service.cache.entity.AreaListCacheData;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 地区缓存处理器
 * <AUTHOR>
 * @date 2023/4/13 14:27
 * @version 1.0.0
 */
@Service
public class AreaCacheHandler extends AbstractStringCacheHandler<AreaListCacheData> {

    @Resource
    private AreaService areaService;

    /**
     * 获取业务缓存的key
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return com.ce.scrm.customer.cache.enumeration.CacheKeyEnum
     **/
    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.AREA;
    }

    /**
     * 指定对象类型
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return java.lang.Class<T>
     **/
    @Override
    protected Class<AreaListCacheData> getClazz() {
        return AreaListCacheData.class;
    }

    /**
     * 获取地区缓存数据
     * @param sourceKey
     * <AUTHOR>
     * @date 2023/4/13 14:06
     * @return com.ce.scrm.customer.cache.CustomerCacheData
     **/
    @Override
    protected AreaListCacheData queryDataBySource(String sourceKey) {
        List<Area> areaList = areaService.lambdaQuery().eq(Area::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();
        List<AreaCacheData> areaCacheDataList = Optional.of(areaList).orElse(Lists.newArrayList()).stream().map(record->{
            AreaCacheData industryCacheData = new AreaCacheData();
            BeanUtil.copyProperties(record,industryCacheData);
            return industryCacheData;
        }).collect(Collectors.toList());
        AreaListCacheData areaListCacheData = new AreaListCacheData();
        areaListCacheData.setList(areaCacheDataList);
        return areaListCacheData;
    }
}