package com.ce.scrm.customer.service.business.entity.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/6 14:12:20
 * @desc 和binlog的客户数据交换数据，数据要保持原始数据类型
 */
@Data
public class CustomerESTagBusinessDto {
    /**
     * 客户标签Code
     */
    private String tagCode;
}