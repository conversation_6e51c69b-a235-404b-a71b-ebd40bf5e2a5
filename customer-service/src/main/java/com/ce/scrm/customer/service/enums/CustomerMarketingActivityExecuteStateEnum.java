package com.ce.scrm.customer.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * @version 1.0
 * @Description: 跨境ABM 客户营销活动的执行状态
 * @Author: xukang
 * @Date: 2025/7/10 15:26
 */
@Getter
@AllArgsConstructor
public enum CustomerMarketingActivityExecuteStateEnum {
	// 新创建的活动默认待执行
    WAIT_EXECUTE(0,"待执行"),
    EXECUTING(1,"执行中"),
	EXECUTE_SUCCESS(2,"执行成功"),
	EXECUTE_FAILED(3,"执行失败")
    ;

    /** 编码 */
    private final Integer code;
    /** 值 */
    private final String value;

	/**
	 * 获取营销活动的执行状态
	 * @param code 执行状态编码
	 * @return 枚举值
	 */
	public static CustomerMarketingActivityExecuteStateEnum of(Integer code) {
		Objects.requireNonNull(code, "enum code not allow null");
		return Stream.of(values())
			.filter(C -> C.getCode().equals(code))
			.findAny()
			.orElse(null);
	}

}