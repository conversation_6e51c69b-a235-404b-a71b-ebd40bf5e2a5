package com.ce.scrm.customer.service.cache.handler;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.customer.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.customer.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.customer.dao.entity.ContactInfo;
import com.ce.scrm.customer.dao.entity.ContactPerson;
import com.ce.scrm.customer.dao.service.ContactInfoService;
import com.ce.scrm.customer.dao.service.ContactPersonService;
import com.ce.scrm.customer.service.cache.entity.ContactInfoCacheData;
import com.ce.scrm.customer.service.cache.entity.ContactPersonCacheData;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 联系人缓存处理器
 * <AUTHOR>
 * @date 2023/4/13 14:27
 * @version 1.0.0
 */
@Service
public class ContactPersonCacheHandler extends AbstractStringCacheHandler<ContactPersonCacheData> {

    @Resource
    private ContactPersonService contactPersonService;

    @Resource
    private ContactInfoService contactInfoService;

    /**
     * 获取业务缓存的key
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return com.ce.scrm.customer.cache.enumeration.CacheKeyEnum
     **/
    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.CONTACT_PERSON;
    }

    /**
     * 指定对象类型
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return java.lang.Class<T>
     **/
    @Override
    protected Class<ContactPersonCacheData> getClazz() {
        return ContactPersonCacheData.class;
    }

    /**
     * 获取联系人数据
     *
     * @param sourceKey
     * <AUTHOR>
     * @date 2023/4/13 14:06
     * @return com.ce.scrm.customer.cache.CustomerCacheData
     **/
    @Override
    protected ContactPersonCacheData queryDataBySource(String sourceKey) {
        ContactPerson contactPerson = contactPersonService.lambdaQuery()
                .eq(ContactPerson::getContactPersonId, sourceKey)
                .eq(ContactPerson::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .last("limit 1").one();

        if(contactPerson == null){
            return null;
        }

        List<ContactInfo> contactInfoList = contactInfoService.lambdaQuery()
                .eq(ContactInfo::getContactPersonId, contactPerson.getContactPersonId())
                .eq(ContactInfo :: getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();
        List<ContactInfoCacheData> contactInfoCacheDataList = Optional.of(contactInfoList).orElse(Lists.newArrayList()).stream().map(record->{
            ContactInfoCacheData contactInfoCacheData = new ContactInfoCacheData();
            BeanUtil.copyProperties(record,contactInfoCacheData);
            return contactInfoCacheData;
        }).collect(Collectors.toList());
        ContactPersonCacheData contactPersonCacheData = BeanUtil.copyProperties(contactPerson, getClazz());
        contactPersonCacheData.setPositionName(ServiceConstant.POSITION_MAPPING.getOrDefault(contactPersonCacheData.getPosition(), contactPersonCacheData.getPosition()));
        contactPersonCacheData.setContactInfoList(contactInfoCacheDataList);
        return contactPersonCacheData;
    }
}