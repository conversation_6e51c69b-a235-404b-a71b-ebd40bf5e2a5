package com.ce.scrm.customer.service.business.impl.abm;

import cn.ce.cesupport.framework.base.enums.CommonYesNoEnum;
import com.ce.scrm.customer.dao.entity.abm.CustomerMarketingActivities;
import com.ce.scrm.customer.dao.entity.abm.CustomerMarketingActivitiesRecord;
import com.ce.scrm.customer.dao.service.abm.CustomerMarketingActivitiesRecordService;
import com.ce.scrm.customer.dao.service.abm.CustomerMarketingActivitiesService;
import com.ce.scrm.customer.service.business.ICustomerMarketingActivitiesRecordBusiness;
import com.ce.scrm.customer.service.enums.CustomerMarketingActivityExecuteStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 跨境ABM 客户营销活动
 * <AUTHOR>
 * @date 2025/7/9 14:56
 * @version 1.0.0
 */
@Slf4j
@Service
public class CustomerMarketingActivitiesRecordBusinessImpl implements ICustomerMarketingActivitiesRecordBusiness {

	@Resource
	private CustomerMarketingActivitiesRecordService activitiesRecordService;
	@Resource
	private CustomerMarketingActivitiesService activitiesService;


	/**
	 * 执行活动
	 * <p>
	 *     哪个活动/哪个客户/哪个联系人/哪种联系方式 给对应的人进行触达， 执行后填充执行时间，修改执行状态
	 * </p>
	 * @param
	 * @return 是否执行成功
	 */
	@Transactional
	@Override
	public Boolean executeActivities(Long activityId) {
		List<CustomerMarketingActivitiesRecord> customerListByActivityId = getCustomerListByActivityId(activityId);
		CustomerMarketingActivities one = activitiesService.lambdaQuery().eq(CustomerMarketingActivities::getId, activityId).one();
		if (Objects.isNull(one)) {
			throw new RuntimeException("执行活动，未查询到活动信息，活动id=" + activityId);
		}
		one.setExecuteState(CustomerMarketingActivityExecuteStateEnum.EXECUTING.getCode());
		activitiesService.updateById(one);
		log.info("开始执行活动，活动id={}, 预计触达人数={}", activityId, customerListByActivityId.size());
		// TODO xk 对接第三方接口 发短信、邮件，发完、后修改 customer_marketing_activities_record.is_execute
		return false;
	}


	/**
	 * 下发后(创建保护关系后)，修改下发状态
	 * @param customerId 客户id
	 * @return 是否下发成功
	 */
	@Override
	public Boolean updateDistributeState(String customerId) {
		boolean updateRs = activitiesRecordService.lambdaUpdate()
			.eq(CustomerMarketingActivitiesRecord::getIsDistribute, CommonYesNoEnum.no_0.getValue())
			.eq(CustomerMarketingActivitiesRecord::getCustomerId, customerId)
			.set(CustomerMarketingActivitiesRecord::getIsDistribute, CommonYesNoEnum.yes_1.getValue()).update();
		log.info("当前客户下发完成，修改下发状态。customerId={}, 修改结果={}", customerId, updateRs);
		return updateRs;
	}

	/**
	 * 根据活动id获取参与活动的客户列表
	 */
	private List<CustomerMarketingActivitiesRecord> getCustomerListByActivityId(Long activityId) {
		if (activityId == null) {
			return Collections.emptyList();
		}
		return activitiesRecordService.lambdaQuery()
			.eq(CustomerMarketingActivitiesRecord::getActivityId, activityId)
			.eq(CustomerMarketingActivitiesRecord::getIsExecute, CommonYesNoEnum.no_0.getLable())
			.eq(CustomerMarketingActivitiesRecord::getIsDistribute, CommonYesNoEnum.no_0.getLable())
			.list();
	}
}