package com.ce.scrm.customer.service.business.entity.dto;

import lombok.Data;

import java.util.List;

/**
 * 联系人业务数据
 * <AUTHOR>
 * @date 2023/4/7 17:31
 * @version 1.0.0
 */
@Data
public class ContactInfoBusinessDto {

    /**
     * 联系方式id
     */
    private String contactInfoId;

    /**
     * 联系类型：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
     */
    private Integer contactType;

    /**
     * 联系类型：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
     */
    private List<Integer> contactTypeList;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 联系方式
     */
    private List<String> contactWayList;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 来源key
     */
    private String sourceKey;

}