package com.ce.scrm.customer.service.enums;

/**
 * 联系人绑定类型
 * <AUTHOR>
 * @date 2023/7/21 10:53
 * @version 1.0.0
 */
public enum BindTypeEnum {
    /**
     * 无
     */
    INIT(0),
    /**
     * 微信
     */
    WECHAT(1),

    /**
     * 企业微信
     */
    WECOME(2),
    ;

    private final int type;

    BindTypeEnum(int type) {
        this.type = type;
    }

    /**
     * 校验类型是否规范
     * @param type  传入类型
     * <AUTHOR>
     * @date 2023/7/21 10:57
     * @return boolean
     **/
    public static boolean check(Integer type) {
        if (type == null) {
            return false;
        }
        for (BindTypeEnum bindTypeEnum : BindTypeEnum.values()) {
            if (type.equals(bindTypeEnum.type)) {
                return true;
            }
        }
        return false;
    }

    public int getType() {
        return type;
    }
}
