package com.ce.scrm.customer.service.business.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.ce.scrm.customer.dao.entity.RocketTopicSwitch;
import com.ce.scrm.customer.dao.service.RocketTopicSwitchService;
import com.ce.scrm.customer.service.business.IRocketTopicSwitchBusiness;
import com.ce.scrm.customer.service.business.entity.dto.RocketTopicSwitchBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.RocketTopicSwitchBusinessView;
import com.ce.scrm.customer.service.cache.entity.RocketTopicSwitchCacheData;
import com.ce.scrm.customer.service.cache.handler.RocketTopicSwitchCacheHandler;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/7/10 18:22
 */
@Slf4j
@Service
public class RocketTopicSwitchBusinessImpl implements IRocketTopicSwitchBusiness {

    @Resource
    RocketTopicSwitchCacheHandler rocketTopicSwitchCacheHandler;
    @Resource
    RocketTopicSwitchService rocketTopicSwitchService;

    @Override
    public Integer getTopicSwitchByTopicName(String topicName) {
        if(StringUtils.isEmpty(topicName)){
            return null;
        }
        RocketTopicSwitchCacheData rocketTopicSwitchCacheData = rocketTopicSwitchCacheHandler.get(topicName);
        if (rocketTopicSwitchCacheData==null){
            return null;
        }
        return rocketTopicSwitchCacheData.getStatus();
    }

    @Override
    public List<RocketTopicSwitchBusinessView> getTopicSwitchByConditation(RocketTopicSwitchBusinessDto rocketTopicSwitchBusinessDto) {
        List<RocketTopicSwitch> rocketTopicSwitchList = rocketTopicSwitchService.lambdaQuery()
                .eq(StringUtils.isNotBlank(rocketTopicSwitchBusinessDto.getTopic()),RocketTopicSwitch::getTopic,rocketTopicSwitchBusinessDto.getTopic())
                .eq(rocketTopicSwitchBusinessDto.getStatus()!=null,RocketTopicSwitch::getStatus,rocketTopicSwitchBusinessDto.getStatus())
                .list();
        List<RocketTopicSwitchBusinessView> viewList = Optional.of(rocketTopicSwitchList).orElse(Lists.newArrayList()).stream().map(record->{
            RocketTopicSwitchBusinessView view = BeanUtil.copyProperties(record, RocketTopicSwitchBusinessView.class);
            return view;
        }).collect(Collectors.toList());
        return viewList;
    }

    @Override
    public Boolean update(RocketTopicSwitchBusinessDto rocketTopicSwitchBusinessDto) {
        RocketTopicSwitchCacheData rocketTopicSwitchCacheData = rocketTopicSwitchCacheHandler.get(rocketTopicSwitchBusinessDto.getTopic());
        if (rocketTopicSwitchCacheData==null){
            return false;
        }
        LambdaUpdateChainWrapper<RocketTopicSwitch> rocketTopicSwitchLambdaUpdateChainWrapper = rocketTopicSwitchService.lambdaUpdate();
        rocketTopicSwitchLambdaUpdateChainWrapper.eq(RocketTopicSwitch::getId,rocketTopicSwitchCacheData.getId());
        boolean update = rocketTopicSwitchLambdaUpdateChainWrapper.set(RocketTopicSwitch::getStatus, rocketTopicSwitchBusinessDto.getStatus()).update();
        rocketTopicSwitchCacheHandler.del(rocketTopicSwitchBusinessDto.getTopic());
        return update;
    }
}
