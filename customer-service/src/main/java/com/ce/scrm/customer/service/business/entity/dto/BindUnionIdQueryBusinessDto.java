package com.ce.scrm.customer.service.business.entity.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/11/25
 */
public class BindUnionIdQueryBusinessDto implements Serializable {
    /**
     * 员工ID
     */
    private List<String> empIdList;

    /**
     * unionId绑定开始时间
     */
    private LocalDateTime unionIdBindBeginTime;


    /**
     * unionId绑定结束时间
     */
    private LocalDateTime unionIdBindEndTime;

    private Integer pageNum;

    private Integer pageSize;

    public List<String> getEmpIdList() {
        return empIdList;
    }

    public void setEmpIdList(List<String> empIdList) {
        this.empIdList = empIdList;
    }

    public LocalDateTime getUnionIdBindBeginTime() {
        return unionIdBindBeginTime;
    }

    public void setUnionIdBindBeginTime(LocalDateTime unionIdBindBeginTime) {
        this.unionIdBindBeginTime = unionIdBindBeginTime;
    }

    public LocalDateTime getUnionIdBindEndTime() {
        return unionIdBindEndTime;
    }

    public void setUnionIdBindEndTime(LocalDateTime unionIdBindEndTime) {
        this.unionIdBindEndTime = unionIdBindEndTime;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
