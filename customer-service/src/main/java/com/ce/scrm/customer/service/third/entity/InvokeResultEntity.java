package com.ce.scrm.customer.service.third.entity;

import java.io.Serializable;

/**
 * 返回包装体
 * <AUTHOR>
 * @date 2021/05/28 下午3:38
 * @version 1.0.0
 */
public class InvokeResultEntity<T> implements Serializable {

    private static final String REQUEST_SUCCESS = "200";

    private String msg;
    private final String code;
    private T data;

    private InvokeResultEntity(T data) {
        this.code = REQUEST_SUCCESS;
        this.msg = "请求成功";
        this.data = data;
    }

    private InvokeResultEntity(T data, String msg) {
        this.code = REQUEST_SUCCESS;
        this.msg = msg;
        this.data = data;
    }

    private InvokeResultEntity(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static <T> InvokeResultEntity<T> success(T data) {
        return new InvokeResultEntity<>(data);
    }

    public static <T> InvokeResultEntity<T> success(T data, String msg) {
        return new InvokeResultEntity<>(data, msg);
    }

    public static <T> InvokeResultEntity<T> success() {
        return success(null);
    }

    public static <T> InvokeResultEntity<T> error(String code, String msg) {
        return new InvokeResultEntity<>(code, msg);
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getMsg() {
        return this.msg;
    }

    public String getCode() {
        return this.code;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Boolean checkSuccess() {
        return REQUEST_SUCCESS.equals(this.code);
    }
}
