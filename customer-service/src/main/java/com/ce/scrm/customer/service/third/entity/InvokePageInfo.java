package com.ce.scrm.customer.service.third.entity;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 调用三方rpc返回分页信息
 *
 * <AUTHOR>
 * @date 2021-12-30 22:53
 * @version 1.0.0
 **/
@Data
public class InvokePageInfo<T> implements Serializable {

    private static final long serialVersionUID = -6455234157678276815L;
    protected long total;
    private long pageNum;
    private long pageSize;
    private long pages;

    protected List<T> list;

    /**
     * mybatis-plus分页转换
     *
     * @param page mybatis-plus分页信息
     * @return com.wuyi.cicd.server.config.response.InvokePageInfo<N>
     * <AUTHOR>
     * @date 2022/6/27 11:36
     **/
    public static <O, N> InvokePageInfo<N> pageConversion(IPage<O> page) {
        InvokePageInfo<N> pageInfo = new InvokePageInfo<>();
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getCurrent());
        pageInfo.setPageSize(page.getSize());
        pageInfo.setPages(page.getPages());
        List<O> records = page.getRecords();
        List<N> list = new ArrayList<>();
        BeanUtils.copyProperties(records, list);
        pageInfo.setList(list);
        return pageInfo;
    }

    public static <O> InvokePageInfo<O> convertToPageInfo(IPage<O> page) {
        InvokePageInfo<O> pageInfo = new InvokePageInfo<>();
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getCurrent());
        pageInfo.setPageSize(page.getSize());
        pageInfo.setPages(page.getPages());
        List<O> records = page.getRecords();
        pageInfo.setList(records);
        return pageInfo;
    }

    public static <O, N> InvokePageInfo<N> pageConversion(IPage<O> page, Class<N> clazz) {
        InvokePageInfo<N> pageInfo = new InvokePageInfo<>();
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getCurrent());
        pageInfo.setPageSize(page.getSize());
        pageInfo.setPages(page.getPages());
        List<O> records = page.getRecords();
        List<N> list = BeanUtil.copyToList(records, clazz);
        pageInfo.setList(list);
        return pageInfo;
    }
}
