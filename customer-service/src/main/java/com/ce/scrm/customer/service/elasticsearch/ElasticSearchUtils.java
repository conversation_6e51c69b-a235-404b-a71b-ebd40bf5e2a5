package com.ce.scrm.customer.service.elasticsearch;

import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * elasticsearch 工具类
 * https://www.elastic.co/guide/en/elasticsearch/client/java-rest/6.8/java-rest-high.html
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/7/27 22:38
 */
@Slf4j
@Component
public class ElasticSearchUtils {

    @Autowired
    private RestHighLevelClient restHighLevelClient;


    /**
     * 创建索引
     * @author: wangshoufang
     * @date: 2023/7/27 23:34
     * @param indexName
     * @return java.lang.Boolean
     */
    public Boolean create(String indexName){
        CreateIndexRequest request = new CreateIndexRequest(indexName);
        try {
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(request, RequestOptions.DEFAULT);
            return createIndexResponse.isAcknowledged();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 创建索引 并且携带mapping
     * @author: wangshoufang
     * @date: 2023/7/27 23:33
     * @param indexName
     * @param mapping
     * @return java.lang.Boolean
     */
    public Boolean create(String indexName,String mapping){
        CreateIndexRequest request = new CreateIndexRequest(indexName);
        try {
            request.mapping(mapping, XContentType.JSON);
            CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(request, RequestOptions.DEFAULT);
            return createIndexResponse.isAcknowledged();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断索引是否存在
     * @author: wangshoufang
     * @date: 2023/7/27 23:33
     * @param indexName
     * @return java.lang.Boolean
     */
    public Boolean exists(String indexName){
        GetIndexRequest request = new GetIndexRequest(indexName);
        boolean exists = false;
        try {
            exists = restHighLevelClient.indices().exists(request, RequestOptions.DEFAULT);
        } catch (Exception e) {
        }
        return exists;
    }

    /**
     * 删除索引
     * @author: wangshoufang
     * @date: 2023/7/27 23:33
     * @param indexName
     * @return java.lang.Boolean
     */
    public Boolean delete(String indexName){
        DeleteIndexRequest request = new DeleteIndexRequest(indexName);
        try {
            AcknowledgedResponse deleteIndexResponse = restHighLevelClient.indices().delete(request, RequestOptions.DEFAULT);
            return deleteIndexResponse.isAcknowledged();
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 别名
     * @author: wangshoufang
     * @date: 2023/7/27 23:31
     * @param indexName
     * @param aliasName
     * @return java.lang.Boolean
     */
    public Boolean alias(String indexName,String aliasName){
        IndicesAliasesRequest request = new IndicesAliasesRequest();
        IndicesAliasesRequest.AliasActions aliasAction =
                new IndicesAliasesRequest.AliasActions(IndicesAliasesRequest.AliasActions.Type.ADD)
                        .index(indexName)
                        .alias(aliasName);
        request.addAliasAction(aliasAction);
        try {
            AcknowledgedResponse indicesAliasesResponse =
                    restHighLevelClient.indices().updateAliases(request, RequestOptions.DEFAULT);
            return indicesAliasesResponse.isAcknowledged();
        } catch (IOException e) {
            return false;
        }
    }

    public Boolean removeAfterAlias(String oldIndexName,String newIdexName,String aliasName){
        IndicesAliasesRequest request = new IndicesAliasesRequest();
        IndicesAliasesRequest.AliasActions removeAliasAction =
                new IndicesAliasesRequest.AliasActions(IndicesAliasesRequest.AliasActions.Type.REMOVE)
                        .index(oldIndexName)
                        .alias(aliasName);
        request.addAliasAction(removeAliasAction);
        IndicesAliasesRequest.AliasActions addAliasAction =
                new IndicesAliasesRequest.AliasActions(IndicesAliasesRequest.AliasActions.Type.ADD)
                        .index(newIdexName)
                        .alias(aliasName);
        request.addAliasAction(addAliasAction);
        try {
            AcknowledgedResponse indicesAliasesResponse =
                    restHighLevelClient.indices().updateAliases(request, RequestOptions.DEFAULT);
            return indicesAliasesResponse.isAcknowledged();
        } catch (IOException e) {
            return false;
        }
    }




    /**
     * 批量操作
     * @author: wangshoufang
     * @date: 2023/7/27 23:45
     * @param request
     * @return java.lang.Boolean
     */
    public Boolean bulk(BulkRequest request){
        try {
            BulkResponse bulkResponse = restHighLevelClient.bulk(request, RequestOptions.DEFAULT);
            for (BulkItemResponse bulkItemResponse : bulkResponse) {
                if (bulkItemResponse.isFailed()) {
                    BulkItemResponse.Failure failure =bulkItemResponse.getFailure();
                    log.info("批量操作失败,index={},message={}",failure.getIndex(),failure.getMessage());
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
