package com.ce.scrm.customer.service.business.entity.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 联系人操作数据
 * <AUTHOR>
 * @date 2023/4/7 17:31
 * @version 1.0.0
 */
@Data
public class ContactPersonOperBusinessDto {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 联系人姓名
     */
    private String contactPersonName;

    /**
     * 性别：0、未知，1、男，2、女
     */
    private Integer gender;

    /**
     * 联系人部门
     */
    private String department;

    /**
     * 职位
     */
    private String position;

    /**
     * 证件类型
     */
    private Integer certificatesType;

    /**
     * 证件号码
     */
    private String certificatesNumber;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 联系人来源key
     */
    private String sourceKey;

    /**
     * 联系人来源标签，逗号分隔
     */
    private String sourceTag;

    /**
     * 删除标记：1、删除，0、未删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者凭证
     */
    private String creatorKey;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人凭证
     */
    private String operatorKey;

    /**
     * 更新人
     */
    private String operator;
}