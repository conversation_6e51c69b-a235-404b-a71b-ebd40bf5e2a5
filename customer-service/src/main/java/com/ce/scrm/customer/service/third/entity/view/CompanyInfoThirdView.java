package com.ce.scrm.customer.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Description: 根据pid查询公司信息
 * @Author: lijinpeng
 * @Date: 2025/2/11 15:10
 */
@Data
public class CompanyInfoThirdView implements Serializable {

    /**
     * 搜客宝公司唯一ID
     */
    private String pid;
    /**
     * 社会信用代码
     */
    private String uncid;
    /**
     * 客户名称
     */
    private String entName;
    /**
     * ka标记
     */
    private Integer kaFlag;

    /**
     * 所属行业一级
     */
    private String firstIndustry;

    /**
     * 所属行业二级
     */
    private List<String> secondIndustry;

    /**
     * 注册资金
     */
    private String regCapUnify;

    /**
     * 成立日期
     */
    private Date esDate;

    /**
     * 地理坐标
     */
    private String location;

    /**
     * 通讯地址
     */
    private String contactAddress;

    /**
     * 省code
     */
    private String province;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市code
     */
    private String city;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区code
     */
    private String district;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 注册地址
     */
    private String regAddress;

    /**
     * 历史名称
     */
    private List<String> historyName;

}
