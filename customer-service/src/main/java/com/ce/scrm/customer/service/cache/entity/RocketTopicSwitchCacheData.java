package com.ce.scrm.customer.service.cache.entity;

import lombok.Data;

import java.util.Date;

/**
 * topic缓存数据
 * <AUTHOR>
 * @date 2023/4/13 14:01
 * @version 1.0.0
 */
@Data
public class RocketTopicSwitchCacheData {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * topic 名称
     */
    private String topic;

    /**
     * topic 状态
     * 0:rabbitmq
     * 1:rocketmq
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}