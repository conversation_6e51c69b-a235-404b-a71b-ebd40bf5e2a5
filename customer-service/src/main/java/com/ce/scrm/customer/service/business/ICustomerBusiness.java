package com.ce.scrm.customer.service.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.service.business.entity.dto.*;
import com.ce.scrm.customer.service.business.entity.view.CompanyDistributionSummaryBusinessView;
import com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView;
import com.ce.scrm.customer.service.business.entity.view.CustomerESBusinessView;
import com.ce.scrm.customer.service.third.entity.view.BigDataCompanyDetail;

import java.util.List;
import java.util.Optional;

/**
 * 客户业务接口
 * <AUTHOR>
 * @date 2023/4/13
 * @version 1.0.0
 */
public interface ICustomerBusiness {

    /**
     * 获取客户数据
     * @param customerBusinessDto   查询参数
     * <AUTHOR>
     * @date 2023/4/13 13:35
     * @return com.ce.scrm.customer.support.business.entity.view.CustomerBusinessDto
     **/
    CustomerBusinessView detail(CustomerBusinessDto customerBusinessDto);

    /**
     * 根据社会信用编码查询客户
     * @param uncid 社会信用编码
     * <AUTHOR>
     * @date 2024/2/29 09:18
     * @return java.util.Optional<com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView>
     **/
    Optional<CustomerBusinessView> detail(String uncid);

    /**
     * 获取客户数据
     * @param customerPageBusinessDto   查询参数
     * <AUTHOR>
     * @date 2023/4/13 13:35
     * @return com.ce.scrm.customer.support.business.entity.view.CustomerBusinessDto
     **/
    Page<CustomerBusinessView> pageList(CustomerPageBusinessDto customerPageBusinessDto);

    /**
     * 添加客户
     * @param customerAddBusinessDto   客户添加的数据
     * <AUTHOR>
     * @date 2023/5/25 10:28
     * @return boolean
     **/
    boolean add(CustomerAddBusinessDto customerAddBusinessDto);

    /**
     * 添加企业客户
     * @param enterpriseCustomerSaveBusinessDto 企业客户添加参数
     * <AUTHOR>
     * @date 2024/2/29 10:26
     * @return java.lang.String
     **/
    String saveEnterpriseCustomer(EnterpriseCustomerSaveBusinessDto enterpriseCustomerSaveBusinessDto);

    /**
     * 拼装搜客宝客户数据
     * @param customer  客户信息
     * @param bigDataCompanyDetail   搜客宝客户数据
     * <AUTHOR>
     * @date 2024/4/28 下午2:12
     * @return com.ce.scrm.customer.dao.entity.Customer
     **/
    Customer packageSkbCustomerData(Customer customer, BigDataCompanyDetail bigDataCompanyDetail);

    /**
     * 更新客户
     * @param customerUpdateBusinessDto   客户更新的数据
     * <AUTHOR>
     * @date 2023/5/25 11:28
     * @return boolean
     **/
    boolean update(CustomerUpdateBusinessDto customerUpdateBusinessDto);


    /**
     * elasticsearch 模糊查询
     * @author: wangshoufang
     * @date: 2023/9/11 09:05
     * @param customerPageBusinessDto
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView>
     */
    Page<CustomerBusinessView> elasticsearchMatchByCondition(CustomerPageBusinessDto customerPageBusinessDto);


    /**
     * <AUTHOR>
     * @date 2025/7/14 09:39:15
     * @return
     * @desc 从customer es库根据条件分页查询客户数据
     */
    Page<CustomerESBusinessView> customerESMatchByCondition(CustomerESPageBusinessDto customerESPageBusinessDto);

    /**
     * elasticsearch 精确查询
     * @author: wangshoufang
     * @date: 2023/9/11 09:05
     * @param customerPageBusinessDto
     * @return com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView
     */
    CustomerBusinessView elasticsearchExactByCondition(CustomerPageBusinessDto customerPageBusinessDto);

    /**
     * 获取KA客户列表
     * <AUTHOR>
     * @date 2024/1/19 21:41
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView>
     **/
    List<CustomerBusinessView> getKaCustomerList();

    /**
     * 清理客户ka标记
     *
     * @param idList 客户表主键ID列表
     * <AUTHOR>
     * @date 2024/1/22 16:14
     **/
    void cleanCustomerKaFlag(List<Long> idList);

    /**
     * 添加客户ka标记
     *
     * @param customerBusinessViewList 客户信息列表
     * <AUTHOR>
     * @date 2024/1/22 16:24
     **/
    void addCustomerKaFlag(List<CustomerBusinessView> customerBusinessViewList);


    /**
     * 更新搜客宝客户标签
     * @param customerTagUpdateBusinessDto   更新搜客宝客户标签
     * <AUTHOR>
     * @date 2023/5/25 11:28
     * @return boolean
     **/
    boolean updateCustomerSkbTag(CustomerTagUpdateBusinessDto customerTagUpdateBusinessDto);


    /**
     * 更新实例客户标签
     * @param customerTagUpdateBusinessDto   更新实例客户标签
     * <AUTHOR>
     * @date 2023/5/25 11:28
     * @return boolean
     **/
    boolean updateCustomerInstanceTag(CustomerTagUpdateBusinessDto customerTagUpdateBusinessDto);


    /**
     * 判断一个客户的联系人，是否是法人
     *
     *  @param customerId 客户ID
     *  @param contactName 联系人姓名
     *  <AUTHOR>
     *  @date 2024/9/10
     *  @return boolean 是否法人
     */
     boolean isLegalPerson(String customerId, String contactName);

    /**
     * 获取拜访率计算 统计结果
     * @param loginInfoBusinessDto
     * @param begin
     * @param end
     * @param aTrue
     * @return
     */
    Integer getCallRate(LoginInfoBusinessDto loginInfoBusinessDto, Integer begin, Integer end, Boolean aTrue);

    /**
     * 获取打卡内页列表
     * @param callDetailsBusinessDto
     * @return
     */
    Page<CustomerBusinessView> getCallDetails(CallDetailsBusinessDto callDetailsBusinessDto);

    /**
     * 监听保护关系表binlog 然后同步customer
     * @param customerUpdateBusinessDto
     * @return
     */
    Boolean monitorProtectInfoConsumer(CustomerUpdateBusinessDto customerUpdateBusinessDto);

    /**
     * 更新客户表数据 只更新CustType字段
     * @param customerUpdateBusinessDto
     * @return
     */
    Boolean updateCustomerCustType(CustomerUpdateBusinessDto customerUpdateBusinessDto);


    Page<CustomerBusinessView> qualifiedNewCustomerList(CustomerPageBusinessDto customerPageBusinessDto);

    /**
     * Description: 按地区统计 tag_menhu=1（【销管口径】门户客户）的客户数
     *              国家无地区码，用"000000”指代国家码
     * @author: JiuDD
     * @param businessDto 统计颗粒度:1=全国;2=省;3=市
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.CompanyDistributionSummaryBusinessView>
     * date: 2025/6/27 11:14
     */
    List<CompanyDistributionSummaryBusinessView> zqCompanyDistributionSummary(CompanyDistributionSummaryBusinessDto businessDto);
}