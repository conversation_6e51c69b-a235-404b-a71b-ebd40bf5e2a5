package com.ce.scrm.customer.service.business;

/**
 * 客户营销活动执行分发业务接口
 * <AUTHOR>
 * @date 2025/7/9
 * @version 1.0.0
 */
public interface ICustomerMarketingActivitiesRecordBusiness {


	/**
	 * 执行活动
	 */
	// Boolean executeActivities(CustomerMarketingActivitiesRecordExeBusinessReqDto reqDto);
	Boolean executeActivities(Long activityId);


	/**
	 * 创建保护关系后，修改下发状态
	 * @param customerId 客户id
	 * @return 是否下发成功
	 */
	Boolean updateDistributeState(String customerId);


}