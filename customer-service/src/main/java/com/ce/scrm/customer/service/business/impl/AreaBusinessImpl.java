package com.ce.scrm.customer.service.business.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.customer.service.business.IAreaBusiness;
import com.ce.scrm.customer.service.business.entity.view.AreaBusinessView;
import com.ce.scrm.customer.service.cache.entity.AreaCacheData;
import com.ce.scrm.customer.service.cache.entity.AreaListCacheData;
import com.ce.scrm.customer.service.cache.handler.AreaByCodeCacheHandler;
import com.ce.scrm.customer.service.cache.handler.AreaCacheHandler;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 客户业务实现
 * <AUTHOR>
 * @date 2023/4/13 13:56
 * @version 1.0.0
 */
@Slf4j
@Service
public class AreaBusinessImpl implements IAreaBusiness {

    @Resource
    private AreaCacheHandler areaCacheHandler;

    @Resource
    private AreaByCodeCacheHandler areaByCodeCacheHandler;

    /**
     * 获取客户数据
     *
     * <AUTHOR>
     * @date 2023/4/13 13:58
     * @return com.ce.scrm.customer.support.business.entity.view.CustomerBusinessView
     **/
    @Override
    public List<AreaBusinessView> getAllData() {

        AreaListCacheData areaListCacheData = areaCacheHandler.get("");
        if(areaListCacheData == null){
            return null;
        }

        List<AreaCacheData> areaListCacheDatalist = areaListCacheData.getList();
        List<AreaBusinessView> areaBusinessViewList = Optional.of(areaListCacheDatalist).orElse(Lists.newArrayList()).stream().map(record->{
            AreaBusinessView areaBusinessView = new AreaBusinessView();
            BeanUtil.copyProperties(record,areaBusinessView);
            return areaBusinessView;
        }).collect(Collectors.toList());

        return areaBusinessViewList;
    }

    /**
     * 根据code获取地区数据
     *
     * @param code 地区code
     * <AUTHOR>
     * @date 2023/5/15 14:28
     * @return com.ce.scrm.customer.service.business.entity.view.AreaBusinessView
     **/
    @Override
    public AreaBusinessView get(String code) {
        AreaCacheData areaCacheData = areaByCodeCacheHandler.get(code);
        if(areaCacheData == null){
            return null;
        }
        return BeanUtil.copyProperties(areaCacheData, AreaBusinessView.class);
    }
}