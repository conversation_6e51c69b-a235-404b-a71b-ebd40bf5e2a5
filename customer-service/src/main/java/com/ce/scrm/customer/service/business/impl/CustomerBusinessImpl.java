package com.ce.scrm.customer.service.business.impl;

import cn.ce.cesupport.emp.consts.EmpPositionConstant;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.annotation.*;
import com.ce.scrm.customer.service.business.ICustomerBusiness;
import com.ce.scrm.customer.service.business.IIndustryBusiness;
import com.ce.scrm.customer.service.business.entity.dto.*;
import com.ce.scrm.customer.service.business.entity.view.CompanyDistributionSummaryBusinessView;
import com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView;
import com.ce.scrm.customer.service.business.entity.view.CustomerESBusinessView;
import com.ce.scrm.customer.service.cache.entity.CustomerCacheData;
import com.ce.scrm.customer.service.cache.handler.CustomerCacheHandler;
import com.ce.scrm.customer.service.config.UniqueIdService;
import com.ce.scrm.customer.service.enums.CustomerTypeEnum;
import com.ce.scrm.customer.service.third.entity.view.BigDataCompanyDetail;
import com.ce.scrm.customer.service.third.invoke.BigDataThirdService;
import com.ce.scrm.customer.service.utils.CustomerESUtil;
import com.ce.scrm.customer.service.utils.PositionUtil;
import com.ce.scrm.extend.dubbo.api.IBigDataDubbo;
import com.ce.scrm.extend.dubbo.entity.response.DubboResult;
import com.ce.scrm.extend.dubbo.entity.view.BigDataCompanyDetailView;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 客户业务实现
 * <AUTHOR>
 * @date 2023/4/13 13:56
 * @version 1.0.0
 */
@Slf4j
@Service
public class CustomerBusinessImpl implements ICustomerBusiness {

    @Resource
    private CustomerCacheHandler customerCacheHandler;

    @Resource
    private CustomerService customerService;

    @Resource
    private BigDataThirdService bigDataThirdService;

    @Resource
    private UniqueIdService uniqueIdService;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private IIndustryBusiness industryBusiness;

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", timeout = 10000, check = false)
    private IBigDataDubbo bigDataDubbo;

    private final String customerESIndexName = "es_crm_customer";

    /**
     * 获取客户数据
     *
     * @param customerBusinessDto 查询参数
     * <AUTHOR>
     * @date 2023/4/13 13:58
     * @return com.ce.scrm.customer.support.business.entity.view.CustomerBusinessView
     **/
    @Override
    public CustomerBusinessView detail(CustomerBusinessDto customerBusinessDto) {
        CustomerCacheData customerCacheData = customerCacheHandler.get(customerBusinessDto.getCustomerId());
        if (customerCacheData == null) {
            return null;
        }
        return BeanUtil.copyProperties(customerCacheData, CustomerBusinessView.class);
    }

    /**
     * 根据客户名称查询客户
     *
     * @param customerName 客户名称
     * <AUTHOR>
     * @date 2024/2/29 09:18
     * @return java.util.Optional<com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView ?
     **/
    @Override
    public Optional<CustomerBusinessView> detail(String customerName) {
        List<Customer> customerList = customerService.lambdaQuery()
                .eq(Customer::getCustomerName, customerName)
                .orderByDesc(Customer::getCreateTime)
                .list();
        if (CollectionUtil.isEmpty(customerList)) {
            return Optional.empty();
        }
        if (customerList.size() > 1) {
            log.error("根据客户名称获取客户存在多条，客户名称为:{}", customerName);
        }
        return Optional.of(BeanUtil.copyProperties(customerList.get(0), CustomerBusinessView.class));
    }

    @Override
    public Page<CustomerBusinessView> pageList(CustomerPageBusinessDto customerPageBusinessDto) {
        LambdaQueryWrapper<Customer> customerLambdaQueryWrapper = Wrappers.lambdaQuery();
        List<String> customerIdList = customerPageBusinessDto.getCustomerIdList();
        if (CollectionUtils.isNotEmpty(customerIdList)) {
            if (customerIdList.size() > 200) {
                customerIdList = customerIdList.subList(0, 200);
            }
            customerLambdaQueryWrapper.in(Customer::getCustomerId, customerIdList);
        }

        if (StringUtils.isNotBlank(customerPageBusinessDto.getCustomerName())) {
            customerLambdaQueryWrapper.eq(Customer::getCustomerName, customerPageBusinessDto.getCustomerName());
        }

        if (StringUtils.isNotBlank(customerPageBusinessDto.getCreateTimeStart())) {
            customerLambdaQueryWrapper.ge(Customer::getCreateTime, customerPageBusinessDto.getCreateTimeStart());
        }
        if (StringUtils.isNotBlank(customerPageBusinessDto.getCreateTimeEnd())) {
            customerLambdaQueryWrapper.le(Customer::getCreateTime, customerPageBusinessDto.getCreateTimeEnd());
        }

        if (StringUtils.isNotBlank(customerPageBusinessDto.getCertificateType())) {
            customerLambdaQueryWrapper.eq(Customer::getCertificateType, Integer.valueOf(customerPageBusinessDto.getCertificateType()));
        }
        if (StringUtils.isNotBlank(customerPageBusinessDto.getCertificateCode())) {
            customerLambdaQueryWrapper.eq(Customer::getCertificateCode, customerPageBusinessDto.getCertificateCode());
        }

        if (StringUtils.isNotBlank(customerPageBusinessDto.getSourceDataId())) {
            customerLambdaQueryWrapper.eq(Customer::getSourceDataId, customerPageBusinessDto.getSourceDataId());
        }

        if (StringUtils.isNotBlank(customerPageBusinessDto.getOrCertificateCode())) {
            customerLambdaQueryWrapper.or().eq(Customer::getCertificateCode, customerPageBusinessDto.getCertificateCode());
        }

        if (StringUtils.isNotBlank(customerPageBusinessDto.getOrderBy())) {
            if (customerPageBusinessDto.isAscFlag()) {
                customerLambdaQueryWrapper.orderByAsc(Customer::getCreateTime);
            }
        }
        /*if(customerPageBusinessDto.getDeleteFlag() != DeleteFlagEnum.ALL.getCode()){
            customerLambdaQueryWrapper.eq(Customer :: getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        }*/
        if (customerPageBusinessDto.getPageNum() == null || customerPageBusinessDto.getPageNum() < 1) {
            customerPageBusinessDto.setPageNum(1);
        }
        if (customerPageBusinessDto.getPageSize() == null || customerPageBusinessDto.getPageSize() < 1) {
            customerPageBusinessDto.setPageSize(10);
        }
        //log.info("当前参数为:{}", JSON.toJSONString(customerPageBusinessDto));
        Page<Customer> page = Page.of(customerPageBusinessDto.getPageNum(), customerPageBusinessDto.getPageSize());
        customerService.page(page, customerLambdaQueryWrapper);
        List<Customer> records = page.getRecords();
        List<CustomerBusinessView> contactInfoBusinessViewList = BeanUtil.copyToList(records, CustomerBusinessView.class);
        Page<CustomerBusinessView> returnPage = BeanUtil.copyProperties(page, Page.class);
        returnPage.setRecords(contactInfoBusinessViewList);
        return returnPage;
    }

    /**
     * 添加客户
     *
     * @param customerAddBusinessDto 客户添加的数据
     * @return boolean
     * <AUTHOR>
     * @date 2023/5/25 10:28
     **/
    @Override
    public boolean add(CustomerAddBusinessDto customerAddBusinessDto) {
        if (customerAddBusinessDto == null) {
            log.warn("添加客户，数据不能为空");
            return false;
        }
        //如果客户ID没有传入，则在此生成
        if (StrUtil.isBlank(customerAddBusinessDto.getCustomerId())) {
            customerAddBusinessDto.setCustomerId(uniqueIdService.getId());
        }
        Customer customer = BeanUtil.copyProperties(customerAddBusinessDto, Customer.class);
        String customerId = customer.getCustomerId();
        if (StrUtil.isBlank(customerId)) {
            customerId = uniqueIdService.getId();
        }
        customer.setCustomerId(customerId);
        BigDataCompanyDetail bigDataCustomerInfo = bigDataThirdService.getBigDataCustomerInfo(customer.getCustomerName());
        if (bigDataCustomerInfo != null) {
            customer = packageSkbCustomerData(customer, bigDataCustomerInfo);
        }
        customer.setCreateTime(LocalDateTime.now());
        customer.setUpdateTime(LocalDateTime.now());
        customer.setOperator(customer.getCreator());

        return customerService.save(customer);
    }

    /**
     * 添加企业客户
     *
     * @param enterpriseCustomerSaveBusinessDto 企业客户添加参数
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/2/29 10:26
     **/
    @Override
    public String saveEnterpriseCustomer(EnterpriseCustomerSaveBusinessDto enterpriseCustomerSaveBusinessDto) {
        //调用大数据接口获取搜客宝企业数据
        BigDataCompanyDetail bigDataCustomerInfo = bigDataThirdService.getBigDataCustomerInfo(enterpriseCustomerSaveBusinessDto.getCustomerName());
        if (bigDataCustomerInfo == null) {
            return null;
        }
        Customer customer = new Customer();
        customer = packageSkbCustomerData(customer, bigDataCustomerInfo);
        customer.setPresentStage(enterpriseCustomerSaveBusinessDto.getPresentStage());
        customer.setCreateWay(enterpriseCustomerSaveBusinessDto.getCreateWay());
        customer.setCreatorKey("scrm");
        customer.setCreator(enterpriseCustomerSaveBusinessDto.getOperator());
        customer.setOperatorKey("scrm");
        customer.setOperator(enterpriseCustomerSaveBusinessDto.getOperator());
        customerService.save(customer);
        return customer.getCustomerId();
    }

    /**
     * 拼装搜客宝客户数据
     * @param customer  客户信息
     * @param bigDataCompanyDetail   搜客宝客户数据
     * <AUTHOR>
     * @date 2024/4/28 下午2:12
     * @return com.ce.scrm.customer.dao.entity.Customer
     **/
    @Override
    public Customer packageSkbCustomerData(Customer customer, BigDataCompanyDetail bigDataCompanyDetail) {
        Customer result = new Customer();
        BeanUtil.copyProperties(customer, result);
        String customerId = result.getCustomerId();
        if (StrUtil.isBlank(customerId) && StrUtil.isBlank(bigDataCompanyDetail.getUncid()) && bigDataCompanyDetail.getUncid().length()>10) {
            customerId = bigDataCompanyDetail.getUncid();
        }
        if (StrUtil.isBlank(customerId)) {
            customerId = uniqueIdService.getId();
        }
        result.setCustomerId(customerId);
        result.setSourceDataId(bigDataCompanyDetail.getPid());
        result.setCustomerType(CustomerTypeEnum.company.getType());
        result.setCustomerName(bigDataCompanyDetail.getEntname());
        result.setCertificateType(1);
        result.setCertificateCode(bigDataCompanyDetail.getUncid());
        result.setCheckInState(bigDataCompanyDetail.getEnt_status());
        result.setEstablishDate(Instant.ofEpochMilli(Long.parseLong(bigDataCompanyDetail.getEstablish_date())).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        result.setRegisterCapital(registerCapital(bigDataCompanyDetail.getReg_cap(), bigDataCompanyDetail.getReg_cap_cur()));
        result.setRegisterNo(bigDataCompanyDetail.getReg_no());
        result.setTaxpayerNo(bigDataCompanyDetail.getUncid());
        result.setEnterpriseType(bigDataCompanyDetail.getEnt_type());
        result.setOpenStartTime(Instant.ofEpochMilli(Long.parseLong(bigDataCompanyDetail.getOp_from())).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        result.setOpenEndTime(Instant.ofEpochMilli(Long.parseLong(bigDataCompanyDetail.getOp_end())).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        result.setTaxpayerQualification("一般纳税人");
        result.setApproveDate(Instant.ofEpochMilli(Long.parseLong(bigDataCompanyDetail.getAppr_date())).atZone(ZoneOffset.ofHours(8)).toLocalDate());
        result.setProvinceCode(bigDataCompanyDetail.getProvince_code());
        result.setProvinceName(bigDataCompanyDetail.getProvince());
        result.setCityCode(bigDataCompanyDetail.getCity_code());
        result.setCityName(bigDataCompanyDetail.getCity());
        result.setDistrictCode(bigDataCompanyDetail.getDistrict_code());
        result.setDistrictName(bigDataCompanyDetail.getDistrict());
        result.setRegistrationAuthority(bigDataCompanyDetail.getReg_org());
        //维护一级行业数据
        if (StrUtil.isNotBlank(bigDataCompanyDetail.getIndustryL1_desc())) {
            result.setFirstIndustryName(bigDataCompanyDetail.getIndustryL1_desc());
            result.setFirstIndustryCode(bigDataCompanyDetail.getIndustryL1_code());
        }
        //维护二级行业数据
        if (CollectionUtil.isNotEmpty(bigDataCompanyDetail.getIndustryL2_desc())) {
            String secondIndustryName = bigDataCompanyDetail.getIndustryL2_desc().get(0);
            result.setSecondIndustryName(secondIndustryName);
            result.setSecondIndustryCode(bigDataCompanyDetail.getIndustryL2_code());
        }
        //维护三级行业数据
        if (StrUtil.isNotBlank(bigDataCompanyDetail.getIndustryL3_desc())) {
            result.setThirdIndustryName(bigDataCompanyDetail.getIndustryL3_desc());
            result.setThirdIndustryCode(bigDataCompanyDetail.getIndustryL3_code());
        }
        //维护四级行业数据
        if (StrUtil.isNotBlank(bigDataCompanyDetail.getIndustryL4_desc())) {
            result.setFourthIndustryName(bigDataCompanyDetail.getIndustryL4_desc());
            result.setFourthIndustryCode(bigDataCompanyDetail.getIndustryL4_code());
        }
        result.setLegalPerson(bigDataCompanyDetail.getLegal_person());
        result.setRegisterAddress(bigDataCompanyDetail.getReg_address());
        result.setBusinessScope(bigDataCompanyDetail.getOp_scope());
        return result;
    }

    /**
     * 拼装注册资本
     * @param reg_cap   注册资金
     * @param reg_cap_cur   注册资金单位
     * <AUTHOR>
     * @date 2024/2/29 10:39
     * @return java.lang.String
     **/
    private String registerCapital(String reg_cap, String reg_cap_cur) {
        String target = "";
        if (org.apache.commons.lang3.StringUtils.isNotBlank(reg_cap)) {
            target += reg_cap;
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(reg_cap_cur)) {
            target += reg_cap_cur;
        }
        return target;
    }

    /**
     * 更新客户
     *
     * @param customerUpdateBusinessDto 客户更新的数据
     * @return boolean
     * <AUTHOR>
     * @date 2023/5/25 11:28
     **/
    @Override
    public boolean update(CustomerUpdateBusinessDto customerUpdateBusinessDto) {
        if (customerUpdateBusinessDto == null) {
            log.warn("更新客户，参数不能为空");
            return false;
        }
        boolean b = updateCustomer(customerUpdateBusinessDto);
        customerCacheHandler.del(customerUpdateBusinessDto.getCustomerId());
        return b;
    }

    /**
     * elasticsearch 模糊查询
     * @author: wangshoufang
     * @date: 2023/9/11 09:02
     * @param customerPageBusinessDto
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.response.DubboPageInfo < com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>>
     */
    @Override
    public Page<CustomerBusinessView> elasticsearchMatchByCondition(CustomerPageBusinessDto customerPageBusinessDto) {
        Page<CustomerBusinessView> page = Page.of(customerPageBusinessDto.getPageNum(), customerPageBusinessDto.getPageSize());
        page.setTotal(0);
        try {
            if (CollectionUtil.isEmpty(customerPageBusinessDto.getCustomerIdList()) && StringUtils.isEmpty(customerPageBusinessDto.getCustomerName())) {
                return null;
            }
            // 显示记录数
            Integer size = 10;
            // 开始记录数
            Integer from = 0;
            if (customerPageBusinessDto.getPageSize() != null) {
                size = customerPageBusinessDto.getPageSize();
            }
            if (customerPageBusinessDto.getPageNum() != null) {
                Integer startIndex = customerPageBusinessDto.getPageNum() - 1;
                from = (startIndex > 0 ? startIndex : 0) * size;
            }
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            if (CollectionUtil.isNotEmpty(customerPageBusinessDto.getCustomerIdList())) {
                TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("customerId", customerPageBusinessDto.getCustomerIdList());
                boolQuery.must(termsQueryBuilder);
            }
            if (StringUtils.isNotEmpty(customerPageBusinessDto.getCustomerName())) {
                MatchQueryBuilder matchQueryBuilder = QueryBuilders.matchQuery("customerName", customerPageBusinessDto.getCustomerName());
                boolQuery.must(matchQueryBuilder);
            }
            //构建查询参数 Builder
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQuery);
            sourceBuilder.from(from);
            sourceBuilder.size(size);
            sourceBuilder.trackTotalHits(true);
            sourceBuilder.timeout(new TimeValue(60, TimeUnit.SECONDS));

            //构建查询参数 Request
            SearchRequest searchRequest = new SearchRequest();
            searchRequest.indices(customerESIndexName);
            searchRequest.source(sourceBuilder);
            SearchResponse response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            SearchHits hits = response.getHits();
            //long totalCount = hits.getTotalHits();
            TotalHits totalCount = hits.getTotalHits();
            List<CustomerESBusinessView> customerElasticsearchViewList = new ArrayList<>();
            if (totalCount.value > 0) {
                for (SearchHit hit : hits) {
                    String result = hit.getSourceAsString();
                    CustomerESBusinessView customerElasticsearchView = JSONObject.parseObject(result, CustomerESBusinessView.class);
                    customerElasticsearchViewList.add(customerElasticsearchView);
                }
            }
            List<CustomerBusinessView> lists = Optional.of(customerElasticsearchViewList).orElse(Lists.newArrayList()).stream().map(record -> {
                CustomerBusinessView customerBusinessView = new CustomerBusinessView();
                customerBusinessView.setCustomerId(record.getCustomerId());
                customerBusinessView.setCustomerName(record.getCustomerName());
                customerBusinessView.setCustomerType(record.getCustomerType());
                customerBusinessView.setCertificateCode(record.getCertificateCode());
                customerBusinessView.setUpdateTime(safeConvertLocalDateTimeToDate(record.getUpdateTime()));
                customerBusinessView.setCreateTime(safeConvertLocalDateTimeToDate(record.getCreateTime()));
                return customerBusinessView;
            }).collect(Collectors.toList());
            page.setTotal(totalCount.value);
            page.setRecords(lists);
            return page;
        } catch (Exception e) {
            log.error("elastic精确查询客户信息失败,param={}", JSONObject.toJSONString(customerPageBusinessDto));
        }
        return page;
    }

    /**
     * <AUTHOR>
     * @date 2025/7/14 10:20:00
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView>
     * @desc 从CustomerEs索引库中根据条件查询客户数据
     */
    @Override
    public Page<CustomerESBusinessView> customerESMatchByCondition(CustomerESPageBusinessDto customerESPageBusinessDto) {

        Page<CustomerESBusinessView> page = Page.of(customerESPageBusinessDto.getPageNum(), customerESPageBusinessDto.getPageSize());
        page.setTotal(0);
        try {
            // 显示记录数
            Integer size = 10;
            // 开始记录数
            Integer from = 0;
            if (customerESPageBusinessDto.getPageSize() != null) {
                size = customerESPageBusinessDto.getPageSize();
            }
            if (customerESPageBusinessDto.getPageNum() != null) {
                Integer startIndex = customerESPageBusinessDto.getPageNum() - 1;
                from = (startIndex > 0 ? startIndex : 0) * size;
            }
            BoolQueryBuilder boolQuery = CustomerESUtil.buildBoolQueryBuilder(customerESPageBusinessDto);

            //构建查询参数 Builder
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            CustomerESUtil.setSourceBuilder(customerESPageBusinessDto, sourceBuilder);
            sourceBuilder.query(boolQuery);
            sourceBuilder.from(from);
            sourceBuilder.size(size);
            sourceBuilder.trackTotalHits(true);
            sourceBuilder.timeout(new TimeValue(60, TimeUnit.SECONDS));

            //构建查询参数 Request
            SearchRequest searchRequest = new SearchRequest();
            searchRequest.indices(customerESIndexName);
            searchRequest.source(sourceBuilder);
            SearchResponse response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            SearchHits hits = response.getHits();
            //long totalCount = hits.getTotalHits();
            TotalHits totalCount = hits.getTotalHits();
            List<CustomerESBusinessView> lists = new ArrayList<>();
            if (totalCount.value > 0) {
                for (SearchHit hit : hits) {
                    String result = hit.getSourceAsString();
                    CustomerESBusinessView esView = JSONObject.parseObject(result, CustomerESBusinessView.class);
                    lists.add(esView);
                }
            }
            page.setTotal(totalCount.value);
            page.setRecords(lists);
            return page;
        } catch (Exception e) {
            log.error("客户ES库查询信息失败,异常信息{}, param={}", e.getMessage(), JSONObject.toJSONString(customerESPageBusinessDto));
        }
        return page;
    }

    /**
     * elasticsearch 精确查询
     * @author: wangshoufang
     * @date: 2023/9/11 09:02
     * @param customerPageBusinessDto
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<com.ce.scrm.customer.dubbo.entity.view.CustomerDubboView>
     */
    @Override
    public CustomerBusinessView elasticsearchExactByCondition(CustomerPageBusinessDto customerPageBusinessDto) {
        try {
            if (CollectionUtil.isEmpty(customerPageBusinessDto.getCustomerIdList()) && StringUtils.isEmpty(customerPageBusinessDto.getCustomerName())) {
                return null;
            }
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            if (CollectionUtil.isNotEmpty(customerPageBusinessDto.getCustomerIdList())) {
                TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("customerId", customerPageBusinessDto.getCustomerIdList());
                boolQuery.must(termsQueryBuilder);
            }
            if (StringUtils.isNotEmpty(customerPageBusinessDto.getCustomerName())) {
                MatchQueryBuilder matchQueryBuilder = QueryBuilders.matchQuery("customerName", customerPageBusinessDto.getCustomerName());
                boolQuery.must(matchQueryBuilder);
            }
            //构建查询参数 Builder
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQuery);
            sourceBuilder.from(0);
            sourceBuilder.size(10);
            sourceBuilder.trackTotalHits(true);
            sourceBuilder.timeout(new TimeValue(60, TimeUnit.SECONDS));

            //构建查询参数 Request
            SearchRequest searchRequest = new SearchRequest();
            searchRequest.indices(customerESIndexName);
            searchRequest.source(sourceBuilder);
            SearchResponse response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            SearchHits hits = response.getHits();
            //long totalCount = hits.getTotalHits();
            TotalHits totalCount = hits.getTotalHits();
            List<CustomerESBusinessView> customerElasticsearchViewList = new ArrayList<>();
            if (totalCount.value > 0) {
                for (SearchHit hit : hits) {
                    String result = hit.getSourceAsString();
                    CustomerESBusinessView customerElasticsearchView = JSONObject.parseObject(result, CustomerESBusinessView.class);
                    customerElasticsearchViewList.add(customerElasticsearchView);
                }
            }
            if (CollectionUtil.isNotEmpty(customerElasticsearchViewList)) {
                CustomerESBusinessView customerElasticsearchView = customerElasticsearchViewList.get(0);
                CustomerBusinessView customerBusinessView = new CustomerBusinessView();
                customerBusinessView.setCustomerId(customerElasticsearchView.getCustomerId());
                customerBusinessView.setCustomerName(customerElasticsearchView.getCustomerName());
                customerBusinessView.setCustomerType(customerElasticsearchView.getCustomerType());
                customerBusinessView.setCertificateCode(customerElasticsearchView.getCertificateCode());
                customerBusinessView.setUpdateTime(safeConvertLocalDateTimeToDate(customerElasticsearchView.getUpdateTime()));
                customerBusinessView.setCreateTime(safeConvertLocalDateTimeToDate(customerElasticsearchView.getCreateTime()));
                return customerBusinessView;
            }
        } catch (Exception e) {
            log.error("elastic精确查询客户信息失败,param={}", JSONObject.toJSONString(customerPageBusinessDto));
        }
        return null;
    }

    /**
     * 更新客户数据
     * @param customerUpdateBusinessDto 更新参数
     * <AUTHOR>
     * @date 2023/5/25 14:13
     * @return boolean
     **/
    private boolean updateCustomer(CustomerUpdateBusinessDto customerUpdateBusinessDto) {
        LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerUpdateBusinessDto.getCustomerId());
        if (StrUtil.isNotBlank(customerUpdateBusinessDto.getCustomerName())) {
            updateChainWrapper.set(Customer::getCustomerName, customerUpdateBusinessDto.getCustomerName());
        }
        if (customerUpdateBusinessDto.getCertificateType() != null) {
            updateChainWrapper.set(Customer::getCertificateType, customerUpdateBusinessDto.getCertificateType());
        }
        if (StrUtil.isNotBlank(customerUpdateBusinessDto.getCertificateCode())) {
            updateChainWrapper.set(Customer::getCertificateCode, customerUpdateBusinessDto.getCertificateCode());
        }
        if (StrUtil.isNotBlank(customerUpdateBusinessDto.getCheckInState())) {
            updateChainWrapper.set(Customer::getCheckInState, customerUpdateBusinessDto.getCheckInState());
        }
        if (customerUpdateBusinessDto.getPresentStage() != null) {
            updateChainWrapper.set(Customer::getPresentStage, customerUpdateBusinessDto.getPresentStage());
        }

        if (customerUpdateBusinessDto.getEstablishDate() != null) {
            updateChainWrapper.set(Customer::getEstablishDate, customerUpdateBusinessDto.getEstablishDate());
        }
        if (customerUpdateBusinessDto.getRegisterCapital() != null) {
            updateChainWrapper.set(Customer::getRegisterCapital, customerUpdateBusinessDto.getRegisterCapital());
        }
        if (customerUpdateBusinessDto.getPaidInCapital() != null) {
            updateChainWrapper.set(Customer::getPaidInCapital, customerUpdateBusinessDto.getPaidInCapital());
        }

        if (customerUpdateBusinessDto.getOrganizationCode() != null) {
            updateChainWrapper.set(Customer::getOrganizationCode, customerUpdateBusinessDto.getOrganizationCode());
        }

        if (customerUpdateBusinessDto.getRegisterNo() != null) {
            updateChainWrapper.set(Customer::getRegisterNo, customerUpdateBusinessDto.getRegisterNo());
        }

        if (customerUpdateBusinessDto.getTaxpayerNo() != null) {
            updateChainWrapper.set(Customer::getTaxpayerNo, customerUpdateBusinessDto.getTaxpayerNo());
        }

        if (customerUpdateBusinessDto.getEnterpriseType() != null) {
            updateChainWrapper.set(Customer::getEnterpriseType, customerUpdateBusinessDto.getEnterpriseType());
        }
        if (customerUpdateBusinessDto.getTaxpayerQualification() != null) {
            updateChainWrapper.set(Customer::getTaxpayerQualification, customerUpdateBusinessDto.getTaxpayerQualification());
        }

        if (customerUpdateBusinessDto.getStaffScale() != null) {
            updateChainWrapper.set(Customer::getStaffScale, customerUpdateBusinessDto.getStaffScale());
        }

        if (customerUpdateBusinessDto.getInsuredNum() != null) {
            updateChainWrapper.set(Customer::getInsuredNum, customerUpdateBusinessDto.getInsuredNum());
        }

        if (customerUpdateBusinessDto.getApproveDate() != null) {
            updateChainWrapper.set(Customer::getApproveDate, customerUpdateBusinessDto.getApproveDate());
        }

        if (customerUpdateBusinessDto.getRegistrationAuthority() != null) {
            updateChainWrapper.set(Customer::getRegistrationAuthority, customerUpdateBusinessDto.getRegistrationAuthority());
        }

        if (customerUpdateBusinessDto.getImportExportEnterpriseCode() != null) {
            updateChainWrapper.set(Customer::getImportExportEnterpriseCode, customerUpdateBusinessDto.getImportExportEnterpriseCode());
        }

        if (customerUpdateBusinessDto.getFirstIndustryCode() != null) {
            updateChainWrapper.set(Customer::getFirstIndustryCode, customerUpdateBusinessDto.getFirstIndustryCode());
        }

        if (customerUpdateBusinessDto.getFirstIndustryName() != null) {
            updateChainWrapper.set(Customer::getFirstIndustryName, customerUpdateBusinessDto.getFirstIndustryName());
        }

        if (customerUpdateBusinessDto.getSecondIndustryCode() != null) {
            updateChainWrapper.set(Customer::getSecondIndustryCode, customerUpdateBusinessDto.getSecondIndustryCode());
        }

        if (customerUpdateBusinessDto.getSecondIndustryName() != null) {
            updateChainWrapper.set(Customer::getSecondIndustryName, customerUpdateBusinessDto.getSecondIndustryName());
        }

        if (StrUtil.isNotBlank(customerUpdateBusinessDto.getProvinceCode())) {
            updateChainWrapper.set(Customer::getProvinceCode, customerUpdateBusinessDto.getProvinceCode());
        }
        if (StrUtil.isNotBlank(customerUpdateBusinessDto.getProvinceName())) {
            updateChainWrapper.set(Customer::getProvinceName, customerUpdateBusinessDto.getProvinceName());
        }
        if (StrUtil.isNotBlank(customerUpdateBusinessDto.getCityCode())) {
            updateChainWrapper.set(Customer::getCityCode, customerUpdateBusinessDto.getCityCode());
        }
        if (StrUtil.isNotBlank(customerUpdateBusinessDto.getCityName())) {
            updateChainWrapper.set(Customer::getCityName, customerUpdateBusinessDto.getCityName());
        }
        if (StrUtil.isNotBlank(customerUpdateBusinessDto.getDistrictCode())) {
            updateChainWrapper.set(Customer::getDistrictCode, customerUpdateBusinessDto.getDistrictCode());
        }
        if (StrUtil.isNotBlank(customerUpdateBusinessDto.getDistrictName())) {
            updateChainWrapper.set(Customer::getDistrictName, customerUpdateBusinessDto.getDistrictName());
        }
        if (StrUtil.isNotBlank(customerUpdateBusinessDto.getRegisterAddress())) {
            updateChainWrapper.set(Customer::getRegisterAddress, customerUpdateBusinessDto.getRegisterAddress());
        }
        if (StrUtil.isNotBlank(customerUpdateBusinessDto.getOperator())) {
            updateChainWrapper.set(Customer::getOperator, customerUpdateBusinessDto.getOperator());
        }
        if (customerUpdateBusinessDto.getTagQuoteCust() != null) {
            updateChainWrapper.set(Customer::getTagQuoteCust, customerUpdateBusinessDto.getTagQuoteCust());
        }
        if (StrUtil.isNotBlank(customerUpdateBusinessDto.getRecommendCustId())) {
            updateChainWrapper.set(Customer::getRecommendCustId, customerUpdateBusinessDto.getRecommendCustId());
        }
        if (customerUpdateBusinessDto.getRecommendCustCreateTime() != null){
            updateChainWrapper.set(Customer::getRecommendCustCreateTime, customerUpdateBusinessDto.getRecommendCustCreateTime());
        }
        if (customerUpdateBusinessDto.getDistributeChannel() != null) {
            updateChainWrapper.set(Customer::getDistributeChannel, customerUpdateBusinessDto.getDistributeChannel());
        }
        if (Objects.equals(customerUpdateBusinessDto.getDistributeChannelIsNull() ,1)) {
            updateChainWrapper.set(Customer::getDistributeChannel, null);
        }
        if (customerUpdateBusinessDto.getDistributeTime() != null) {
            updateChainWrapper.set(Customer::getDistributeTime, customerUpdateBusinessDto.getDistributeTime());
        }

        if (customerUpdateBusinessDto.getReviewStatus() != null) {
            updateChainWrapper.set(Customer::getReviewStatus, customerUpdateBusinessDto.getReviewStatus());
        }
        if (Objects.equals(customerUpdateBusinessDto.getReviewStatusIsNull() ,1)) {
            updateChainWrapper.set(Customer::getReviewStatus, null);
        }

        if (customerUpdateBusinessDto.getReceiptEndTime() != null) {
            updateChainWrapper.set(Customer::getReceiptEndTime, customerUpdateBusinessDto.getReceiptEndTime());
        }
        if (Objects.equals(customerUpdateBusinessDto.getReceiptEndTimeIsNull() ,1)) {
            updateChainWrapper.set(Customer::getReceiptEndTime, null);
        }

        return updateChainWrapper.update();
    }

    /**
     * 获取KA客户列表
     *
     * <AUTHOR>
     * @date 2024/1/19 21:41
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView>
     **/
    @Override
    public List<CustomerBusinessView> getKaCustomerList() {
        List<Customer> customerList = customerService.lambdaQuery()
                .eq(Customer::getTagKa, 1)
                .eq(Customer::getCustomerType, CustomerTypeEnum.company.getType())
                .select(Customer::getId, Customer::getCustomerName, Customer::getSourceDataId, Customer::getCertificateCode)
                .list();
        return BeanUtil.copyToList(customerList, CustomerBusinessView.class);
    }

    /**
     * 清理客户ka标记
     *
     * @param idList 客户表主键ID列表
     * <AUTHOR>
     * @date 2024/1/22 16:14
     **/
    @Override
    public void cleanCustomerKaFlag(List<Long> idList) {
        customerService.lambdaUpdate().set(Customer::getTagKa, 0).in(Customer::getId, idList).update();
    }

    /**
     * 给客户添加ka标记
     *
     * @param customerBusinessViewList 客户信息列表
     * <AUTHOR>
     * @date 2024/1/22 16:24
     **/
    @Override
    public void addCustomerKaFlag(List<CustomerBusinessView> customerBusinessViewList) {
        for (CustomerBusinessView customerBusinessView : customerBusinessViewList) {
            List<Customer> customerList = getAddKaFlagCustomerIdList(customerBusinessView);
            if (CollectionUtil.isNotEmpty(customerList)) {
                List<Long> customerIdList = customerList.stream().map(Customer::getId).collect(Collectors.toList());
                customerService.lambdaUpdate().set(Customer::getTagKa, 1).in(Customer::getId, customerIdList).update();
            }
        }
    }

    /**
     * 搜客宝标签flag7,flag8 更新
     * @param customerTagUpdateBusinessDto 更新搜客宝客户标签
     * @return
     */
    @Override
    public boolean updateCustomerSkbTag(CustomerTagUpdateBusinessDto customerTagUpdateBusinessDto) {
        LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerTagUpdateBusinessDto.getCustomerId());
        updateChainWrapper.set(Customer::getOperator, "admin");
        updateChainWrapper.set(Customer::getTagFlag7, customerTagUpdateBusinessDto.getTagFlag7());
        updateChainWrapper.set(Customer::getTagFlag8, customerTagUpdateBusinessDto.getTagFlag8());
        updateChainWrapper.set(Customer::getTagFlag12, customerTagUpdateBusinessDto.getTagFlag12());
        updateChainWrapper.set(Customer::getTagTechcompany, customerTagUpdateBusinessDto.getTagTechcompany());
        updateChainWrapper.set(Customer::getRegisterCapital, customerTagUpdateBusinessDto.getRegCapUnify());
        if (StringUtils.isNotBlank(customerTagUpdateBusinessDto.getSourceDataId())) {
            updateChainWrapper.set(Customer::getSourceDataId, customerTagUpdateBusinessDto.getSourceDataId());
        }
        boolean result = updateChainWrapper.update();
        customerCacheHandler.del(customerTagUpdateBusinessDto.getCustomerId());
        return result;
    }

    /**
     * 更新门户实例标签
     * @param customerTagUpdateBusinessDto 更新实例客户标签
     * @return
     */
    @Override
    public boolean updateCustomerInstanceTag(CustomerTagUpdateBusinessDto customerTagUpdateBusinessDto) {
        LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerTagUpdateBusinessDto.getCustomerId());
        updateChainWrapper.set(Customer::getOperator, "admin");
        /**
         * 是否门户连带客户 0:否，1:是
         */
        updateChainWrapper.set(Customer::getTagMenhuRelated, customerTagUpdateBusinessDto.getTagMenhuRelated());
        /**
         * 是否生态转门户 0:否，1:是
         */
        updateChainWrapper.set(Customer::getTagEcoToMenhu, customerTagUpdateBusinessDto.getTagEcoToMenhu());
        /**
         * 是否生态客户 0:否，1:是
         */
        updateChainWrapper.set(Customer::getTagEcoCust, customerTagUpdateBusinessDto.getTagEcoCust());
        /**
         * 是否数字版本门户 0:否，1:是
         */
        updateChainWrapper.set(Customer::getTagMenhuDigital, customerTagUpdateBusinessDto.getTagMenhuDigital());
        /**
         * 是否2023版本门户 0:否，1:是
         */
        updateChainWrapper.set(Customer::getTagMenhu2023, customerTagUpdateBusinessDto.getTagMenhu2023());
        /**
         * 是否低版本门户 0:否，1:是
         */
        updateChainWrapper.set(Customer::getTagMenhuLowver, customerTagUpdateBusinessDto.getTagMenhuLowver());
        /**
         * 是否门户应升已升客户 0:否，1:是
         */
        updateChainWrapper.set(Customer::getTagMenhuUpgradeableUpgrade, customerTagUpdateBusinessDto.getTagMenhuUpgradeableUpgrade());
        /**
         * 是否门户应升级客户 0:否，1:是
         */
        updateChainWrapper.set(Customer::getTagMenhuUpgradeable, customerTagUpdateBusinessDto.getTagMenhuUpgradeable());
        /**
         * 是否门户应续已续客户 0:否，1:是
         */
        updateChainWrapper.set(Customer::getTagMenhuRenewableRenew, customerTagUpdateBusinessDto.getTagMenhuRenewableRenew());
        /**
         * 是否门户应续客户 0:否，1:是
         */
        updateChainWrapper.set(Customer::getTagMenhuRenewable, customerTagUpdateBusinessDto.getTagMenhuRenewable());
        /**
         * 是否交叉购买客户 0:否，1:是
         */
        updateChainWrapper.set(Customer::getTagCrossBuy, customerTagUpdateBusinessDto.getTagCrossBuy());
        /**
         * 是否纯门户客户 0:否，1:是
         */
        updateChainWrapper.set(Customer::getTagPureMenhu, customerTagUpdateBusinessDto.getTagPureMenhu());
        /**
         * 门户新客户首次购买时间
         */
        updateChainWrapper.set(Customer::getTagMenhuNewTime, customerTagUpdateBusinessDto.getTagMenhuNewTime());
        /**
         * 门户新客户首次购买产品类别 0:低版本 1:门户2023 2:数字门户
         */
        updateChainWrapper.set(Customer::getTagMenhuNewCategory, customerTagUpdateBusinessDto.getTagMenhuNewCategory());
        Boolean result = updateChainWrapper.update();
        customerCacheHandler.del(customerTagUpdateBusinessDto.getCustomerId());
        return result;
    }

    /**
     * 获取添加ka标记的客户ID列表
     * @param customerBusinessView  客户信息
     * <AUTHOR>
     * @date 2024/1/22 17:04
     * @return java.util.List<com.ce.scrm.customer.dao.entity.Customer>
     **/
    private List<Customer> getAddKaFlagCustomerIdList(CustomerBusinessView customerBusinessView) {
        //根据社会统一信用代码更新客户ka标记
        List<Customer> customerList = customerService.lambdaQuery()
                .select(Customer::getId)
                .eq(Customer::getCertificateCode, customerBusinessView.getCertificateCode())
                .eq(Customer::getCustomerType, CustomerTypeEnum.company.getType())
                .list();
        if (CollectionUtil.isNotEmpty(customerList)) {
            return customerList;
        }
        //根据名称更新客户ka标记
        customerList = customerService.lambdaQuery()
                .select(Customer::getId)
                .eq(Customer::getCertificateCode, customerBusinessView.getCustomerName())
                .eq(Customer::getCustomerType, CustomerTypeEnum.company.getType())
                .list();
        if (CollectionUtil.isNotEmpty(customerList)) {
            return customerList;
        }
        //根据搜客宝ID更新客户ka标记
        customerList = customerService.lambdaQuery()
                .select(Customer::getId)
                .eq(Customer::getCertificateCode, customerBusinessView.getSourceDataId())
                .eq(Customer::getCustomerType, CustomerTypeEnum.company.getType())
                .list();
        if (CollectionUtil.isNotEmpty(customerList)) {
            return customerList;
        }
        return Lists.newArrayList();
    }

    /**
     * 判断一个客户的联系人，是否是法人
     *
     *  @param customerId 客户ID
     *  @param contactName 联系人姓名
     *  <AUTHOR>
     *  @date 2024/9/10
     *  @return boolean 是否法人
     */
    @Override
    public boolean isLegalPerson(String customerId, String contactName) {
        CustomerCacheData customerCacheData = customerCacheHandler.get(customerId);
        if (customerCacheData == null) {
            log.warn("isLegalPerson -> customer cache data is null, customerId: {}", customerId);
            return false;
        }
        if (Objects.equals(customerCacheData.getCustomerType(), CustomerTypeEnum.personal.getType())){
            log.warn("isLegalPerson -> customerType is personal, customerId: {}", customerId);
            return false;
        }
        String customerName = customerCacheData.getCustomerName();
        if (StrUtil.isBlank(customerName)) {
            log.warn("isLegalPerson -> customerName is null, customerName: {}", customerName);
            return false;
        }
        // 根据customerName查询法人联系人
        DubboResult<BigDataCompanyDetailView> bigDataCompanyDetailViewDubboResult = bigDataDubbo.getCompanyDetailByName(customerName);
        if (!bigDataCompanyDetailViewDubboResult.checkSuccess()) {
            log.warn("调用获取企业基本信息数据dubbo失败，返回值为:{}", JSON.toJSONString(bigDataCompanyDetailViewDubboResult));
            return false;
        }
        log.info("调用获取企业基本信息数据dubbo成功，返回值为:{}", JSON.toJSONString(bigDataCompanyDetailViewDubboResult));
        BigDataCompanyDetailView bigDataCompanyDetailView = bigDataCompanyDetailViewDubboResult.getData();
        if (bigDataCompanyDetailView == null) {
            log.warn("调用获取企业基本信息数据dubbo返回值为空，customerName: {}", customerName);
            return false;
        }
        String legalPersonName = bigDataCompanyDetailView.getLegal_person();
        if (StrUtil.isBlank(legalPersonName)) {
            log.warn("企业基本信息中法人姓名为空，customerName: {}", customerName);
            return false;
        }
        //去除首尾空格进行判断是否相等
        return legalPersonName.trim().equals(contactName.trim());
    }

    /**
     * 获取拜访率计算 统计结果
     * @param loginInfoBusinessDto
     * @param begin
     * @param end
     * @param aTrue
     * @return
     */
    @Override
    public Integer getCallRate(LoginInfoBusinessDto loginInfoBusinessDto, Integer begin, Integer end, Boolean aTrue) {
        //log.info("获取拜访率计算统计SQL开始 loginInfoBusinessDto:{},begin:{},end:{},aTrue:{}", JSON.toJSONString(loginInfoBusinessDto),begin,end,aTrue);

        LambdaQueryChainWrapper<Customer> customerLambdaQueryChainWrapper = customerService.lambdaQuery();

        // 权限筛选
        if(EmpPositionConstant.BUSINESS_AREA.equals(loginInfoBusinessDto.getLoginPosition())) {
            customerLambdaQueryChainWrapper.eq(Customer::getProtectAreaId,loginInfoBusinessDto.getLoginAreaId());
        } else if (PositionUtil.isBusinessMajor(loginInfoBusinessDto.getLoginPosition())) {
            customerLambdaQueryChainWrapper.eq(Customer::getProtectAreaId,loginInfoBusinessDto.getLoginAreaId());
            customerLambdaQueryChainWrapper.eq(Customer::getProtectSubcompanyId,loginInfoBusinessDto.getLoginSubId());
        } else if (PositionUtil.isBusinessManager(loginInfoBusinessDto.getLoginPosition())) {
            customerLambdaQueryChainWrapper.eq(Customer::getProtectAreaId,loginInfoBusinessDto.getLoginAreaId());
            customerLambdaQueryChainWrapper.eq(Customer::getProtectSubcompanyId,loginInfoBusinessDto.getLoginSubId());
            customerLambdaQueryChainWrapper.eq(Customer::getProtectBussdeptId,loginInfoBusinessDto.getLoginOrgId());
        } else if (PositionUtil.isBusinessSaler(loginInfoBusinessDto.getLoginPosition())) {
            customerLambdaQueryChainWrapper.eq(Customer::getProtectAreaId,loginInfoBusinessDto.getLoginAreaId());
            customerLambdaQueryChainWrapper.eq(Customer::getProtectSubcompanyId,loginInfoBusinessDto.getLoginSubId());
            customerLambdaQueryChainWrapper.eq(Customer::getProtectBussdeptId,loginInfoBusinessDto.getLoginOrgId());
            customerLambdaQueryChainWrapper.eq(Customer::getProtectSalerId,loginInfoBusinessDto.getLoginEmployeeId());
        } else if (EmpPositionConstant.BUSINESS_SYS_MANAGER.equals(loginInfoBusinessDto.getLoginPosition())) {

        } else {
            //其他角色默认不显示数据
            return null;
        }

        //本月打卡次数
        if(aTrue) {
            customerLambdaQueryChainWrapper.gt(Customer::getCdpCurrentMonthClockCount,0);
        }

        // 保护
        customerLambdaQueryChainWrapper.eq(Customer::getProtectStatus,"1");

        // 已保护天数
        customerLambdaQueryChainWrapper.gt(Customer::getCdpProtectDay,begin);
        customerLambdaQueryChainWrapper.le(Customer::getCdpProtectDay,end);

        // 客户首次付款时间 小于 最新一次保护时间
        customerLambdaQueryChainWrapper.apply("(cdp_first_payment_time is null OR cdp_first_payment_time > protect_protect_time)");

        // 查询
        Long count = customerLambdaQueryChainWrapper.count();
        //log.info("获取拜访率计算统计SQL结束 count:{}", count);
        return Math.toIntExact(count);
    }

    @Override
    public Page<CustomerBusinessView> getCallDetails(CallDetailsBusinessDto callDetailsBusinessDto) {
        LambdaQueryChainWrapper<Customer> customerLambdaQueryChainWrapper = customerService.lambdaQuery();

        // 系统权限筛选
        if(EmpPositionConstant.BUSINESS_AREA.equals(callDetailsBusinessDto.getLoginPosition())) {
            customerLambdaQueryChainWrapper.eq(Customer::getProtectAreaId,callDetailsBusinessDto.getLoginAreaId());
        } else if (PositionUtil.isBusinessMajor(callDetailsBusinessDto.getLoginPosition())) {
            customerLambdaQueryChainWrapper.eq(Customer::getProtectAreaId,callDetailsBusinessDto.getLoginAreaId());
            customerLambdaQueryChainWrapper.eq(Customer::getProtectSubcompanyId,callDetailsBusinessDto.getLoginSubId());
        } else if (PositionUtil.isBusinessManager(callDetailsBusinessDto.getLoginPosition())) {
            customerLambdaQueryChainWrapper.eq(Customer::getProtectAreaId,callDetailsBusinessDto.getLoginAreaId());
            customerLambdaQueryChainWrapper.eq(Customer::getProtectSubcompanyId,callDetailsBusinessDto.getLoginSubId());
            customerLambdaQueryChainWrapper.eq(Customer::getProtectBussdeptId,callDetailsBusinessDto.getLoginOrgId());
        } else if (PositionUtil.isBusinessSaler(callDetailsBusinessDto.getLoginPosition())) {
            customerLambdaQueryChainWrapper.eq(Customer::getProtectAreaId,callDetailsBusinessDto.getLoginAreaId());
            customerLambdaQueryChainWrapper.eq(Customer::getProtectSubcompanyId,callDetailsBusinessDto.getLoginSubId());
            customerLambdaQueryChainWrapper.eq(Customer::getProtectBussdeptId,callDetailsBusinessDto.getLoginOrgId());
            customerLambdaQueryChainWrapper.eq(Customer::getProtectSalerId,callDetailsBusinessDto.getLoginEmployeeId());
        } else if (EmpPositionConstant.BUSINESS_SYS_MANAGER.equals(callDetailsBusinessDto.getLoginPosition())) {

        } else {
            //其他角色默认不显示数据
            return null;
        }

        // 保护天数
        calculateProtectDaySql(customerLambdaQueryChainWrapper,callDetailsBusinessDto.getDayEnum());

        // 本月打卡次数
        calculateCurrentMonthClockCountSql(customerLambdaQueryChainWrapper,callDetailsBusinessDto.getCurrentMonthClockCountEnum());

        // 累计打卡次数
        calculateClockCountSql(customerLambdaQueryChainWrapper,callDetailsBusinessDto.getClockCountEnum());

        // 角色
        calculateRoleSql(customerLambdaQueryChainWrapper,callDetailsBusinessDto.getAreaId(),
                callDetailsBusinessDto.getSubId(),callDetailsBusinessDto.getDeptId(),
                callDetailsBusinessDto.getSalerId());

        // 基础条件
        // 保护
        customerLambdaQueryChainWrapper.eq(Customer::getProtectStatus,"1");
        // 客户首次付款时间 小于 最新一次保护时间
        customerLambdaQueryChainWrapper.apply("(cdp_first_payment_time is null OR cdp_first_payment_time > protect_protect_time)");

        // 分页处理
        Page<Customer> page = Page.of(callDetailsBusinessDto.getPageNum(), callDetailsBusinessDto.getPageSize());
        Page<Customer> customerPage = customerLambdaQueryChainWrapper.page(page);
        Page<CustomerBusinessView> returnPage = BeanUtil.copyProperties(customerPage, Page.class);
        returnPage.setRecords(BeanUtil.copyToList(customerPage.getRecords(), CustomerBusinessView.class));
        return returnPage;
    }

    /**
     * 监听保护关系表binlog 然后同步customer
     * @param customerUpdateBusinessDto
     * @return
     */
    @Override
    public Boolean monitorProtectInfoConsumer(CustomerUpdateBusinessDto customerUpdateBusinessDto) {
        if (customerUpdateBusinessDto == null) {
            log.warn("更新客户，参数不能为空");
            return false;
        }
        boolean b = updateCustomerBinLog(customerUpdateBusinessDto);
        customerCacheHandler.del(customerUpdateBusinessDto.getCustomerId());
        return b;
    }

    /**
     * 更新数据
     * @param customerUpdateBusinessDto
     * @return
     */
    private boolean updateCustomerBinLog(CustomerUpdateBusinessDto customerUpdateBusinessDto) {
        LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerUpdateBusinessDto.getCustomerId());
        updateChainWrapper.set(Customer::getOperator,customerUpdateBusinessDto.getOperator());
        updateChainWrapper.set(Customer::getProtectSalerId, customerUpdateBusinessDto.getProtectSalerId());
        updateChainWrapper.set(Customer::getProtectBussdeptId, customerUpdateBusinessDto.getProtectBussdeptId());
        updateChainWrapper.set(Customer::getProtectSubcompanyId, customerUpdateBusinessDto.getProtectSubcompanyId());
        updateChainWrapper.set(Customer::getProtectBuId, customerUpdateBusinessDto.getProtectBuId());
        updateChainWrapper.set(Customer::getProtectAreaId, customerUpdateBusinessDto.getProtectAreaId());
        updateChainWrapper.set(Customer::getProtectStatus, customerUpdateBusinessDto.getProtectStatus());
        updateChainWrapper.set(Customer::getProtectProtectendTime, customerUpdateBusinessDto.getProtectProtectendTime());
        updateChainWrapper.set(Customer::getProtectCustType, customerUpdateBusinessDto.getProtectCustType());
        updateChainWrapper.set(Customer::getProtectProtectTime, customerUpdateBusinessDto.getProtectProtectTime());
        updateChainWrapper.set(Objects.equals(customerUpdateBusinessDto.getCdpProtectDay(),0),Customer::getCdpProtectDay, customerUpdateBusinessDto.getCdpProtectDay());
        updateChainWrapper.set(Objects.equals(customerUpdateBusinessDto.getCdpCurrentMonthClockCount(),0),Customer::getCdpCurrentMonthClockCount, customerUpdateBusinessDto.getCdpCurrentMonthClockCount());
        updateChainWrapper.set(Objects.equals(customerUpdateBusinessDto.getCdpClockCount(),0),Customer::getCdpClockCount, customerUpdateBusinessDto.getCdpClockCount());
//        updateChainWrapper.set(Customer::getCdpFirstPaymentTime, customerUpdateBusinessDto.getCdpFirstPaymentTime());
        updateChainWrapper.set(Customer::getBindFlag, customerUpdateBusinessDto.getBindFlag());
        updateChainWrapper.set(Customer::getBusinessOpportunityConfirmationFlag, customerUpdateBusinessDto.getBusinessOpportunityConfirmationFlag());

        return updateChainWrapper.update();
    }

    /**
     * 更新客户表数据 只更新CustType字段
     * 签单的场景，保护关系不变,购买的产品决定用户的cust_type
     * @param customerUpdateBusinessDto
     * @return
     */
    @Override
    public Boolean updateCustomerCustType(CustomerUpdateBusinessDto customerUpdateBusinessDto) {
        log.info("customerUpdateBusinessDto: {}" ,JSON.toJSONString(customerUpdateBusinessDto));
        if (customerUpdateBusinessDto == null) {
            log.warn("更新客户，参数不能为空");
            return false;
        }
        LambdaUpdateChainWrapper<Customer> updateChainWrapper = customerService.lambdaUpdate().eq(Customer::getCustomerId, customerUpdateBusinessDto.getCustomerId());
        updateChainWrapper.set(Customer::getOperator,customerUpdateBusinessDto.getOperator());
        updateChainWrapper.set(Customer::getProtectCustType, customerUpdateBusinessDto.getProtectCustType());
        updateChainWrapper.set(Customer::getBindFlag, customerUpdateBusinessDto.getBindFlag());
        updateChainWrapper.set(Customer::getBusinessOpportunityConfirmationFlag, customerUpdateBusinessDto.getBusinessOpportunityConfirmationFlag());
        boolean b = updateChainWrapper.update();
        customerCacheHandler.del(customerUpdateBusinessDto.getCustomerId());
        return b;
    }

    /***
     * 查询合格新客
     * @param customerPageBusinessDto
     * <AUTHOR>
     * @date 2025/1/6 20:43
     * @version 1.0.0
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView>
    **/
    @Override
    public Page<CustomerBusinessView> qualifiedNewCustomerList(CustomerPageBusinessDto customerPageBusinessDto) {
        LambdaQueryWrapper<Customer> customerLambdaQueryWrapper = Wrappers.lambdaQuery();
        List<String> customerIdList = customerPageBusinessDto.getCustomerIdList();
        customerLambdaQueryWrapper.in(Customer::getRecommendCustId, customerIdList);
        if (customerPageBusinessDto.getQualifiedNewCustBeginTime()!=null){
            customerLambdaQueryWrapper.ge(Customer::getTagQualifiedNewCustTime, customerPageBusinessDto.getQualifiedNewCustBeginTime());
        }
        if (customerPageBusinessDto.getQualifiedNewCustEndTime()!=null){
            customerLambdaQueryWrapper.le(Customer::getTagQualifiedNewCustTime, customerPageBusinessDto.getQualifiedNewCustEndTime());
        }
        if (customerPageBusinessDto.getPageNum() == null || customerPageBusinessDto.getPageNum() < 1) {
            customerPageBusinessDto.setPageNum(1);
        }
        if (customerPageBusinessDto.getPageSize() == null || customerPageBusinessDto.getPageSize() < 1) {
            customerPageBusinessDto.setPageSize(10);
        }
        //log.info("当前参数为:{}", JSON.toJSONString(customerPageBusinessDto));
        Page<Customer> page = Page.of(customerPageBusinessDto.getPageNum(), customerPageBusinessDto.getPageSize());
        customerService.page(page, customerLambdaQueryWrapper);
        List<Customer> records = page.getRecords();
        List<CustomerBusinessView> contactInfoBusinessViewList = BeanUtil.copyToList(records, CustomerBusinessView.class);
        Page<CustomerBusinessView> returnPage = BeanUtil.copyProperties(page, Page.class);
        returnPage.setRecords(contactInfoBusinessViewList);
        return returnPage;
    }

    /**
     * Description: 按地区统计 tag_menhu=1（【销管口径】门户客户）的客户数
     *              国家无地区码，用"000000”指代国家码
     * @author: JiuDD
     * @param businessDto 统计颗粒度:1=全国;2=省;3=市
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.CompanyDistributionSummaryBusinessView>
     * date: 2025/6/27 11:14
     */
    @Override
    public List<CompanyDistributionSummaryBusinessView> zqCompanyDistributionSummary(CompanyDistributionSummaryBusinessDto businessDto) {
        QueryWrapper<Customer> queryWrapper = new QueryWrapper<>();
        if (businessDto.getLevel() == 1) {
            queryWrapper.select("'000000' AS areaCode, SUM(tag_menhu) AS total");
        } else if (businessDto.getLevel() == 2) {
            queryWrapper.select("province_code AS areaCode, SUM(tag_menhu) AS total");
            queryWrapper.groupBy("province_code");
        } else if (businessDto.getLevel() == 3) {
            queryWrapper.select("city_code AS areaCode, SUM(tag_menhu) AS total");
            queryWrapper.groupBy("city_code");
        } else {
            return Lists.newArrayList();
        }
        queryWrapper.eq("tag_menhu", 1);
        List<Map<String, Object>> maps = customerService.getBaseMapper().selectMaps(queryWrapper);
        return maps.stream()
                .filter(item -> item.get("areaCode") instanceof String && !(StrUtil.isBlank((String) item.get("areaCode"))))
                .map(item -> new CompanyDistributionSummaryBusinessView(String.valueOf(item.get("areaCode")), Integer.parseInt(String.valueOf(item.get("total")))))
                .collect(Collectors.toList());
    }

    private void calculateRoleSql(LambdaQueryChainWrapper<Customer> customerLambdaQueryChainWrapper,
                                  String loginAreaId, String loginSubId, String loginOrgId, String loginEmployeeId) {

        if(loginAreaId != null) {
            customerLambdaQueryChainWrapper.eq(Customer::getProtectAreaId,loginAreaId);
        }
        if(loginSubId != null) {
            customerLambdaQueryChainWrapper.eq(Customer::getProtectSubcompanyId,loginSubId);
        }
        if(loginOrgId != null) {
            customerLambdaQueryChainWrapper.eq(Customer::getProtectBussdeptId,loginOrgId);
        }
        if (loginEmployeeId != null) {
            customerLambdaQueryChainWrapper.eq(Customer::getProtectSalerId,loginEmployeeId);
        }

    }

    /**
     * 计算打卡次数SQL
     * @param customerLambdaQueryChainWrapper
     * @param clockCountEnum
     */
    private void calculateClockCountSql(LambdaQueryChainWrapper<Customer> customerLambdaQueryChainWrapper, Integer clockCountEnum) {

        if(clockCountEnum==null) {
            return;
        }

        if(clockCountEnum.equals(0)) {
            customerLambdaQueryChainWrapper.eq(Customer::getCdpClockCount,0);
        }else if(clockCountEnum.equals(1)) {
            customerLambdaQueryChainWrapper.eq(Customer::getCdpClockCount,1);
        }else if (clockCountEnum.equals(2)) {
            customerLambdaQueryChainWrapper.eq(Customer::getCdpClockCount,2);
        }else if (clockCountEnum.equals(3)) {
            customerLambdaQueryChainWrapper.eq(Customer::getCdpClockCount,3);
        }else if (clockCountEnum.equals(4)) {
            customerLambdaQueryChainWrapper.gt(Customer::getCdpClockCount,3);
        }

    }

    /**
     * 计算本月打卡次数SQL
     * @param customerLambdaQueryChainWrapper
     * @param currentMonthClockCountEnum
     */
    private void calculateCurrentMonthClockCountSql(LambdaQueryChainWrapper<Customer> customerLambdaQueryChainWrapper, Integer currentMonthClockCountEnum) {

        if(currentMonthClockCountEnum == null) {
            return;
        }

        if(currentMonthClockCountEnum.equals(0)) {
            customerLambdaQueryChainWrapper.eq(Customer::getCdpCurrentMonthClockCount,0);
        }else if(currentMonthClockCountEnum.equals(1)) {
            customerLambdaQueryChainWrapper.eq(Customer::getCdpCurrentMonthClockCount,1);
        }else if (currentMonthClockCountEnum.equals(2)) {
            customerLambdaQueryChainWrapper.eq(Customer::getCdpCurrentMonthClockCount,2);
        }else if (currentMonthClockCountEnum.equals(3)) {
            customerLambdaQueryChainWrapper.eq(Customer::getCdpCurrentMonthClockCount,3);
        }else if (currentMonthClockCountEnum.equals(4)) {
            customerLambdaQueryChainWrapper.gt(Customer::getCdpCurrentMonthClockCount,3);
        }

    }

    /**
     * 计算已保护天数SQL
     * @param customerLambdaQueryChainWrapper
     * @param dayEnum
     */
    private void calculateProtectDaySql(LambdaQueryChainWrapper<Customer> customerLambdaQueryChainWrapper, Integer dayEnum) {

        if(dayEnum == null) {
            return;
        }

        if(dayEnum.equals(0)) {
            customerLambdaQueryChainWrapper.gt(Customer::getCdpProtectDay,0);
            customerLambdaQueryChainWrapper.le(Customer::getCdpProtectDay,30);
        }else if (dayEnum.equals(1)) {
            customerLambdaQueryChainWrapper.gt(Customer::getCdpProtectDay,30);
            customerLambdaQueryChainWrapper.le(Customer::getCdpProtectDay,60);
        }else if (dayEnum.equals(2)) {
            customerLambdaQueryChainWrapper.gt(Customer::getCdpProtectDay,60);
            customerLambdaQueryChainWrapper.le(Customer::getCdpProtectDay,90);
        }else if (dayEnum.equals(3)) {
            customerLambdaQueryChainWrapper.gt(Customer::getCdpProtectDay,90);
        }

    }

    /**
     * <AUTHOR>
     * @date 2025/7/14 18:46:42
     * @return org.elasticsearch.index.query.BoolQueryBuilder
     * @desc 根据字段类型,字段值构建通用查询条件
     */
    private BoolQueryBuilder buildBoolQueryBuilder(Object dto) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        Map<String, RangeQueryBuilder> rangeMap = new HashMap<>();
        Map<String, BoolQueryBuilder> orGroups = new HashMap<>();
        Field[] fields = dto.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(EsIgnore.class)) {
                continue; // 忽略字段
            }

            field.setAccessible(true);
            try {
                Object value = field.get(dto);
                if (isEffectivelyEmpty(value)) continue;

                String fieldName = field.getName();
                String esFieldName = fieldName;

                // 读取字段名映射
                if (field.isAnnotationPresent(EsField.class)) {
                    esFieldName = field.getAnnotation(EsField.class).name();
                }

                boolean useMatch = field.isAnnotationPresent(EsMatch.class);
                boolean forceNumeric = field.isAnnotationPresent(EsNumeric.class);
                Class<?> numericType = forceNumeric ? field.getAnnotation(EsNumeric.class).type() : null;
                EsRange esRange = field.getAnnotation(EsRange.class);
                EsDateFormat dateFormat = field.getAnnotation(EsDateFormat.class);
                boolean isDate = dateFormat != null;
                Class<?> fieldType = field.getType();
                QueryBuilder singleQuery = null;
                // ========== 1. 区间查询 (优先) ==========
                // 日期和时间类型优先使用区间查询的方式
                if (esRange != null) {
                    if (value.toString().contains(",")) {
                        String[] parts = value.toString().split(",");
                        RangeQueryBuilder range = QueryBuilders.rangeQuery(esFieldName);
                        if (parts.length > 0 && !parts[0].trim().isEmpty())
                            range.gte(convertRangeValue(parts[0].trim(), isDate, dateFormat, true));
                        if (parts.length > 1 && !parts[1].trim().isEmpty())
                            range.lte(convertRangeValue(parts[1].trim(), isDate, dateFormat, false));
                        if (isDate) range.format(dateFormat.value());
                        singleQuery = range;
                    } else {
                        singleQuery = QueryBuilders.termsQuery(esFieldName, value.toString());
                    }
                }
                // String类型,暂时排除日期格式的字符串，下面单独处理,对于多值查询，优先使用逗号分割的方式，通过注解来确定ES字段类型
                else if (fieldType == String.class && !((String) value).matches("\\d{4}-\\d{2}-\\d{2}.*")) {
                    if (useMatch) {
                        boolQuery.must(QueryBuilders.matchPhraseQuery(esFieldName, value.toString()));
                        continue;
                    } else if (((String) value).contains(",")) {
                        String[] parts = ((String) value).split(",");
                        List<Object> values = Arrays.stream(parts)
                                .map(String::trim)
                                .filter(s -> !s.isEmpty())
                                .collect(Collectors.toList());
                        if (forceNumeric) values = convertListToNumeric(values, numericType);
                        singleQuery = QueryBuilders.termsQuery(esFieldName, values);
                    } else {
                        //不打分，查询更快，更语义清晰，能缓存
                        singleQuery = QueryBuilders.termsQuery(esFieldName, value.toString());
                    }
                }
                // ========== 数组 / 集合 → terms ==========
                else if (value instanceof Collection || value.getClass().isArray()) {
                    List<Object> list = toList(value);
                    if (forceNumeric) list = convertListToNumeric(list, numericType);
                    if (!list.isEmpty()) {
                        singleQuery = QueryBuilders.termsQuery(esFieldName, list);
                    }
                }
                // 数值类型
                else if (Number.class.isAssignableFrom(fieldType)) {
                    singleQuery = QueryBuilders.termsQuery(esFieldName, value);
                }
                // 日期范围处理
                else if (Date.class.isAssignableFrom(fieldType)
                        || fieldType == LocalDate.class
                        || fieldType == LocalDateTime.class
                        || (fieldType == String.class && ((String) value).matches("\\d{4}-\\d{2}-\\d{2}.*"))) {
                    Object formattedDate = value;

                    if (fieldType == String.class) {
                        String strVal = value.toString();
                        String[] parts = strVal.split(",");
                        if (parts.length == 2) {
                            RangeQueryBuilder range = QueryBuilders.rangeQuery(esFieldName);
                            if (!parts[0].trim().isEmpty())
                                range.gte(convertRangeValue(parts[0].trim(), isDate, dateFormat, true));
                            if (!parts[1].trim().isEmpty())
                                range.lte(convertRangeValue(parts[1].trim(), isDate, dateFormat, false));
                            if (isDate) range.format(dateFormat.value());
                            singleQuery = range;
                        }
                    } else if (fieldType == LocalDate.class) {
                        formattedDate = ((LocalDate) value).toString(); // 格式为 yyyy-MM-dd
                    } else if (fieldType == LocalDateTime.class) {
                        // 将 LocalDateTime 转为字符串，格式为 Elasticsearch 所支持的时间格式（比如 "yyyy-MM-dd'T'HH:mm:ss"）
                        formattedDate = ((LocalDateTime) value).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                    }
                    if (fieldName.endsWith("Start") || fieldName.endsWith("End")) {
                        String baseEsField = esFieldName;
                        RangeQueryBuilder range = rangeMap.computeIfAbsent(baseEsField, QueryBuilders::rangeQuery);

                        if (fieldName.endsWith("Start")) {
                            range.gte(formattedDate);
                        } else {
                            range.lte(formattedDate);
                        }
                        rangeMap.put(baseEsField, range);
                    } else {
                        singleQuery = QueryBuilders.rangeQuery(esFieldName).gte(formattedDate).lte(formattedDate);
                    }
                }
                // TODO: 可扩展数组、集合、布尔类型、Nested 等
                else {
                    singleQuery = QueryBuilders.termsQuery(esFieldName, value);
                }
                //如果 singleQuery 是空，跳过该字段
                if (singleQuery == null) {
                    continue;
                }
                // ========== 是否属于 OR 分组 ==========
                EsOrGroup orGroup = field.getAnnotation(EsOrGroup.class);
                if (orGroup != null) {
                    String groupName = orGroup.value();
                    BoolQueryBuilder groupQuery = orGroups.computeIfAbsent(groupName, k -> QueryBuilders.boolQuery());
                    groupQuery.should(singleQuery);
                } else {
                    boolQuery.filter(singleQuery);
                }
            } catch (IllegalAccessException e) {
                log.error("buildBoolQueryBuilder失败,param={}", JSONObject.toJSONString(dto));
            }
        }
        // 加入 range 查询
        for (RangeQueryBuilder rangeQuery : rangeMap.values()) {
            boolQuery.filter(rangeQuery);
        }
        // 把所有 OR 组拼进主 boolQuery
        for (BoolQueryBuilder groupQuery : orGroups.values()) {
            groupQuery.minimumShouldMatch(1);
            boolQuery.filter(groupQuery);
        }
        return boolQuery;
    }

    /**
     * <AUTHOR>
     * @date 2025/7/23 09:30:27
     * @return void
     * @desc 设置排序条件,field1:asc,field2:desc,field3:field4,字段没有指定排序方式的话，用全局的排序方式
     */
    private void setSourceBuilder(Object dto, SearchSourceBuilder sourceBuilder) {
        try {
            Field orderByField = dto.getClass().getDeclaredField("orderBy");
            Field ascFlagField = dto.getClass().getDeclaredField("ascFlag");

            orderByField.setAccessible(true);
            ascFlagField.setAccessible(true);

            String orderBy = (String) orderByField.get(dto);
            //基础类型 boolean 没有 null 值，但默认是 false, 即默认是降序
            boolean ascFlag = ascFlagField.getBoolean(dto);

            if (orderBy == null || orderBy.trim().isEmpty()) {
                return; // 没有排序字段，直接跳过
            }

            String[] fields = orderBy.split(",");
            for (String fieldSpec : fields) {
                fieldSpec = fieldSpec.trim();
                if (fieldSpec.isEmpty()) continue;

                String fieldName = fieldSpec;
                SortOrder sortOrder = ascFlag ? SortOrder.ASC : SortOrder.DESC;

                // 支持 "field:asc" 这种写法
                if (fieldSpec.contains(":")) {
                    String[] parts = fieldSpec.split(":");
                    fieldName = parts[0].trim();
                    if (parts.length > 1) {
                        String dir = parts[1].trim().toLowerCase();
                        if ("asc".equals(dir)) {
                            sortOrder = SortOrder.ASC;
                        } else if ("desc".equals(dir)) {
                            sortOrder = SortOrder.DESC;
                        }
                    }
                }
                if (!fieldName.isEmpty()) {
                    sourceBuilder.sort(fieldName, sortOrder);
                }
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("setSourceBuilder失败,param={}", JSONObject.toJSONString(dto));
        }
    }

    /**
     * <AUTHOR>
     * @date 2025/7/23 09:29:47
     * @return boolean
     * @desc 判断值是否为 null、空字符串、空集合、空数组
     */
    private boolean isEffectivelyEmpty(Object value) {
        if (value == null) return true;
        if (value instanceof String && ((String) value).trim().isEmpty()) return true;
        if (value instanceof Collection && ((Collection<?>) value).isEmpty()) return true;
        if (value.getClass().isArray() && Array.getLength(value) == 0) return true;
        return false;
    }

   /**
    * <AUTHOR>
    * @date 2025/7/23 09:29:14
    * @return java.util.Date
    * @desc LocalDateTime转换为Date类型
    */
    private Date safeConvertLocalDateTimeToDate(LocalDateTime localDateTime) {
        return localDateTime != null
                ? Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant())
                : null;
    }

    /** 数组/集合统一转 List */
    private static List<Object> toList(Object value) {
        if (value instanceof Collection) {
            return ((Collection<?>) value).stream().filter(Objects::nonNull).collect(Collectors.toList());
        } else if (value.getClass().isArray()) {
            return Arrays.stream((Object[]) value).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /** 数组/集合 → 指定数值类型 */
    private static List<Object> convertListToNumeric(List<Object> list, Class<?> type) {
        return list.stream().map(val -> convert(val, type)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /** 单值转换 */
    private static Object convert(Object val, Class<?> type) {
        if (!(val instanceof String)) return val;
        try {
            String str = ((String) val).trim();
            if (type == Long.class) return Long.parseLong(str);
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private static Object convertRangeValue(String val, boolean isDate, EsDateFormat dateFormat, boolean isStart) {
        if (isDate) {
            // 日期型：自动补时分秒
            if (val.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
                if (dateFormat.autoCompleteTime()) {
                    return isStart ? val + "T00:00:00" : val + "T23:59:59";
                }
            }
            //匹配 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy-MM-ddTHH:mm:ss"
            else if (val.matches("^(\\d{4}-\\d{2}-\\d{2})\\s+(\\d{2}:\\d{2}:\\d{2})$")) {
                return val.replaceFirst("^(\\d{4}-\\d{2}-\\d{2})\\s+(\\d{2}:\\d{2}:\\d{2})$", "$1T$2");
            }
            return val;
        } else {
            // 数值型
            try {
                if (val.contains(".")) return Double.parseDouble(val);
                return Long.parseLong(val);
            } catch (NumberFormatException e) {
                return val;
            }
        }
    }
}