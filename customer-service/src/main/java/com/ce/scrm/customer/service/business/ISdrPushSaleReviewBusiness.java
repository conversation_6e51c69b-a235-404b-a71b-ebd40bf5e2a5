package com.ce.scrm.customer.service.business;

import com.ce.scrm.customer.service.business.entity.dto.abm.SdrPushSaleReviewBusinessAddDto;
import com.ce.scrm.customer.service.business.entity.dto.abm.SdrPushSaleReviewBusinessUpdateDto;
import com.ce.scrm.customer.service.business.entity.view.abm.SdrPushSaleReviewBusinessViewDto;

import java.util.List;
import java.util.Optional;

public interface ISdrPushSaleReviewBusiness {
    boolean add(SdrPushSaleReviewBusinessAddDto reviewBusinessAddDto);

    Optional<List<SdrPushSaleReviewBusinessViewDto>> getCustomerReviewList(String customerId);

    Optional<SdrPushSaleReviewBusinessViewDto> getToBeReview(String customerId);

    Long countToBeReview(String customerId);

    Optional<String> review(SdrPushSaleReviewBusinessUpdateDto reviewBusinessUpdateDto);
}
