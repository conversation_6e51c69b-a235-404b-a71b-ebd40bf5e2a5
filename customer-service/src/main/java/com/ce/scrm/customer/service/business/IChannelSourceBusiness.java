package com.ce.scrm.customer.service.business;

import com.ce.scrm.customer.service.business.entity.dto.ChannelSourceBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.ContactInfoBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.ChannelSourceBusinessView;
import com.ce.scrm.customer.service.business.entity.view.ContactInfoBusinessView;

import java.util.List;

/**
 * 客户业务接口
 * <AUTHOR>
 * @date 2023/4/13
 * @version 1.0.0
 */
public interface IChannelSourceBusiness {

    /**
     * 获取渠道数据
     * @param channelSourceBusinessDto 查询参数
     * <AUTHOR>
     * @date 2023/4/20 13:35
     * @return com.ce.scrm.customer.support.business.entity.view.ChannelSourceBusinessView
     **/
    ChannelSourceBusinessView getBySourceKey(ChannelSourceBusinessDto channelSourceBusinessDto);
}