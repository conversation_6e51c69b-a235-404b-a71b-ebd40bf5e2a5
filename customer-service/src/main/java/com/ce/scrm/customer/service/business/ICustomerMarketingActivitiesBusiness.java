package com.ce.scrm.customer.service.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.service.business.entity.dto.abm.CustomerMarketingActivitiesBusinessPageDto;
import com.ce.scrm.customer.service.business.entity.dto.abm.CustomerMarketingActivitiesBusinessReqDto;
import com.ce.scrm.customer.service.business.entity.dto.abm.CustomerMarketingActivitiesBusinessUpdateDto;
import com.ce.scrm.customer.service.business.entity.view.abm.CustomerMarketingActivitiesBusinessView;

/**
 * 客户营销活动业务接口
 * <AUTHOR>
 * @date 2025/7/9
 * @version 1.0.0
 */
public interface ICustomerMarketingActivitiesBusiness {


	/**
	 * 新建营销活动信息
	 * @param reqDto 活动信息
	 * @return 是否新建成功
	 */
	Long save(CustomerMarketingActivitiesBusinessReqDto reqDto);


	/**
	 * 修改营销活动信息
	 * @param reqDto 活动信息
	 * @return 是否新建成功
	 */
	Boolean updateById(CustomerMarketingActivitiesBusinessUpdateDto reqDto);


	/**
	 * 获取客户营销活动详情
	 * @param pageDto 查询参数
	 * <AUTHOR>
	 * @date 2025/7/9 16:15
	 * @return com.ce.scrm.customer.support.business.entity.view.CustomerMarketingActivitiesBusinessView
	 */
	Page<CustomerMarketingActivitiesBusinessView> page(CustomerMarketingActivitiesBusinessPageDto pageDto);

	/**
	 * 获取客户营销活动详情
	 * @param activitiesId 查询参数
	 * <AUTHOR>
	 * @date 2025/7/9 16:15
	 * @return com.ce.scrm.customer.support.business.entity.view.CustomerMarketingActivitiesBusinessView
	 */
	CustomerMarketingActivitiesBusinessView detail(Long activitiesId);

}