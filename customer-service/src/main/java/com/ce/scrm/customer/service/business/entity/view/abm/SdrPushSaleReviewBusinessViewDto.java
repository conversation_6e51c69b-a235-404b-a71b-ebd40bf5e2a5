package com.ce.scrm.customer.service.business.entity.view.abm;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * sdr推送销售审核表
 */
@Data
public class SdrPushSaleReviewBusinessViewDto implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 地址
     */
    private String address;

    /**
     * 跟进详情
     */
    private String followDetails;

    /**
     * 审核状态 0:待审核 1:审核通过
     */
    private Integer reviewStatus;

    /**
     * 创建者
     */
    private String createdId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 审核人id
     */
    private String reviewId;

    /**
     * 审核时间
     */
    private Date reviewTime;

}