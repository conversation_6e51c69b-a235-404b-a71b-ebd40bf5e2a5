package com.ce.scrm.customer.service.enums;

/**
 * 客户类型枚举
 * <AUTHOR>
 * @date 2023/4/6 13:57
 * @version 1.0.0
 **/
public enum CustomerTypeEnum {
    /**
     * 企业
     */
    company(1),
    /**
     * 个人
     */
    personal(2),
    /**
     * 国外及港澳台
     */
    overseas_hongkong_macao_taiwan(3),
    ;

    private final Integer type;

    CustomerTypeEnum(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }
}