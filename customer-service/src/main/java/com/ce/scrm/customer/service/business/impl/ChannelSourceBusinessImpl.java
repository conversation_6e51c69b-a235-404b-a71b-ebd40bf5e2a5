package com.ce.scrm.customer.service.business.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.customer.dao.entity.CustomerContact;
import com.ce.scrm.customer.dao.service.CustomerContactService;
import com.ce.scrm.customer.service.business.IChannelSourceBusiness;
import com.ce.scrm.customer.service.business.ICustomerContactBusiness;
import com.ce.scrm.customer.service.business.entity.dto.ChannelSourceBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.CustomerContactBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.*;
import com.ce.scrm.customer.service.cache.entity.*;
import com.ce.scrm.customer.service.cache.handler.ChannelSourceCacheHandler;
import com.ce.scrm.customer.service.cache.handler.CustomerContactCacheHandler;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 客户业务实现
 * <AUTHOR>
 * @date 2023/4/13 13:56
 * @version 1.0.0
 */
@Slf4j
@Service
public class ChannelSourceBusinessImpl implements IChannelSourceBusiness {

    @Resource
    private ChannelSourceCacheHandler channelSourceCacheHandler;

    @Override
    public ChannelSourceBusinessView getBySourceKey(ChannelSourceBusinessDto channelSourceBusinessDto) {

        ChannelSourceCacheData channelSourceCacheData = channelSourceCacheHandler.get(channelSourceBusinessDto.getSourceKey());
        if(channelSourceCacheData == null){
            return null;
        }

        return BeanUtil.copyProperties(channelSourceCacheData, ChannelSourceBusinessView.class);
    }
}