package com.ce.scrm.customer.service.business;

import com.ce.scrm.customer.dao.entity.CustomerLeads;

import java.util.List;
import java.util.Map;

/**
 * 客户leads业务接口
 * <AUTHOR>
 * @date 2025/7/25
 * @version 1.0.0
 */
public interface ICustomerLeadsBusiness {


	/**
	 * 获取客户对应的leads信息map
	 * @param custIds 客户id列表
	 * @return cust-leads map
	 */
	Map<String, List<CustomerLeads>> getCustIdLeadsMap(List<String> custIds);




}