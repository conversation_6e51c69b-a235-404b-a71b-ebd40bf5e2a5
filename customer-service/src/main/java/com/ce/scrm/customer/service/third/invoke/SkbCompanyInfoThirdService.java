package com.ce.scrm.customer.service.third.invoke;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.ce.scrm.customer.service.third.entity.view.CompanyInfoData;
import com.ce.scrm.customer.service.third.entity.view.CompanyInfoThirdView;
import com.ce.scrm.extend.dubbo.api.ICompanyInfoEsDubbo;
import com.ce.scrm.extend.dubbo.entity.view.CompanyInfoDubboView;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 搜客宝公司三方业务
 * <AUTHOR>
 * @date 2024/1/19 21:16
 * @version 1.0.0
 */
@Service
@Slf4j
public class SkbCompanyInfoThirdService {

    @DubboReference(group = "scrm-extend-api", version = "1.0.0", timeout = 35000)
    private ICompanyInfoEsDubbo companyInfoEsDubbo;

    /**
     * 获取KA公司信息
     * <AUTHOR>
     * @date 2024/1/19 21:38
     * @return java.util.List<com.ce.scrm.customer.service.third.entity.view.CompanyInfoData>
     **/
    public List<CompanyInfoData> getKaCompanyList() {
        List<CompanyInfoDubboView> kaCompanyInfoList = companyInfoEsDubbo.getKaCompanyInfoList();
        if (CollectionUtil.isEmpty(kaCompanyInfoList)) {
            return Lists.newArrayList();
        }
        return BeanUtil.copyToList(kaCompanyInfoList, CompanyInfoData.class);
    }

    /*
     * @Description 根据pid获取公司集合信息
     * <AUTHOR>
     * @date 2025/2/11 15:14
     * @param pid
     * @return java.util.List<com.ce.scrm.customer.service.third.entity.view.CompanyInfoThirdView>
     */
    public List<CompanyInfoThirdView> listByPid(String pid){
        log.info("listByPid 入参为pid={}",pid);
        List<CompanyInfoDubboView> companyInfoDubboViews = companyInfoEsDubbo.listByPid(pid);
        return BeanUtil.copyToList(companyInfoDubboViews, CompanyInfoThirdView.class);
    }

    /*
     * @Description 根据pid获取公司信息
     * <AUTHOR>
     * @date 2025/2/11 15:15
     * @param pid
     * @return com.ce.scrm.customer.service.third.entity.view.CompanyInfoThirdView
     */
    public CompanyInfoThirdView oneByPid(String pid){
        List<CompanyInfoThirdView> companyInfoThirdViews = listByPid(pid);
        if(CollectionUtil.isEmpty(companyInfoThirdViews)) {
            return null;
        }
        return BeanUtil.copyProperties(companyInfoThirdViews.get(0), CompanyInfoThirdView.class);
    }
}