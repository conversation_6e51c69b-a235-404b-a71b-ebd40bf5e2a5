package com.ce.scrm.customer.service.business.entity.dto;

import lombok.Data;

import java.util.Date;

/**
 * 客户更新字段参数
 * <AUTHOR>
 * @date 2023/5/15 11:38
 * @version 1.0.0
 */
@Data
public class CustomerTagUpdateBusinessDto {

    /**
     * 大数据id
     */
    private String sourceDataId;
    /**
     * 客户id
     */
    private String customerId;
    /**
     * 更新人
     */
    private String operator;

    /**
     * 是否是ka用户
     */
    private Integer tagKa;

    /**
     * 是否是外贸用户
     */
    private Integer tagWaimao;

    /**
     * 是否门户连带客户 0:否，1:是
     */
    private Integer tagMenhuRelated;

    /**
     * 是否生态转门户 0:否，1:是
     */
    private Integer tagEcoToMenhu;

    /**
     * 是否生态客户 0:否，1:是
     */
    private Integer tagEcoCust;

    /**
     * 是否数字版本门户 0:否，1:是
     */
    private Integer tagMenhuDigital;

    /**
     * 是否2023版本门户 0:否，1:是
     */
    private Integer tagMenhu2023;

    /**
     * 是否低版本门户 0:否，1:是
     */
    private Integer tagMenhuLowver;

    /**
     * 是否门户应升已升客户 0:否，1:是
     */
    private Integer tagMenhuUpgradeableUpgrade;

    /**
     * 是否门户应升级客户 0:否，1:是
     */
    private Integer tagMenhuUpgradeable;

    /**
     * 是否门户应续已续客户 0:否，1:是
     */
    private Integer tagMenhuRenewableRenew;

    /**
     * 是否门户应续客户 0:否，1:是
     */
    private Integer tagMenhuRenewable;

    /**
     * 是否交叉购买客户 0:否，1:是
     */
    private Integer tagCrossBuy;

    /**
     * 是否纯门户客户 0:否，1:是
     */
    private Integer tagPureMenhu;

    /**
     * 搜客宝 1:规模工业，2:规上服务业，3:规上建筑业，4:规上批发零售业，5:规上住宿餐饮业，6:规上房地产开发与经营业
     */
    private String tagFlag7;

    /**
     * 搜客宝 行业 1:律师，2:学校，3:医院
     */
    private String tagFlag8;

    /**
     * 是否是上市企业 1是0否
     */
    private String tagFlag12;

    /**
     * 注册资金
     */
    private String regCapUnify;

    /**
     * 科技型企业
     */
    private String tagTechcompany;

    /**
     * 门户新客户首次购买时间
     */
    private Date tagMenhuNewTime;

    /**
     * 门户新客户首次购买产品类别 0:低版本 1:门户2023 2:数字门户
     */
    private String tagMenhuNewCategory;
}