package com.ce.scrm.customer.service.business;

import com.ce.scrm.customer.service.business.entity.view.IndustryBusinessView;

import java.util.List;

/**
 * 客户业务接口
 * <AUTHOR>
 * @date 2023/4/13
 * @version 1.0.0
 */
public interface IIndustryBusiness {

    /**
     * 获取行业数据
     * <AUTHOR>
     * @date 2023/4/14 17:35
     **/
    List<IndustryBusinessView> getAllData();

    /**
     * 根据行业名称获取行业信息
     * @param name  行业名称
     * @param isFirst   是否一级行业
     * <AUTHOR>
     * @date 2024/4/28 下午1:49
     * @return com.ce.scrm.customer.service.business.entity.view.IndustryBusinessView
     **/
    IndustryBusinessView getIndustryByName(String name, boolean isFirst);
}