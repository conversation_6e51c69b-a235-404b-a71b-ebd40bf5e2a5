package com.ce.scrm.customer.service.business.impl.abm;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.dao.entity.abm.CustomerMarketingActivities;
import com.ce.scrm.customer.dao.mapper.abm.CustomerMarketingActivitiesMapper;
import com.ce.scrm.customer.dao.service.abm.CustomerMarketingActivitiesService;
import com.ce.scrm.customer.service.business.ICustomerMarketingActivitiesBusiness;
import com.ce.scrm.customer.service.business.entity.dto.abm.CustomerMarketingActivitiesBusinessPageDto;
import com.ce.scrm.customer.service.business.entity.dto.abm.CustomerMarketingActivitiesBusinessReqDto;
import com.ce.scrm.customer.service.business.entity.dto.abm.CustomerMarketingActivitiesBusinessUpdateDto;
import com.ce.scrm.customer.service.business.entity.view.abm.CustomerMarketingActivitiesBusinessView;
import com.ce.scrm.customer.service.enums.CustomerMarketingActivityExecuteStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 跨境ABM 客户营销活动
 * <AUTHOR>
 * @date 2025/7/9 14:56
 * @version 1.0.0
 */
@Slf4j
@Service
public class CustomerMarketingActivitiesBusinessImpl implements ICustomerMarketingActivitiesBusiness {

	private static final String PREFIX = "HD";
    @Resource
    private CustomerMarketingActivitiesService customerMarketingActivitiesService;
	@Resource
    private CustomerMarketingActivitiesMapper customerMarketingActivitiesMapper;
	@Resource
	private RestTemplate xxljobRestTemplate;

	/**
	 * 创建营销活动
	 * @param reqDto 活动信息
	 * @return 是否创建成功
	 */
	@Transactional
	@Override
	public Long save(CustomerMarketingActivitiesBusinessReqDto reqDto) {
		CustomerMarketingActivities activities = BeanUtil.copyProperties(reqDto, CustomerMarketingActivities.class);
		activities.setId(null);
		activities.setExecuteState(CustomerMarketingActivityExecuteStateEnum.WAIT_EXECUTE.getCode());
		boolean save = customerMarketingActivitiesService.save(activities);
		if (!save) {
			throw new RuntimeException("保存营销活动失败");
		}
		Long id = activities.getId();
		String activityCode = PREFIX + String.format("%05d", id);
		// 生成 activityCode活动编码: PREFIX + 左补零后的ID（5位数）再更新一次 activityCode
		CustomerMarketingActivities update = new CustomerMarketingActivities();
		update.setId(id);
		update.setActivityCode(activityCode);
		customerMarketingActivitiesService.updateById(update);
		return id;
	}

	/**
	 * 修改营销活动信息
	 * @param reqDto 活动信息
	 * @return 是否修改成功
	 */
	@Override
	public Boolean updateById(CustomerMarketingActivitiesBusinessUpdateDto reqDto) {
		boolean updateRs = customerMarketingActivitiesService.lambdaUpdate()
			.eq(CustomerMarketingActivities::getId, reqDto.getId())
			.set(StringUtils.isNotBlank(reqDto.getActivityName()), CustomerMarketingActivities::getActivityName, reqDto.getActivityName())
			// .set(CustomerMarketingActivities::getActivityType, reqDto.getActivityType())
			.set(StringUtils.isNotBlank(reqDto.getTemplateId()), CustomerMarketingActivities::getTemplateId, reqDto.getTemplateId())
			.set(StringUtils.isNotBlank(reqDto.getActivityContent()), CustomerMarketingActivities::getActivityContent, reqDto.getActivityContent())
			.set(StringUtils.isNotBlank(reqDto.getActivityUrl()), CustomerMarketingActivities::getActivityUrl, reqDto.getActivityUrl())
			.set(StringUtils.isNotBlank(reqDto.getSegmentId()), CustomerMarketingActivities::getSegmentId, reqDto.getSegmentId())
			.set(Objects.nonNull(reqDto.getSegmentCustCount()), CustomerMarketingActivities::getSegmentCustCount, reqDto.getSegmentCustCount())
			.set(StringUtils.isNotBlank(reqDto.getSegmentName()), CustomerMarketingActivities::getSegmentName, reqDto.getSegmentName())
			.set(CustomerMarketingActivities::getUpdateBy, reqDto.getUpdateBy())
			.set(CustomerMarketingActivities::getUpdatedTime, reqDto.getUpdateTime())
			.update();

		return updateRs;
	}

    /**
     * 获取客户营销活动详情
     * @param activitiesId 活动id
     * <AUTHOR>
     * @date 2025/7/9 16:18
     */
	@Override
	public CustomerMarketingActivitiesBusinessView detail(Long activitiesId) {
		CustomerMarketingActivities activity = customerMarketingActivitiesService.lambdaQuery()
			.eq(CustomerMarketingActivities::getId, activitiesId)
			.one();
		if (Objects.isNull(activity)) {
			log.warn("根据活动名称或id未查询到对应的活动，活动id为:{}", activitiesId);
			return null;
		}
		return BeanUtil.copyProperties(activity, CustomerMarketingActivitiesBusinessView.class);
	}

	/**
	 * 营销活动列表
	 * @param pageDto 查询参数
	 * @return 营销活动列表
	 */
	@Override
	public Page<CustomerMarketingActivitiesBusinessView> page(CustomerMarketingActivitiesBusinessPageDto pageDto) {
		LambdaQueryChainWrapper<CustomerMarketingActivities> wrapper = customerMarketingActivitiesService.lambdaQuery();
		wrapper.eq(StringUtils.isNotBlank(pageDto.getId()), CustomerMarketingActivities::getId, pageDto.getId());
		wrapper.eq(StringUtils.isNotBlank(pageDto.getActivityCode()), CustomerMarketingActivities::getActivityCode, pageDto.getActivityCode());
		wrapper.like(StringUtils.isNotBlank(pageDto.getActivityName()), CustomerMarketingActivities::getActivityName, pageDto.getActivityName());
		wrapper.eq(StringUtils.isNotBlank(pageDto.getCreateBy()), CustomerMarketingActivities::getCreateBy, pageDto.getCreateBy());
		wrapper.eq(Objects.nonNull(pageDto.getExecuteState()), CustomerMarketingActivities::getExecuteState, pageDto.getExecuteState());
		wrapper.ge(Objects.nonNull(pageDto.getCreateTimeStart()), CustomerMarketingActivities::getCreatedTime, pageDto.getCreateTimeStart());
		wrapper.le(Objects.nonNull(pageDto.getCreateTimeEnd()), CustomerMarketingActivities::getCreatedTime, pageDto.getCreateTimeEnd());
		wrapper.orderByDesc(CustomerMarketingActivities::getCreatedTime);
		Page<CustomerMarketingActivities> pageReq = Page.of(pageDto.getPageNum(), pageDto.getPageSize());
		Page<CustomerMarketingActivities> entitiesPages = wrapper.page(pageReq);
		if (!CollectionUtils.isEmpty(entitiesPages.getRecords())) {
			Page<CustomerMarketingActivitiesBusinessView> returnPage = new Page<>();
			returnPage.setCurrent(entitiesPages.getCurrent());
			returnPage.setSize(entitiesPages.getSize());
			returnPage.setTotal(entitiesPages.getTotal());
			List<CustomerMarketingActivitiesBusinessView> busRsRecord = BeanUtil.copyToList(entitiesPages.getRecords(), CustomerMarketingActivitiesBusinessView.class);
			returnPage.setRecords(busRsRecord);
			return returnPage;
		}
		return Page.of(entitiesPages.getCurrent(), entitiesPages.getSize(), entitiesPages.getTotal());
	}
}