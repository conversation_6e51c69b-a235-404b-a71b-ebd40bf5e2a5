package com.ce.scrm.customer.service.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * @version 1.0
 * @Description: 跨境ABM 客户营销活动的触达方式
 * @Author: xukang
 * @Date: 2025/7/9 14:56
 */
@Getter
@AllArgsConstructor
public enum CustomerMarketingActivityTypeEnum {

    SMS(1,"短信"),
	EMAIL(2,"邮件"),
	PHONE(3,"电话"),
	OTHERS(4,"其他")
    ;

    /** 编码 */
    private final Integer code;
    /** 值 */
    private final String value;

	/**
	 * 获取触达方式枚举
	 * @param code 触达方式编码
	 * @return 枚举值
	 */
	public static CustomerMarketingActivityTypeEnum of(Integer code) {
		Objects.requireNonNull(code, "enum code not allow null");
		return Stream.of(values())
			.filter(C -> C.getCode().equals(code))
			.findAny()
			.orElse(null);
	}

	/**
	 * 支持创建活动的类型
	 * @return 短信和邮件
	 */
	public static List<Integer> supportCreateActivityType() {
		return Lists.newArrayList(SMS.getCode(), EMAIL.getCode());
	}

}