package com.ce.scrm.customer.service.cache.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ce.scrm.customer.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.customer.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.cache.entity.CustomerCacheData;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 客户缓存处理器
 * <AUTHOR>
 * @date 2023/4/13 14:27
 * @version 1.0.0
 */
@Slf4j
@Service
public class CustomerCacheHandler extends AbstractStringCacheHandler<CustomerCacheData> {

    @Resource
    private CustomerService customerService;

    /**
     * 获取业务缓存的key
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return com.ce.scrm.customer.cache.enumeration.CacheKeyEnum
     **/
    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.CUSTOMER;
    }

    /**
     * 指定对象类型
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return java.lang.Class<T>
     **/
    @Override
    protected Class<CustomerCacheData> getClazz() {
        return CustomerCacheData.class;
    }

    /**
     * 获取客户数据
     *
     * @param customerId    客户ID
     * <AUTHOR>
     * @date 2023/4/13 14:06
     * @return com.ce.scrm.customer.cache.CustomerCacheData
     **/
    @Override
    protected CustomerCacheData queryDataBySource(String customerId) {
        if (StrUtil.isBlank(customerId)) {
            return null;
        }
        Customer customer = customerService.lambdaQuery()
                .eq(Customer::getCustomerId, customerId)
                /*.eq(Customer::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())*/
                .last("limit 1").one();
        if (customer == null) {
            return null;
        }
        return BeanUtil.copyProperties(customer, getClazz());
    }
}