package com.ce.scrm.customer.service.business.impl.abm;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ce.scrm.customer.dao.entity.abm.SdrPushSaleReview;
import com.ce.scrm.customer.dao.service.abm.SdrPushSaleReviewService;
import com.ce.scrm.customer.service.business.ISdrPushSaleReviewBusiness;
import com.ce.scrm.customer.service.business.entity.dto.abm.SdrPushSaleReviewBusinessAddDto;
import com.ce.scrm.customer.service.business.entity.dto.abm.SdrPushSaleReviewBusinessUpdateDto;
import com.ce.scrm.customer.service.business.entity.view.abm.SdrPushSaleReviewBusinessViewDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/***
 * <AUTHOR>
 * @date 2025/7/16 10:48
 * @version 1.0.0
 * @return
 **/
@Slf4j
@Service
public class SdrPushSaleReviewBusinessImpl implements ISdrPushSaleReviewBusiness {

    @Resource
    private SdrPushSaleReviewService sdrPushSaleReviewService;

    /***
     * 添加审核记录
     * @param reviewBusinessAddDto
     * <AUTHOR>
     * @date 2025/7/16 16:23
     * @version 1.0.0
     * @return java.util.Optional<com.ce.scrm.center.dao.entity.SdrPushSaleReview>
     **/
    @Override
    public boolean add(SdrPushSaleReviewBusinessAddDto reviewBusinessAddDto) {
        log.info("添加审核记录={}", JSON.toJSONString(reviewBusinessAddDto));
        SdrPushSaleReview entity = new SdrPushSaleReview();
        BeanUtils.copyProperties(reviewBusinessAddDto, entity);
        entity.setCreateTime(new Date());
        entity.setReviewStatus(0);
        entity.setId(null);
        boolean save = sdrPushSaleReviewService.save(entity);
        if (!save) {
            log.error("添加审核记录失败，entity={}", JSON.toJSONString(reviewBusinessAddDto));
            return false;
        } else {
            reviewBusinessAddDto.setId(entity.getId());
            return true;
        }
    }

    /***
     * 获取客户审核记录集合
     * @param customerId
     * <AUTHOR>
     * @date 2025/7/16 18:44
     * @version 1.0.0
     * @return java.util.Optional<java.util.List < com.ce.scrm.center.service.business.entity.dto.SdrPushSaleReviewBusinessViewDto>>
     **/
    @Override
    public Optional<List<SdrPushSaleReviewBusinessViewDto>> getCustomerReviewList(String customerId) {
        log.info("获取客户审核记录集合 customerId={}", customerId);
        List<SdrPushSaleReview> list = sdrPushSaleReviewService.list(new LambdaQueryWrapper<SdrPushSaleReview>()
                .eq(SdrPushSaleReview::getCustomerId, customerId));
        List<SdrPushSaleReviewBusinessViewDto> sdrPushSaleReviewBusinessViewDtos = BeanUtil.copyToList(list, SdrPushSaleReviewBusinessViewDto.class);
        return Optional.of(sdrPushSaleReviewBusinessViewDtos);
    }

    /***
     * 获取待审核的记录
     * @param customerId
     * <AUTHOR>
     * @date 2025/7/16 18:47
     * @version 1.0.0
     * @return java.util.Optional<com.ce.scrm.center.service.business.entity.dto.SdrPushSaleReviewBusinessViewDto>
     **/
    @Override
    public Optional<SdrPushSaleReviewBusinessViewDto> getToBeReview(String customerId) {
        log.info("获取待审核的记录 customerId={}", customerId);
        SdrPushSaleReview pushSaleReview = sdrPushSaleReviewService.getOne(new LambdaQueryWrapper<SdrPushSaleReview>()
                .eq(SdrPushSaleReview::getCustomerId, customerId)
                .eq(SdrPushSaleReview::getReviewStatus, 0)
        );
        if (Objects.isNull(pushSaleReview)) {
            return Optional.empty();
        }
        SdrPushSaleReviewBusinessViewDto sdrPushSaleReviewBusinessViewDto = BeanUtil.copyProperties(pushSaleReview, SdrPushSaleReviewBusinessViewDto.class);
        return Optional.of(sdrPushSaleReviewBusinessViewDto);
    }

    /***
     * 获取待审核的记录数
     * @param customerId
     * <AUTHOR>
     * @date 2025/7/16 18:49
     * @version 1.0.0
     * @return java.lang.Long
     **/
    @Override
    public Long countToBeReview(String customerId) {
        log.info("获取待审核的记录数 customerId={} ", customerId);
        return sdrPushSaleReviewService.count(new LambdaQueryWrapper<SdrPushSaleReview>()
                .eq(SdrPushSaleReview::getCustomerId, customerId)
                .eq(SdrPushSaleReview::getReviewStatus, 0)
        );
    }

    /***
     * 审核
     * @param reviewBusinessUpdateDto
     * <AUTHOR>
     * @date 2025/7/16 18:54
     * @version 1.0.0
     * @return java.util.Optional<java.lang.String>
     **/
    @Override
    public Optional<String> review(SdrPushSaleReviewBusinessUpdateDto reviewBusinessUpdateDto) {
        log.info("审核 : {}", JSON.toJSONString(reviewBusinessUpdateDto));
        Long id = reviewBusinessUpdateDto.getId();
        Integer reviewStatus = reviewBusinessUpdateDto.getReviewStatus();
        String reviewId = reviewBusinessUpdateDto.getReviewId();


        SdrPushSaleReview sdrPushSaleReview = sdrPushSaleReviewService.getById(id);
        if (Objects.isNull(sdrPushSaleReview)) {
            return Optional.of("找不到对应记录");
        }

        Integer curReviewStatus = sdrPushSaleReview.getReviewStatus();
        if (!Objects.equals(curReviewStatus, 0)) {
            return Optional.of("不是待审核状态,无法审核");
        }
        boolean update = sdrPushSaleReviewService.lambdaUpdate()
                .eq(SdrPushSaleReview::getId, id)
                .set(SdrPushSaleReview::getReviewStatus, reviewStatus)
                .set(SdrPushSaleReview::getReviewId, reviewId)
                .set(SdrPushSaleReview::getReviewTime, new Date())
                .update();
        if (!update) {
            log.error("审核失败 {}", JSON.toJSONString(reviewBusinessUpdateDto));
            return Optional.of("审核失败");
        } else {
            return Optional.empty();
        }
    }

}
