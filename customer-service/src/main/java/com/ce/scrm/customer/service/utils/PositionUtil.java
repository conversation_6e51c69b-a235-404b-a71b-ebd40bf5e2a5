package com.ce.scrm.customer.service.utils;

import org.apache.commons.lang3.StringUtils;

public class PositionUtil {

    public PositionUtil() {
    }

    public static boolean isBusinessManager(String position) {
        if (StringUtils.isEmpty(position)) {
            return false;
        } else {
            return "商务经理".equals(position) || "客户总监".equals(position);
        }
    }

    public static boolean isBusinessMajor(String position) {
        if (StringUtils.isEmpty(position)) {
            return false;
        } else {
            return "分公司总监".equals(position) || "全国客户总监".equals(position);
        }
    }

    public static boolean isBusinessSaler(String position) {
        if (StringUtils.isEmpty(position)) {
            return false;
        } else {
            return "客户代表".equals(position) || "商务代表".equals(position) || "商务主管".equals(position) || "大客户经理".equals(position);
        }
    }

}
