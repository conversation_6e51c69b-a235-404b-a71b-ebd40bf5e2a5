package com.ce.scrm.customer.service.business.entity.dto.abm;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description ABM 营销活动创建
 * <AUTHOR>
 * @Date 2025-07-09 14:45
 */
@Data
public class CustomerMarketingActivitiesBusinessPageDto implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 页号
	 */
	private Integer pageNum = 1;

	/**
	 * 页码
	 */
	private Integer pageSize = 10;

	/**
	 * 活动id
	 */
	private String id;

	/**
	 * 活动code
	 */
	private String activityCode;

	/**
	 * 活动名称
	 */
	private String activityName;

	/**
	 * 创建人id
	 */
	private String createBy;

	/**
	 * 执行状态
	 */
	private Integer executeState;

	/**
	 * 创建时间开始
	 */
	private Date createTimeStart;

	/**
	 * 创建时间结束
	 */
	private Date createTimeEnd;
}
