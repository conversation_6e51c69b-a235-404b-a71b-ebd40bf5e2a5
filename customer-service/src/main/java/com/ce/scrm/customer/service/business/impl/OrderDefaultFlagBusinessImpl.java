package com.ce.scrm.customer.service.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.ce.scrm.customer.dao.entity.CustomerContact;
import com.ce.scrm.customer.dao.entity.OrderDefaultFlag;
import com.ce.scrm.customer.dao.service.CustomerContactService;
import com.ce.scrm.customer.dao.service.OrderDefaultFlagService;
import com.ce.scrm.customer.service.business.IOrderDefaultFlagBusiness;
import com.ce.scrm.customer.service.enums.ContactInfoEnum;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订单默认标记业务实现
 * <AUTHOR>
 * @date 2023/5/22 10:29
 * @version 1.0.0
 */
@Slf4j
@Service
public class OrderDefaultFlagBusinessImpl implements IOrderDefaultFlagBusiness {

    @Resource
    private OrderDefaultFlagService orderDefaultFlagService;

    @Resource
    private CustomerContactService customerContactService;

    /**
     * 获取订单默认标记的数据
     *
     * @param contactPersonId 联系人ID
     * @param phoneList       手机号列表
     * @param emailList       邮箱列表
     * <AUTHOR>
     * @date 2023/5/22 10:28
     * @return com.ce.scrm.customer.dao.entity.OrderDefaultFlag
     **/
    @Override
    public OrderDefaultFlag get(String contactPersonId, List<String> phoneList, List<String> emailList) {
        if (StrUtil.isBlank(contactPersonId) || CollectionUtil.isEmpty(phoneList) || CollectionUtil.isEmpty(emailList)) {
            log.warn("获取订单默认联系人标记，必要参数为空");
            return null;
        }
        return orderDefaultFlagService.lambdaQuery().eq(OrderDefaultFlag::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(OrderDefaultFlag::getContactPersonId, contactPersonId)
                .in(OrderDefaultFlag::getPhone, phoneList)
                .in(OrderDefaultFlag::getEmail, emailList)
                .one();
    }

    /**
     * 订单添加标记
     *
     * @param contactPersonId 联系人ID
     * @param phone           手机号
     * @param email           邮箱
     * @param operator        操作人
     * <AUTHOR>
     * @date 2023/5/23 09:47
     * @return boolean
     **/
    @Override
    public boolean add(String contactPersonId, String phone, String email, String operator) {
        if (StrUtil.isBlank(contactPersonId) || StrUtil.isBlank(phone) || StrUtil.isBlank(email) || StrUtil.isBlank(operator)) {
            log.warn("添加订单默认联系人标记，必要参数不能为空");
            return false;
        }
        CustomerContact customerContact = customerContactService.lambdaQuery().eq(CustomerContact::getContactPersonId, contactPersonId).one();
        if (customerContact == null) {
            log.warn("添加订单默认联系人标记，联系人不存在，联系人ID为:{}", contactPersonId);
            return false;
        }
        //当前联系人的其他数据都删除
        orderDefaultFlagService.lambdaUpdate().eq(OrderDefaultFlag::getCustomerId, customerContact.getCustomerId()).set(OrderDefaultFlag::getDeleteFlag, DeleteFlagEnum.DELETE.getCode()).update();
        //添加标记
        OrderDefaultFlag orderDefaultFlag = new OrderDefaultFlag();
        orderDefaultFlag.setCustomerId(customerContact.getCustomerId());
        orderDefaultFlag.setContactPersonId(contactPersonId);
        orderDefaultFlag.setPhone(phone);
        orderDefaultFlag.setEmail(email);
        orderDefaultFlag.setCreator(operator);
        orderDefaultFlag.setOperator(operator);
        return orderDefaultFlagService.save(orderDefaultFlag);
    }

    /**
     * 校验是否是默认签单人
     *
     * @param contactPersonId 联系人ID
     * @param contactInfoEnum 联系方式类型
     * @param contactWay      联系方式
     * <AUTHOR>
     * @date 2023/5/25 15:41
     * @return boolean
     **/
    @Override
    public boolean check(String contactPersonId, ContactInfoEnum contactInfoEnum, String contactWay) {
        LambdaQueryChainWrapper<OrderDefaultFlag> lambdaQueryChainWrapper = orderDefaultFlagService.lambdaQuery().eq(OrderDefaultFlag::getContactPersonId, contactPersonId).eq(OrderDefaultFlag::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode());
        if (contactInfoEnum == ContactInfoEnum.mobile) {
            lambdaQueryChainWrapper.eq(OrderDefaultFlag::getPhone, contactWay);
        } else if (contactInfoEnum == ContactInfoEnum.email) {
            lambdaQueryChainWrapper.eq(OrderDefaultFlag::getEmail, contactWay);
        } else {
            return false;
        }
        return lambdaQueryChainWrapper.exists();
    }
}