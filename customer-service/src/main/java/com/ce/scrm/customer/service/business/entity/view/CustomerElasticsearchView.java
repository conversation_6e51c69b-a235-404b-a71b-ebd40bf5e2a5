package com.ce.scrm.customer.service.business.entity.view;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *  elasticsearch 客户信息
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/9/11 09:33
 */
@Data
public class CustomerElasticsearchView implements Serializable {
    /**
     * 客户ID
     */
    private String custId;
    /**
     * 客户证件号
     */
    private String custIdNumber;
    /**
     * 客户名字
     */
    private String custNameCn;
    /**
     * 客户类型
     */
    private Integer custType;
    /**
     * 更新时间
     */
    private Date doneDate;
    /**
     * 客户创建时间
     */
    private Date custCreatTime;
    /**
     * 数据写入时间
     */
    private Date createDate;
}
