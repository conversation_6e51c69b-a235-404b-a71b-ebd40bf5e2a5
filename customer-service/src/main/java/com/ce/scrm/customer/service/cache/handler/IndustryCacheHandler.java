package com.ce.scrm.customer.service.cache.handler;

import cn.hutool.core.bean.BeanUtil;
import com.ce.scrm.customer.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.customer.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.customer.dao.entity.Industry;
import com.ce.scrm.customer.dao.service.IndustryService;
import com.ce.scrm.customer.service.cache.entity.IndustryCacheData;
import com.ce.scrm.customer.service.cache.entity.IndustryListCacheData;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 行业缓存处理器
 * <AUTHOR>
 * @date 2023/4/13 14:27
 * @version 1.0.0
 */
@Service
public class IndustryCacheHandler extends AbstractStringCacheHandler<IndustryListCacheData> {

    @Resource
    private IndustryService industryService;

    /**
     * 获取业务缓存的key
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return com.ce.scrm.customer.cache.enumeration.CacheKeyEnum
     **/
    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.INDUSTRY;
    }

    /**
     * 指定对象类型
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return java.lang.Class<T>
     **/
    @Override
    protected Class<IndustryListCacheData> getClazz() {
        return IndustryListCacheData.class;
    }

    /**
     * 获取全部行业缓存数据
     *
     * @param sourceKey
     * <AUTHOR>
     * @date 2023/4/13 14:06
     * @return com.ce.scrm.customer.cache.IndustryCacheData
     **/
    @Override
    protected IndustryListCacheData queryDataBySource(String sourceKey) {
        List<Industry> industryList = industryService.lambdaQuery().eq(Industry::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();
        List<IndustryCacheData> industryCacheDataList = Optional.of(industryList).orElse(Lists.newArrayList()).stream().map(record->{
            IndustryCacheData industryCacheData = new IndustryCacheData();
            BeanUtil.copyProperties(record,industryCacheData);
            return industryCacheData;
        }).collect(Collectors.toList());
        IndustryListCacheData industryListCacheData = new IndustryListCacheData();
        industryListCacheData.setList(industryCacheDataList);
        return industryListCacheData;
    }
}