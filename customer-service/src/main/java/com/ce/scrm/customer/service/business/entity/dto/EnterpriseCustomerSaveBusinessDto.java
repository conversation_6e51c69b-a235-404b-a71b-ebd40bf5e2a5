package com.ce.scrm.customer.service.business.entity.dto;

import lombok.Data;

/**
 * 企业客户保存业务参数
 * <AUTHOR>
 * @date 2024/2/29 10:24
 * @version 1.0.0
 */
@Data
public class EnterpriseCustomerSaveBusinessDto {
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户当前阶段：1、线索，2、商机，3、保有客户，4、流失客户
     */
    private Integer presentStage;

    /**
     * 客户创建方式：1、大数据，2、商务创建，3、市场商机，4、会销
     */
    private Integer createWay;

    /**
     * 操作人ID
     */
    private String operator;
}