package com.ce.scrm.customer.service.third.entity.view;

import lombok.Data;

import java.io.Serializable;

/**
 * 员工三方数据
 * <AUTHOR>
 * @date 2023/5/15 16:22
 * @version 1.0.0
 */
@Data
public class CompleteOrgInfo implements Serializable {

    private String employeeId;

    private String employeeName;

    /**
     * 所属机构
     */
    private String orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 大区名称
     */
    private String areaName;
    /**
     * 大区id
     */
    private String areaId;
    /**
     * 分司名称
     */
    private String subName;
    /**
     * 分司id
     */
    private String subId;
    /**
     * 事业部 id
     */
    private String buId;
    /**
     * 事业部名称
     */
    private String buName;

}