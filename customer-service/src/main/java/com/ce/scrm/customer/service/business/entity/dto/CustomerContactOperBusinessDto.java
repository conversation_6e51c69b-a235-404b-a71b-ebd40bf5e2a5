package com.ce.scrm.customer.service.business.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 联系方式操作数据
 * <AUTHOR>
 * @date 2023/4/7 17:31
 * @version 1.0.0
 */
@Data
public class CustomerContactOperBusinessDto {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 删除标记：1、删除，0、未删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者凭证
     */
    private String creatorKey;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人凭证
     */
    private String operatorKey;

    /**
     * 更新人
     */
    private String operator;
}