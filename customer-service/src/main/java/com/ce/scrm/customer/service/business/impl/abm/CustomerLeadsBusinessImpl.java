package com.ce.scrm.customer.service.business.impl.abm;

import com.ce.scrm.customer.dao.entity.CustomerLeads;
import com.ce.scrm.customer.dao.service.abm.CustomerLeadsService;
import com.ce.scrm.customer.service.business.ICustomerLeadsBusiness;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 跨境ABM 客户营销活动
 * <AUTHOR>
 * @date 2025/7/9 14:56
 * @version 1.0.0
 */
@Slf4j
@Service
public class CustomerLeadsBusinessImpl implements ICustomerLeadsBusiness {

	@Resource
	private CustomerLeadsService customerLeadsService;

	/**
	 * 获取活动id leads列表 Map
	 * @param custIds 客户id列表
	 * @return map
	 */
	@Override
	public Map<String, List<CustomerLeads>> getCustIdLeadsMap(List<String> custIds) {
		if (CollectionUtils.isEmpty(custIds)){
			return Collections.emptyMap();
		}

		List<CustomerLeads> leadsList = customerLeadsService.lambdaQuery().in(CustomerLeads::getCustomerId, custIds).list();
		if (CollectionUtils.isEmpty(leadsList)) {
			log.warn("customer leads not exist in customerIds = {}", custIds);
			return Collections.emptyMap();
		}

		return leadsList.stream().collect(Collectors.groupingBy(CustomerLeads::getCustomerId));
	}
}