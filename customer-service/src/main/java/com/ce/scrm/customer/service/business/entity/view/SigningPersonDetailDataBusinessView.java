package com.ce.scrm.customer.service.business.entity.view;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 获取签单联系人数据（给其他系统返回信息）
 * <AUTHOR>
 * @date 2024/9/5
 * @version 1.0.0
 **/
public class SigningPersonDetailDataBusinessView implements Serializable {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 联系人姓名
     */
    private String contactPersonName;

    /**
     * 电话
     */
    private String phone;

    /**
     * 签单联系人 0、不是，1、临时，2、正式'
     */
    private Integer signingPersonFlag;

    /**
     * 签单联系人更新时间
     */
    private LocalDateTime signingPersonUpdate;

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getContactPersonId() {
        return contactPersonId;
    }

    public void setContactPersonId(String contactPersonId) {
        this.contactPersonId = contactPersonId;
    }

    public String getContactPersonName() {
        return contactPersonName;
    }

    public void setContactPersonName(String contactPersonName) {
        this.contactPersonName = contactPersonName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getSigningPersonFlag() {
        return signingPersonFlag;
    }

    public void setSigningPersonFlag(Integer signingPersonFlag) {
        this.signingPersonFlag = signingPersonFlag;
    }

    public LocalDateTime getSigningPersonUpdate() {
        return signingPersonUpdate;
    }

    public void setSigningPersonUpdate(LocalDateTime signingPersonUpdate) {
        this.signingPersonUpdate = signingPersonUpdate;
    }
}