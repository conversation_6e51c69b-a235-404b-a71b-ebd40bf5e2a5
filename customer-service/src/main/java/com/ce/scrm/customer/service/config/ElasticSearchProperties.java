package com.ce.scrm.customer.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/7/27 10:10
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "elasticsearch")
public class ElasticSearchProperties {

    private String hosts;

    private String userName;

    private String password;
}
