package com.ce.scrm.customer.service.business.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.cache.access.BaseManager;
import com.ce.scrm.customer.dao.entity.ContactInfo;
import com.ce.scrm.customer.dao.entity.ContactPerson;
import com.ce.scrm.customer.dao.entity.CustomerContact;
import com.ce.scrm.customer.dao.entity.OrderDefaultFlag;
import com.ce.scrm.customer.dao.entity.view.ContactPersonLegal;
import com.ce.scrm.customer.dao.mapper.ContactPersonMapper;
import com.ce.scrm.customer.dao.service.ContactInfoService;
import com.ce.scrm.customer.dao.service.ContactPersonService;
import com.ce.scrm.customer.dao.service.CustomerContactService;
import com.ce.scrm.customer.service.business.*;
import com.ce.scrm.customer.service.business.entity.dto.*;
import com.ce.scrm.customer.service.business.entity.view.*;
import com.ce.scrm.customer.service.cache.entity.ContactInfoCacheData;
import com.ce.scrm.customer.service.cache.entity.ContactPersonCacheData;
import com.ce.scrm.customer.service.cache.handler.ContactPersonCacheHandler;
import com.ce.scrm.customer.service.cache.handler.CustomerContactCacheHandler;
import com.ce.scrm.customer.service.config.UniqueIdService;
import com.ce.scrm.customer.service.enums.BindTypeEnum;
import com.ce.scrm.customer.service.enums.ContactInfoEnum;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import com.ce.scrm.customer.service.third.entity.view.BigDataCompanyDetail;
import com.ce.scrm.customer.service.third.invoke.BigDataThirdService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 客户业务实现
 * <AUTHOR>
 * @date 2023/4/13 13:56
 * @version 1.0.0
 */
@Slf4j
@Service
public class ContactPersonBusinessImpl implements IContactPersonBusiness {

    @Resource
    private UniqueIdService uniqueIdService;

    @Resource
    private ContactPersonCacheHandler contactPersonCacheHandler;

    @Resource
    private CustomerContactCacheHandler customerContactCacheHandler;

    @Resource
    private IChannelSourceBusiness channelSourceBusiness;

    @Resource
    private ContactPersonService contactPersonService;

    @Resource
    private ContactInfoService contactInfoService;

    @Resource
    private CustomerContactService customerContactService;

    @Resource
    private IOrderDefaultFlagBusiness orderDefaultFlagBusiness;

    @Resource
    private BaseManager baseManager;

    @Resource
    private BigDataThirdService bigDataThirdService;

    @Resource
    ContactPersonMapper contactPersonMapper;

    @Resource
    ICustomerContactBusiness customerContactBusiness;

    @Resource
    private ICustomerBusiness customerBusiness;

    /**
     * 获取客户数据
     *
     * @param contactPersonBusinessDto 查询参数
     * <AUTHOR>
     * @date 2023/4/13 13:58
     * @return com.ce.scrm.customer.support.business.entity.view.CustomerBusinessView
     **/
    @Override
    public ContactPersonBusinessView detail(ContactPersonBusinessDto contactPersonBusinessDto) {
        ContactPersonCacheData contactPersonCacheData = contactPersonCacheHandler.get(contactPersonBusinessDto.getContactPersonId());
        if (contactPersonCacheData == null) {
            return null;
        }
        ContactPersonBusinessView contactPersonBusinessView = BeanUtil.copyProperties(contactPersonCacheData, ContactPersonBusinessView.class);
        // 设置联系人sourceTag
        String sourceKey = contactPersonBusinessView.getSourceKey();
        AtomicReference<String> sourceTag = new AtomicReference<>(sourceKey);
        List<ContactInfoCacheData> contactInfoList = contactPersonCacheData.getContactInfoList();
        if ("scrm".equals(sourceKey) && CollectionUtil.isNotEmpty(contactInfoList)) {
            List<String> phoneList = contactInfoList.stream().filter(contactInfoCacheData -> ContactInfoEnum.mobile.getType().equals(contactInfoCacheData.getContactType())).map(ContactInfoCacheData::getContactWay).collect(Collectors.toList());
            List<String> emailList = contactInfoList.stream().filter(contactInfoCacheData -> ContactInfoEnum.email.getType().equals(contactInfoCacheData.getContactType())).map(ContactInfoCacheData::getContactWay).collect(Collectors.toList());
            Optional<OrderDefaultFlag> orderDefaultFlag = Optional.ofNullable(orderDefaultFlagBusiness.get(contactPersonBusinessView.getContactPersonId(), phoneList, emailList));
            orderDefaultFlag.ifPresent(o -> sourceTag.set(sourceTag.get() + ",order"));
        }
        if (StringUtils.isNotBlank(sourceTag.get())) {
            String[] sourceTagArray = sourceTag.get().split(",");
            StringBuilder sbSourceTag = new StringBuilder();
            for (int i = 0; i < sourceTagArray.length; i++) {
                ChannelSourceBusinessDto channelSourceBusinessDto = new ChannelSourceBusinessDto();
                channelSourceBusinessDto.setSourceKey(sourceTagArray[i]);
                ChannelSourceBusinessView bySourceKey = channelSourceBusiness.getBySourceKey(channelSourceBusinessDto);
                if (bySourceKey != null) {
                    sbSourceTag.append(bySourceKey.getSourceShowName());
                    if (i != sourceTagArray.length - 1) {
                        sbSourceTag.append(",");
                    }
                }
            }
            contactPersonBusinessView.setSourceTag(sbSourceTag.toString());
        }
        ChannelSourceBusinessDto channelSourceBusinessDto = new ChannelSourceBusinessDto();
        channelSourceBusinessDto.setSourceKey(sourceKey);
        ChannelSourceBusinessView bySourceKey = channelSourceBusiness.getBySourceKey(channelSourceBusinessDto);
        if (bySourceKey != null) {
            contactPersonBusinessView.setSourceKey(bySourceKey.getSourceName());
        }
        return contactPersonBusinessView;
    }

    /**
     * 获取联系人数据
     * @param contactPersonId   联系人ID
     * <AUTHOR>
     * @date 2023/5/17 10:45
     * @return com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView
     **/
    @Override
    public ContactPersonBusinessView detail(String contactPersonId) {
        ContactPersonCacheData contactPersonCacheData;
        if (StrUtil.isBlank(contactPersonId) || (contactPersonCacheData = contactPersonCacheHandler.get(contactPersonId)) == null) {
            return null;
        }
        return BeanUtil.copyProperties(contactPersonCacheData, ContactPersonBusinessView.class);
    }

    /**
     * 获取联系人数据
     *
     * @param customerId 客户ID
     * @param phone      手机号
     * <AUTHOR>
     * @date 2023/5/16 16:18
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView>
     **/
    @Override
    public List<ContactPersonBusinessView> detail(String customerId, String phone) {
        //获取客户下所有的联系人ID
        List<CustomerContact> customerContactList = customerContactService.lambdaQuery().eq(CustomerContact::getCustomerId, customerId).eq(CustomerContact::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();
        if (CollectionUtil.isEmpty(customerContactList)) {
            log.warn("当前客户下没有联系人数据，客户ID为:{}", customerId);
            return null;
        }
        List<String> contactPersonIdList = customerContactList.stream().map(CustomerContact::getContactPersonId).collect(Collectors.toList());
        //根据联系人ID和手机号获取满足条件的联系方式
        List<ContactInfo> contactInfoList = contactInfoService.lambdaQuery().eq(ContactInfo::getContactWay, phone).eq(ContactInfo::getContactType, ContactInfoEnum.mobile.getType()).in(ContactInfo::getContactPersonId, contactPersonIdList).eq(ContactInfo::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();
        if (CollectionUtil.isEmpty(contactInfoList)) {
            log.warn("当前客户下手机号没有对应的联系人，客户ID为:{}, 手机号为:{}", customerId, phone);
            return null;
        }
        //根据联系方式获取所有满足条件的联系人数据
        List<ContactPersonCacheData> contactPersonCacheDataList = contactInfoList.stream().map(ContactInfo::getContactPersonId).distinct().map(contactPersonId -> contactPersonCacheHandler.get(contactPersonId)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(contactPersonCacheDataList)) {
            log.warn("当前客户下手机号对应的联系人不存在，联系方式数据为:{}", JSON.toJSONString(contactInfoList));
            return null;
        }
        return contactPersonCacheDataList.stream().map(contactPersonCacheData -> {
            ContactPersonBusinessView contactPersonBusinessView = BeanUtil.copyProperties(contactPersonCacheData, ContactPersonBusinessView.class);
            List<ContactInfoBusinessView> contactInfoBusinessViewList = BeanUtil.copyToList(contactPersonCacheData.getContactInfoList(), ContactInfoBusinessView.class);
            contactPersonBusinessView.setContactInfoList(contactInfoBusinessViewList);
            return contactPersonBusinessView;
        }).collect(Collectors.toList());
    }

    /**
     * 获取签单联系人数据
     * @param contactPersonName  联系人姓名
     * @param phone   手机号
     * <AUTHOR>
     * @date 2024/9/5
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.SigningPersonDetailDataBusinessView>
     **/
    @Override
    public List<CustomerBusinessView> getSigningPersonDataDetail(String contactPersonName, String phone) {
        //根据手机号获取所有contactInfo
        List<ContactInfo> contactInfoList = contactInfoService.lambdaQuery()
                .eq(ContactInfo::getContactType, ContactInfoEnum.mobile.getType())
                .eq(ContactInfo::getContactWay, phone)
                .eq(ContactInfo::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .list();
        if (CollectionUtil.isEmpty(contactInfoList)){
            log.warn("根据手机号未查询到对应的联系方法，手机号:{}", phone);
            return Lists.newArrayList();
        }

        //根据姓名和手机号获取所有contactPerson 只获取contactPersonId
        List<ContactPerson> contactPersonList = contactPersonService.lambdaQuery()
                .select(ContactPerson::getContactPersonId)
                .eq(ContactPerson::getContactPersonName, contactPersonName)
                .in(ContactPerson::getContactPersonId, contactInfoList.stream().map(ContactInfo::getContactPersonId).collect(Collectors.toList()))
                .in(ContactPerson::getSigningPersonFlag, Arrays.asList(1, 2))
                .eq(ContactPerson::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .list();
        if (CollectionUtil.isEmpty(contactInfoList)){
            log.warn("根据姓名和手机号未查询到对应的联系人，姓名:{}, 手机号:{}", contactPersonName, phone);
            return null;
        }

        //根据联系人获取对应的customerContact的所有customerId
        List<CustomerContact> customerContactList = customerContactService.lambdaQuery()
                .in(CustomerContact::getContactPersonId, contactPersonList.stream().map(ContactPerson::getContactPersonId).collect(Collectors.toList()))
                .eq(CustomerContact::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .list();
        if (CollectionUtil.isEmpty(customerContactList)){
            log.warn("根据联系人未查询到对应的客户联系人，联系人:{}", JSON.toJSONString(contactPersonList));
            return null;
        }

        //根据联系方式获取所有满足条件的联系人数据
        List<ContactPersonCacheData> contactPersonCacheDataList = contactPersonList.stream().map(ContactPerson::getContactPersonId).distinct().map(contactPersonId -> contactPersonCacheHandler.get(contactPersonId)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(contactPersonCacheDataList)) {
            log.warn("当前客户下手机号对应的联系人不存在，联系方式数据为:{}", JSON.toJSONString(contactInfoList));
            return null;
        }

        List<ContactPersonBusinessView> contactPersonBusinessViewList = contactPersonCacheDataList.stream().map(contactPersonCacheData -> {
            ContactPersonBusinessView contactPersonBusinessView = BeanUtil.copyProperties(contactPersonCacheData, ContactPersonBusinessView.class);
            List<ContactInfoBusinessView> contactInfoBusinessViewList = BeanUtil.copyToList(contactPersonCacheData.getContactInfoList(), ContactInfoBusinessView.class);
            contactPersonBusinessView.setContactInfoList(contactInfoBusinessViewList);
            return contactPersonBusinessView;
        }).collect(Collectors.toList());

        //分组所有的customerId
        Set<String> customerIds = customerContactList.stream()
                .map(CustomerContact::getCustomerId)
                .collect(Collectors.toSet());

        //返回类型
        List<CustomerBusinessView> customerBusinessViewReturnList = new ArrayList<>();
        //根据customerId获取对应的customer
        customerIds.forEach(customerId -> {
            //根据customerId获取对应的customer
            CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
            customerBusinessDto.setCustomerId(customerId);
            Optional<CustomerBusinessView> customerBusinessViewOptional = Optional.ofNullable(customerBusiness.detail(customerBusinessDto));
            if (!customerBusinessViewOptional.isPresent()){
                log.warn("根据customerId未查询到对应的客户，customerId:{}", customerId);
                return;
            }
            CustomerBusinessView customerBusinessView = customerBusinessViewOptional.get();
            Set<String> contactPersonIds = customerContactList.stream()
                    .filter(customerContact -> customerContact.getCustomerId().equals(customerId))
                    .map(CustomerContact::getContactPersonId)
                    .collect(Collectors.toSet());
            //找到对应的contactPersonList放置到customerBusinessView中
            List<ContactPersonBusinessView> contactPersonBusinessViewListForCustomer = contactPersonBusinessViewList.stream()
                    .filter(contactPersonBusinessView -> contactPersonIds.contains(contactPersonBusinessView.getContactPersonId()))
                    .collect(Collectors.toList());
            customerBusinessView.setContactPersonList(contactPersonBusinessViewListForCustomer);
            customerBusinessViewReturnList.add(customerBusinessView);

        });
        return customerBusinessViewReturnList;
    }

    /**
     * 添加联系人
     *
     * @param contactPersonAddBusinessDto 联系人数据
     * <AUTHOR>
     * @date 2023/5/15 13:38
     * @return boolean
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(ContactPersonAddBusinessDto contactPersonAddBusinessDto) {
        if (contactPersonAddBusinessDto == null) {
            log.warn("添加联系人，数据不能为空");
            return false;
        }
        //如果联系人ID没有传入，则在此生成
        if (StrUtil.isBlank(contactPersonAddBusinessDto.getContactPersonId())) {
            contactPersonAddBusinessDto.setContactPersonId(uniqueIdService.getId());
        }
        //客户联系人入库
        CustomerContact customerContact = new CustomerContact();
        customerContact.setCustomerId(contactPersonAddBusinessDto.getCustomerId());
        customerContact.setContactPersonId(contactPersonAddBusinessDto.getContactPersonId());
        customerContact.setCreator(contactPersonAddBusinessDto.getOperator());
        customerContact.setOperator(contactPersonAddBusinessDto.getOperator());
        boolean customerContactFlag = customerContactService.save(customerContact);
        //联系人入库
        boolean contactPersonFlag = contactPersonService.save(packageContactPerson(contactPersonAddBusinessDto));
        //联系方式入库
        List<ContactInfo> contactInfoList = packageContactInfo(contactPersonAddBusinessDto);
        boolean contactInfoFlag = true;
        if (CollectionUtil.isNotEmpty(contactInfoList)) {
            contactInfoFlag = contactInfoService.saveBatch(contactInfoList);
        }
        contactPersonCacheHandler.del(contactPersonAddBusinessDto.getContactPersonId());
        customerContactCacheHandler.del(contactPersonAddBusinessDto.getCustomerId());
        return customerContactFlag && contactPersonFlag && contactInfoFlag;
    }

    /**
     * 拼装新增联系人数据
     * @param contactPersonAddBusinessDto   联系人数据
     * <AUTHOR>
     * @date 2023/5/15 13:57
     * @return com.ce.scrm.customer.dao.entity.ContactPerson
     **/
    private ContactPerson packageContactPerson(ContactPersonAddBusinessDto contactPersonAddBusinessDto) {
        ContactPerson contactPerson = BeanUtil.copyProperties(contactPersonAddBusinessDto, ContactPerson.class);
        if (contactPerson.getGender() == null) {
            contactPerson.setGender(0);
        }
        contactPerson.setCreator(contactPersonAddBusinessDto.getOperator());
        return contactPerson;
    }

    /**
     * 拼装更新联系方式数据
     * @param contactPersonAddBusinessDto   联系人数据
     * <AUTHOR>
     * @date 2023/5/15 13:57
     * @return com.ce.scrm.customer.dao.entity.ContactPerson
     **/
    private List<ContactInfo> packageContactInfo(ContactPersonAddBusinessDto contactPersonAddBusinessDto) {
        List<ContactPersonAddBusinessDto.ContactInfoAddBusinessDto> contactInfoList = contactPersonAddBusinessDto.getContactInfoList();
        if (CollectionUtil.isEmpty(contactInfoList)) {
            return null;
        }
        return contactInfoList.stream().map(contactInfoAddBusinessDto -> {
            ContactInfo contactInfo = BeanUtil.copyProperties(contactInfoAddBusinessDto, ContactInfo.class);
            contactInfo.setContactInfoId(uniqueIdService.getId());
            contactInfo.setContactPersonId(contactPersonAddBusinessDto.getContactPersonId());
            contactInfo.setSourceKey(contactPersonAddBusinessDto.getSourceKey());
            contactInfo.setCreator(contactPersonAddBusinessDto.getOperator());
            contactInfo.setOperator(contactPersonAddBusinessDto.getOperator());
            return contactInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 更新联系人
     *
     * @param contactPersonUpdateBusinessDto 联系人数据
     * <AUTHOR>
     * @date 2023/5/15 13:38
     * @return boolean
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ContactPersonUpdateBusinessDto contactPersonUpdateBusinessDto) {
        if (contactPersonUpdateBusinessDto == null || StrUtil.isBlank(contactPersonUpdateBusinessDto.getContactPersonId())) {
            log.warn("更新联系人，联系人ID不能为空");
            return false;
        }
        //更新联系人
        boolean updateContactPersonFlag = updateContactPerson(contactPersonUpdateBusinessDto);
        //更新联系方式
        boolean updateContactInfoFlag = true;
        if (CollectionUtil.isNotEmpty(contactPersonUpdateBusinessDto.getContactInfoList())) {
            updateContactInfoFlag = updateContactInfo(contactPersonUpdateBusinessDto);
        }
        contactPersonCacheHandler.del(contactPersonUpdateBusinessDto.getContactPersonId());
        CustomerContact customerContact = customerContactService.lambdaQuery().eq(CustomerContact::getContactPersonId, contactPersonUpdateBusinessDto.getContactPersonId()).one();
        if (customerContact != null) {
            customerContactCacheHandler.del(customerContact.getCustomerId());
        }
        return updateContactPersonFlag && updateContactInfoFlag;
    }

    /**
     * 更新联系人手机号是否已验证
     * @param contactPersonUpdateBusinessDto
     * <AUTHOR>
     * @date 2023/11/22 11:13
     * @version 1.0.0
     * @return boolean
     **/
    @Override
    public boolean updatePhoneVerifiedFlag(ContactPersonUpdateBusinessDto contactPersonUpdateBusinessDto) {
        if (contactPersonUpdateBusinessDto == null || CollectionUtil.isEmpty(contactPersonUpdateBusinessDto.getContactInfoList())) {
            log.warn("更新手机号是否已验证，联系人方式不能为空");
            return false;
        }
        return updateContactPhoneVerifiedFlag(contactPersonUpdateBusinessDto);
    }

    /**
     * 更新联系人数据
     *
     * @param contactPersonUpdateBusinessDto    更新参数
     * <AUTHOR>
     * @date 2023/5/16 11:10
     * @return boolean
     **/
    private boolean updateContactPerson(ContactPersonUpdateBusinessDto contactPersonUpdateBusinessDto) {
        LambdaUpdateChainWrapper<ContactPerson> updateChainWrapper = contactPersonService.lambdaUpdate().eq(ContactPerson::getContactPersonId, contactPersonUpdateBusinessDto.getContactPersonId());
        updateChainWrapper.set(ContactPerson::getUpdateTime, LocalDateTime.now());
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getContactPersonName())) {
            updateChainWrapper.set(ContactPerson::getContactPersonName, contactPersonUpdateBusinessDto.getContactPersonName());
        }
        if (contactPersonUpdateBusinessDto.getGender() != null) {
            updateChainWrapper.set(ContactPerson::getGender, contactPersonUpdateBusinessDto.getGender());
        }
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getDepartment())) {
            updateChainWrapper.set(ContactPerson::getDepartment, contactPersonUpdateBusinessDto.getDepartment());
        }
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getPosition())) {
            updateChainWrapper.set(ContactPerson::getPosition, contactPersonUpdateBusinessDto.getPosition());
        }
        if (contactPersonUpdateBusinessDto.getCertificatesType() != null) {
            updateChainWrapper.set(ContactPerson::getCertificatesType, contactPersonUpdateBusinessDto.getCertificatesType());
        }
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getCertificatesNumber())) {
            updateChainWrapper.set(ContactPerson::getCertificatesNumber, contactPersonUpdateBusinessDto.getCertificatesNumber());
        }
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getProvinceCode())) {
            updateChainWrapper.set(ContactPerson::getProvinceCode, contactPersonUpdateBusinessDto.getProvinceCode());
        }
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getProvinceName())) {
            updateChainWrapper.set(ContactPerson::getProvinceName, contactPersonUpdateBusinessDto.getProvinceName());
        }
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getCityCode())) {
            updateChainWrapper.set(ContactPerson::getCityCode, contactPersonUpdateBusinessDto.getCityCode());
        }
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getCityName())) {
            updateChainWrapper.set(ContactPerson::getCityName, contactPersonUpdateBusinessDto.getCityName());
        }
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getDistrictCode())) {
            updateChainWrapper.set(ContactPerson::getDistrictCode, contactPersonUpdateBusinessDto.getDistrictCode());
        }
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getDistrictName())) {
            updateChainWrapper.set(ContactPerson::getDistrictName, contactPersonUpdateBusinessDto.getDistrictName());
        }
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getAddress())) {
            updateChainWrapper.set(ContactPerson::getAddress, contactPersonUpdateBusinessDto.getAddress());
        }
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getRemarks())) {
            updateChainWrapper.set(ContactPerson::getRemarks, contactPersonUpdateBusinessDto.getRemarks());
        }
        if (StrUtil.isNotBlank(contactPersonUpdateBusinessDto.getOperator())) {
            updateChainWrapper.set(ContactPerson::getOperator, contactPersonUpdateBusinessDto.getOperator());
        }
        if (contactPersonUpdateBusinessDto.getSigningPersonFlag() != null) {
            updateChainWrapper.set(ContactPerson::getSigningPersonFlag, contactPersonUpdateBusinessDto.getSigningPersonFlag());
        }
        if (contactPersonUpdateBusinessDto.getSigningPersonUpdate() != null){
            updateChainWrapper.set(ContactPerson::getSigningPersonUpdate, contactPersonUpdateBusinessDto.getSigningPersonUpdate());
        }
        if (contactPersonUpdateBusinessDto.getLegalPersonFlag() != null) {
            updateChainWrapper.set(ContactPerson::getLegalPersonFlag, contactPersonUpdateBusinessDto.getLegalPersonFlag());
        }
        return updateChainWrapper.update();
    }

    /**
     * 更新联系方式
     *
     * @param contactPersonUpdateBusinessDto    联系方式数据
     * <AUTHOR>
     * @date 2023/5/16 11:45
     * @return boolean
     **/
    private boolean updateContactInfo(ContactPersonUpdateBusinessDto contactPersonUpdateBusinessDto) {
        List<ContactPersonUpdateBusinessDto.ContactInfoUpdateBusinessDto> contactInfoList = contactPersonUpdateBusinessDto.getContactInfoList();
        contactInfoList.forEach(contactInfoUpdateBusinessDto -> {
            boolean result;
            ContactInfo contactInfo = new ContactInfo();
            contactInfo.setContactWay(contactInfoUpdateBusinessDto.getContactWay());
            contactInfo.setOperator(contactPersonUpdateBusinessDto.getOperator());
            if (StrUtil.isBlank(contactInfoUpdateBusinessDto.getContactInfoId())) {
                contactInfo.setContactInfoId(uniqueIdService.getId());
                contactInfo.setContactPersonId(contactPersonUpdateBusinessDto.getContactPersonId());
                contactInfo.setContactType(contactInfoUpdateBusinessDto.getContactType());
                contactInfo.setSourceKey(contactInfoUpdateBusinessDto.getSourceKey());
                contactInfo.setCreator(contactPersonUpdateBusinessDto.getOperator());
                result = contactInfoService.save(contactInfo);
            } else {
                LambdaUpdateChainWrapper<ContactInfo> lambdaUpdateChainWrapper = contactInfoService.lambdaUpdate()
                        .eq(ContactInfo::getContactInfoId, contactInfoUpdateBusinessDto.getContactInfoId());
                lambdaUpdateChainWrapper.set(ContactInfo::getOperator, contactPersonUpdateBusinessDto.getOperator());
                if (contactInfoUpdateBusinessDto.getPhoneVerifiedFlag() != null) {
                    lambdaUpdateChainWrapper.set(ContactInfo::getPhoneVerifiedFlag, contactInfoUpdateBusinessDto.getPhoneVerifiedFlag());
                }
                if (contactInfoUpdateBusinessDto.getPhoneVerifiedTime() != null) {
                    lambdaUpdateChainWrapper.set(ContactInfo::getPhoneVerifiedTime, contactInfoUpdateBusinessDto.getPhoneVerifiedTime());
                }
                if (StrUtil.isNotBlank(contactInfoUpdateBusinessDto.getContactWay())) {
                    lambdaUpdateChainWrapper.set(ContactInfo::getContactWay, contactInfoUpdateBusinessDto.getContactWay());
                } else {
                    lambdaUpdateChainWrapper.set(ContactInfo::getDeleteFlag, DeleteFlagEnum.DELETE.getCode());
                }
                result = lambdaUpdateChainWrapper.update();
            }
            if (!result) {
                log.warn("更新联系方式，联系方式更新失败，更新数据为:{}", JSON.toJSONString(contactInfoUpdateBusinessDto));
            }
        });
        return true;
    }

    /**
     * 更新联系方式
     *
     * @param contactPersonUpdateBusinessDto    联系方式数据
     * <AUTHOR>
     * @date 2023/5/16 11:45
     * @return boolean
     **/
    private boolean updateContactPhoneVerifiedFlag(ContactPersonUpdateBusinessDto contactPersonUpdateBusinessDto) {
        List<ContactPersonUpdateBusinessDto.ContactInfoUpdateBusinessDto> contactInfoList = contactPersonUpdateBusinessDto.getContactInfoList();
        contactInfoList.forEach(contactInfoUpdateBusinessDto -> {
            if (StringUtils.isNotBlank(contactInfoUpdateBusinessDto.getContactInfoId())) {
                LambdaUpdateChainWrapper<ContactInfo> lambdaUpdateChainWrapper = contactInfoService.lambdaUpdate()
                        .eq(ContactInfo::getContactInfoId, contactInfoUpdateBusinessDto.getContactInfoId());
                lambdaUpdateChainWrapper.set(ContactInfo::getOperator, contactPersonUpdateBusinessDto.getOperator());
                if (contactInfoUpdateBusinessDto.getPhoneVerifiedFlag() != null) {
                    lambdaUpdateChainWrapper.set(ContactInfo::getPhoneVerifiedFlag, contactInfoUpdateBusinessDto.getPhoneVerifiedFlag());
                }
                if (contactInfoUpdateBusinessDto.getPhoneVerifiedTime() != null) {
                    lambdaUpdateChainWrapper.set(ContactInfo::getPhoneVerifiedTime, contactInfoUpdateBusinessDto.getPhoneVerifiedTime());
                }
                boolean result = lambdaUpdateChainWrapper.update();
                if (!result) {
                    log.warn("更新联系方式的手机号是否已验证，联系方式手机号是否已验证更新失败，更新数据为:{}", JSON.toJSONString(contactInfoUpdateBusinessDto));
                }
            }
        });
        LambdaUpdateChainWrapper<ContactPerson> lambdaUpdateChainWrapper = contactPersonService.lambdaUpdate()
                        .eq(ContactPerson::getContactPersonId, contactPersonUpdateBusinessDto.getContactPersonId())
                        .set(ContactPerson::getOperator, contactPersonUpdateBusinessDto.getOperator());
        if (contactPersonUpdateBusinessDto.getLegalPersonFlag() != null) {
            lambdaUpdateChainWrapper.set(ContactPerson::getLegalPersonFlag, contactPersonUpdateBusinessDto.getLegalPersonFlag());
        }
        if (!lambdaUpdateChainWrapper.update()){
            log.warn("更新联系人是否是法人，联系人信息更新失败，更新数据为:{}", JSON.toJSONString(contactPersonUpdateBusinessDto));
        }
        contactPersonCacheHandler.del(contactPersonUpdateBusinessDto.getContactPersonId());
        return true;
    }

    /**
     * 联系人绑定unionId
     *
     * @param contactPersonId 联系人ID
     * @param bindType      绑定类型
     * @param unionId         微信unionId
     * @param sourceKey       来源key
     * @param operator        操作人
     * @return boolean
     * <AUTHOR>
     * @date 2023/6/16 11:22
     **/
    @Override
    public boolean bindUnionId(String contactPersonId, Integer bindType, String unionId,String wechatNickName, String sourceKey, String operator) {
        boolean flag = contactPersonService.lambdaUpdate()
                .eq(ContactPerson::getContactPersonId, contactPersonId)
                .isNull(ContactPerson::getUnionId)
                .set(ContactPerson::getBindType, bindType)
                .set(ContactPerson::getBindEmpId, operator)
                .set(ContactPerson::getWechatNickName, wechatNickName)
                .set(ContactPerson::getUnionId, unionId)
                .set(ContactPerson::getUnionIdBindTime, LocalDateTime.now())
                .set(ContactPerson::getOperatorKey, sourceKey)
                .set(ContactPerson::getOperator, operator)
                .update();
        contactPersonCacheHandler.del(contactPersonId);
        CustomerContact customerContact = customerContactService.lambdaQuery().eq(CustomerContact::getContactPersonId, contactPersonId).one();
        if (customerContact != null) {
            customerContactCacheHandler.del(customerContact.getCustomerId());
        }
        return flag;
    }

    /**
     * 联系人解绑unionId
     *
     * @param contactPersonId 联系人ID
     * @param sourceKey       来源key
     * @param operator        操作人ID
     * <AUTHOR>
     * @date 2023/6/16 11:23
     * @return boolean
     **/
    @Override
    public boolean unbindUnionId(String contactPersonId, String sourceKey, String operator) {
        boolean flag = contactPersonService.lambdaUpdate()
                .eq(ContactPerson::getContactPersonId, contactPersonId)
                .set(ContactPerson::getBindType, BindTypeEnum.INIT.getType())
                .set(ContactPerson::getBindEmpId, null)
                .set(ContactPerson::getWechatNickName, null)
                .set(ContactPerson::getUnionId, null)
                .set(ContactPerson::getOperatorKey, sourceKey)
                .set(ContactPerson::getOperator, operator)
                .update();
        contactPersonCacheHandler.del(contactPersonId);
        CustomerContact customerContact = customerContactService.lambdaQuery().eq(CustomerContact::getContactPersonId, contactPersonId).one();
        if (customerContact != null) {
            customerContactCacheHandler.del(customerContact.getCustomerId());
        }
        return flag;
    }

    /**
     * 根据unionId获取联系人信息
     *
     * @param bindType  绑定类型
     * @param unionId  联系人unionId
     * @return com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView
     * <AUTHOR>
     * @date 2023/6/16 21:12
     **/
    @Override
    public ContactPersonBusinessView getByUnionId(Integer bindType, String unionId) {
        ContactPerson contactPerson = contactPersonService.lambdaQuery()
                .eq(ContactPerson::getBindType, bindType)
                .eq(ContactPerson::getUnionId, unionId)
                .eq(ContactPerson::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .one();
        if (contactPerson != null) {
            return BeanUtil.copyProperties(contactPerson, ContactPersonBusinessView.class);
        }
        return null;
    }

    /***
     * 根据unionId获取联系人信息集合
     * @param bindType
     * @param unionId
     * <AUTHOR>
     * @date 2025/7/14 09:38
     * @version 1.0.0
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView>
    **/
    @Override
    public List<ContactPersonBusinessView> getContactPersonsByUnionId(Integer bindType, String unionId) {
        List<ContactPerson> contactPersons = contactPersonService.lambdaQuery()
                .eq(ContactPerson::getBindType, bindType)
                .eq(ContactPerson::getUnionId, unionId)
                .eq(ContactPerson::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .list();
        if (!CollectionUtil.isEmpty(contactPersons)) {
            return BeanUtil.copyToList(contactPersons, ContactPersonBusinessView.class);
        }
        return null;
    }

    /**
     * 根据unionId列表获取联系人列表
     *
     * @param unionIdList unionId列表
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView>
     * <AUTHOR>
     * @date 2023/6/16 14:41
     **/
    @Override
    public List<ContactPersonBusinessView> list(List<String> unionIdList) {
        List<ContactPerson> contactPersonList = contactPersonService.lambdaQuery()
                .in(ContactPerson::getUnionId, unionIdList)
                .eq(ContactPerson::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .orderByDesc(ContactPerson::getUnionIdBindTime)
                .list();
        return Optional.ofNullable(contactPersonList).orElse(new ArrayList<>()).stream().map(contactPerson -> {
            ContactPersonBusinessView contactPersonBusinessView = BeanUtil.copyProperties(contactPerson, ContactPersonBusinessView.class);
            ChannelSourceBusinessDto channelSourceBusinessDto = new ChannelSourceBusinessDto();
            channelSourceBusinessDto.setSourceKey(contactPerson.getSourceKey());
            ChannelSourceBusinessView channelSourceBusinessView = channelSourceBusiness.getBySourceKey(channelSourceBusinessDto);
            if (channelSourceBusinessView != null) {
                contactPersonBusinessView.setSourceTag(channelSourceBusinessView.getSourceShowName());
            }
            List<ContactInfo> contactInfoList = contactInfoService.lambdaQuery()
                    .eq(ContactInfo::getContactPersonId, contactPerson.getContactPersonId())
                    .eq(ContactInfo::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                    .orderByAsc(ContactInfo::getContactType)
                    .list();
            if (CollectionUtil.isNotEmpty(contactInfoList)) {
                contactPersonBusinessView.setContactInfoList(BeanUtil.copyToList(contactInfoList, ContactInfoBusinessView.class));
            }
            return contactPersonBusinessView;
        }).collect(Collectors.toList());
    }

    /**
     * 删除联系人
     *
     * @param contactPersonId 联系人ID
     * @return boolean
     * <AUTHOR>
     * @date 2024/1/15 14:01
     **/
    @Override
    public boolean del(String contactPersonId) {
        //逻辑删除联系人及联系方式
        boolean delFlag = contactPersonService.lambdaUpdate()
                .set(ContactPerson::getDeleteFlag, DeleteFlagEnum.DELETE.getCode())
                .eq(ContactPerson::getContactPersonId, contactPersonId)
                .update()
                &&
                contactInfoService.lambdaUpdate()
                        .set(ContactInfo::getDeleteFlag, DeleteFlagEnum.DELETE.getCode())
                        .eq(ContactInfo::getContactPersonId, contactPersonId)
                        .update();
        //清空联系人及联系方式缓存
        contactPersonCacheHandler.del(contactPersonId);
        CustomerContact customerContact = customerContactService.lambdaQuery().eq(CustomerContact::getContactPersonId, contactPersonId).one();
        if (customerContact != null) {
            customerContactCacheHandler.del(customerContact.getCustomerId());
        }
        return delFlag;
    }

    /**
     * 刷新联系人是否为法人标识
     * 由于搜客宝接口每天访问有次数限制（80万 目前业务用掉大概六十多万），所以需要分摊到每天执行，而不是每天都需要跑全量数据。
     *
     * @return void
     * <AUTHOR>
     * @date 2024/8/30
     */
    @Override
    public void refreshLegalPersonFlag() {
        try {
            //获取法人列表
            List<ContactPersonLegal> contactPersonLegalList = contactPersonMapper.getContactPersonLegalList();
            if (CollectionUtil.isEmpty(contactPersonLegalList)) {
                log.info("定时任务刷新联系人是否为法人标识，没有需要更新的数据");
                return;
            }
            log.error("定时任务刷新联系人是否为法人标识，个数为：{} 个", contactPersonLegalList.size());
            for (ContactPersonLegal contactPersonLegal : contactPersonLegalList) {
                if (org.apache.commons.lang3.StringUtils.isBlank(contactPersonLegal.getContactPersonName())){
                    log.warn("联系人姓名为空，contactPersonId:{}", contactPersonLegal.getContactPersonId());
                    continue;
                }
                //获取法人标记并更新
                CustomerContactBusinessDto customerContactBusinessDto = new CustomerContactBusinessDto();
                customerContactBusinessDto.setContactPersonId(contactPersonLegal.getContactPersonId());
                CustomerContactBusinessView customerContactBusinessView = customerContactBusiness.customerContactByContactId(customerContactBusinessDto);
                if (customerContactBusinessView != null) {
                    CustomerBusinessDto customerBusinessDto = new CustomerBusinessDto();
                    customerBusinessDto.setCustomerId(customerContactBusinessView.getCustomerId());
                    CustomerBusinessView customerBusinessView = customerBusiness.detail(customerBusinessDto);
                    if (customerBusinessView != null) {
                        //获取法人标记
                        Optional<BigDataCompanyDetail> bigDataCustomerInfoOptional = Optional.ofNullable(bigDataThirdService.getBigDataCustomerInfo(customerBusinessView.getCustomerName()));
                        if (!bigDataCustomerInfoOptional.isPresent()) {
                            log.warn("refreshLegalPersonFlag-获取客户信息失败，customerName:{}", customerBusinessView.getCustomerName());
                            continue;
                        }
                        BigDataCompanyDetail bigDataCustomerInfo = bigDataCustomerInfoOptional.get();
                        if (org.apache.commons.lang3.StringUtils.isBlank(bigDataCustomerInfo.getLegal_person())){
                            log.warn("获取大数据接口，法人标记姓名为空，customerName:{}", customerBusinessView.getCustomerName());
                            continue;
                        }
                        boolean legalFlag = Objects.equals(contactPersonLegal.getContactPersonName().trim(), bigDataCustomerInfo.getLegal_person().trim());
                        log.info("refreshLegalPersonFlag-获取法人标记，ContactPersonLegal:{}, legalFlag:{}, legalPerson:{}", JSON.toJSONString(contactPersonLegal), legalFlag, bigDataCustomerInfo.getLegal_person());
                        if (!legalFlag) {
                            // 不相等 则更新联系人为非法人
                            boolean contactPersonUpdate = contactPersonService.lambdaUpdate()
                                    .eq(ContactPerson::getId, contactPersonLegal.getId())
                                    .set(ContactPerson::getLegalPersonFlag, 0)
                                    .update();
                            if (!contactPersonUpdate) {
                                log.error("refreshLegalPersonFlag-更新联系人是否是法人，联系人信息更新失败，ContactPersonLegal:{}", JSON.toJSONString(contactPersonLegal));
                                continue;
                            }
                            //更新成功后 删除contactPerson缓存
                            contactPersonCacheHandler.del(contactPersonLegal.getContactPersonId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("refreshLegalPersonFlag-异常信息：{}", e.getMessage(), e);
        }
    }

    /***
     * 根据员工ID和unionIdList 查询联系人列表
     * @param empId
     * @param unionIdList
     * <AUTHOR>
     * @date 2024/11/25 19:40
     * @version 1.0.0
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView>
     **/
    @Override
    public List<ContactPersonBusinessView> getContactPersonListByEmpIdAndUnionIdList(String empId, List<String> unionIdList) {
        List<ContactPerson> contactPersonList = contactPersonService.lambdaQuery()
                .in(ContactPerson::getUnionId, unionIdList)
                .eq(ContactPerson::getBindEmpId, empId)
                .eq(ContactPerson::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .orderByDesc(ContactPerson::getUnionIdBindTime)
                .list();
        return Optional.ofNullable(contactPersonList).orElse(new ArrayList<>()).stream().map(contactPerson -> {
            ContactPersonBusinessView contactPersonBusinessView = BeanUtil.copyProperties(contactPerson, ContactPersonBusinessView.class);
            return contactPersonBusinessView;
        }).collect(Collectors.toList());
    }




    /***
     * 分页查询绑定信息
     * @param bindUnionIdQueryBusinessDto
     * <AUTHOR>
     * @date 2024/11/25 20:46
     * @version 1.0.0
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView>
    **/
    @Override
    public Page<ContactPersonBusinessView> getContactPersonListByEmpIdList(BindUnionIdQueryBusinessDto bindUnionIdQueryBusinessDto) {
        LambdaQueryChainWrapper<ContactPerson> contactPersonLambdaQueryChainWrapper = contactPersonService.lambdaQuery();
        contactPersonLambdaQueryChainWrapper
                .isNotNull(ContactPerson::getUnionId)
                .in(ContactPerson::getBindEmpId, bindUnionIdQueryBusinessDto.getEmpIdList())
                .ge(ContactPerson::getUnionIdBindTime, bindUnionIdQueryBusinessDto.getUnionIdBindBeginTime())
                .le(ContactPerson::getUnionIdBindTime,bindUnionIdQueryBusinessDto.getUnionIdBindEndTime())
                .eq(ContactPerson::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())
                .orderByDesc(ContactPerson::getUnionIdBindTime);
        // 分页处理
        Page<ContactPerson> page = Page.of(bindUnionIdQueryBusinessDto.getPageNum(), bindUnionIdQueryBusinessDto.getPageSize());
        Page<ContactPerson> contactPersonPage = contactPersonLambdaQueryChainWrapper.page(page);
        Page<ContactPersonBusinessView> returnPage = BeanUtil.copyProperties(contactPersonPage, Page.class);
        returnPage.setRecords(BeanUtil.copyToList(contactPersonPage.getRecords(), ContactPersonBusinessView.class));
        return returnPage;
    }
}