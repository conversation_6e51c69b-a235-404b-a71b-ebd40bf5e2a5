package com.ce.scrm.customer.service.cache.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ce.scrm.customer.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.customer.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.customer.dao.entity.RocketTopicSwitch;
import com.ce.scrm.customer.dao.service.RocketTopicSwitchService;
import com.ce.scrm.customer.service.cache.entity.RocketTopicSwitchCacheData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 客户缓存处理器
 * <AUTHOR>
 * @date 2023/4/13 14:27
 * @version 1.0.0
 */
@Slf4j
@Service
public class RocketTopicSwitchCacheHandler extends AbstractStringCacheHandler<RocketTopicSwitchCacheData> {

    @Resource
    private RocketTopicSwitchService rocketTopicSwitchService;

    /**
     * 获取业务缓存的key
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return com.ce.scrm.customer.cache.enumeration.CacheKeyEnum
     **/
    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.ROCKET_TOPIC;
    }

    /**
     * 指定对象类型
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return java.lang.Class<T>
     **/
    @Override
    protected Class<RocketTopicSwitchCacheData> getClazz() {
        return RocketTopicSwitchCacheData.class;
    }

    /**
     * 获取rocket topic switch 数据
     *
     * @param topicName   topic名称
     * <AUTHOR>
     * @date 2023/4/13 14:06
     * @return com.ce.scrm.customer.service.cache.entity.RocketTopicSwitchCacheData
     **/
    @Override
    protected RocketTopicSwitchCacheData queryDataBySource(String topicName) {
        if (StrUtil.isBlank(topicName)){
            return null;
        }
        RocketTopicSwitch rocketTopicSwitch = rocketTopicSwitchService.lambdaQuery().eq(RocketTopicSwitch::getTopic,topicName).last("limit 1").one();
        if(rocketTopicSwitch==null){
            return null;
        }
        return BeanUtil.copyProperties(rocketTopicSwitch, getClazz());
    }
}