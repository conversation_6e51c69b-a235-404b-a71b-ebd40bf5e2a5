package com.ce.scrm.customer.service.business.entity.view;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 客户业务数据
 * <AUTHOR>
 * @date 2023/4/7 17:31
 * @version 1.0.0
 */
@Data
public class CustomerContactBusinessView {

    /**
     * 客户id：C、企业客户标识，P、个人客户标识，Q、其他客户标识（历史客户ID保持不变）
     */
    private String customerId;

    /**
     * 联系人id
     */
    private String contactPersonId;

    /**
     * 删除标记：1、删除，0、未删除
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者凭证
     */
    private String creatorKey;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人凭证
     */
    private String operatorKey;

    /**
     * 更新人
     */
    private String operator;

}