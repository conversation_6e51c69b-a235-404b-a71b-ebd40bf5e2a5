package com.ce.scrm.customer.service.business;

import com.ce.scrm.customer.dao.entity.OrderDefaultFlag;
import com.ce.scrm.customer.service.enums.ContactInfoEnum;

import java.util.List;

/**
 * 订单默认标记业务接口
 * <AUTHOR>
 * @date 2023/4/13
 * @version 1.0.0
 */
public interface IOrderDefaultFlagBusiness {

    /**
     * 获取订单默认标记的数据
     * @param contactPersonId   联系人ID
     * @param phoneList 手机号列表
     * @param emailList 邮箱列表
     * <AUTHOR>
     * @date 2023/5/22 10:28
     * @return com.ce.scrm.customer.dao.entity.OrderDefaultFlag
     **/
    OrderDefaultFlag get(String contactPersonId, List<String> phoneList, List<String> emailList);

    /**
     * 订单添加标记
     * @param contactPersonId   联系人ID
     * @param phone 手机号
     * @param email 邮箱
     * @param operator        操作人
     * <AUTHOR>
     * @date 2023/5/23 09:47
     * @return boolean
     **/
    boolean add(String contactPersonId, String phone, String email, String operator);

    /**
     * 校验是否是默认签单人
     * @param contactPersonId   联系人ID
     * @param contactInfoEnum   联系方式类型
     * @param contactWay    联系方式
     * <AUTHOR>
     * @date 2023/5/25 15:41
     * @return boolean
     **/
    boolean check(String contactPersonId, ContactInfoEnum contactInfoEnum, String contactWay);
}