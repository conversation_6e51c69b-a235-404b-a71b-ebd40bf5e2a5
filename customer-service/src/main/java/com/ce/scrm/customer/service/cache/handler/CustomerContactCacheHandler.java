package com.ce.scrm.customer.service.cache.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.ce.scrm.customer.cache.enumeration.CacheKeyEnum;
import com.ce.scrm.customer.cache.handler.AbstractStringCacheHandler;
import com.ce.scrm.customer.dao.entity.ContactInfo;
import com.ce.scrm.customer.dao.entity.ContactPerson;
import com.ce.scrm.customer.dao.entity.Customer;
import com.ce.scrm.customer.dao.entity.CustomerContact;
import com.ce.scrm.customer.dao.service.ContactInfoService;
import com.ce.scrm.customer.dao.service.ContactPersonService;
import com.ce.scrm.customer.dao.service.CustomerContactService;
import com.ce.scrm.customer.dao.service.CustomerService;
import com.ce.scrm.customer.service.cache.entity.ContactInfoCacheData;
import com.ce.scrm.customer.service.cache.entity.ContactPersonCacheData;
import com.ce.scrm.customer.service.cache.entity.CustomerCacheData;
import com.ce.scrm.customer.service.cache.entity.CustomerContactCacheData;
import com.ce.scrm.customer.service.constant.ServiceConstant;
import com.ce.scrm.customer.service.enums.DeleteFlagEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 客户联系人缓存处理器
 * <AUTHOR>
 * @date 2023/4/13 14:27
 * @version 1.0.0
 */
@Slf4j
@Service
public class CustomerContactCacheHandler extends AbstractStringCacheHandler<CustomerContactCacheData> {

    @Resource
    private CustomerService customerService;

    @Resource
    private CustomerContactService customerContactService;

    @Resource
    private ContactPersonService contactPersonService;

    @Resource
    private ContactInfoService contactInfoService;

    /**
     * 获取业务缓存的key
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return com.ce.scrm.customer.cache.enumeration.CacheKeyEnum
     **/
    @Override
    public CacheKeyEnum getCacheKey() {
        return CacheKeyEnum.CUSTOMER_CONTACT;
    }

    /**
     * 指定对象类型
     *
     * <AUTHOR>
     * @date 2023/4/6 17:21
     * @return java.lang.Class<T>
     **/
    @Override
    protected Class<CustomerContactCacheData> getClazz() {
        return CustomerContactCacheData.class;
    }

    /**
     * 获取客户联系人数据
     *
     * @param sourceKey
     * <AUTHOR>
     * @date 2023/4/13 14:06
     * @return com.ce.scrm.customer.cache.CustomerCacheData
     **/
    @Override
    protected CustomerContactCacheData queryDataBySource(String sourceKey) {
        Customer customer = customerService.lambdaQuery()
                .eq(Customer::getCustomerId, sourceKey)
                /*
                .eq(Customer::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode())*/
                .last("limit 1").one();

        if (customer == null) {
            return null;
        }

        List<CustomerContact> customerContactList = customerContactService.lambdaQuery().eq(CustomerContact::getCustomerId, sourceKey).eq(CustomerContact::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();
        List<String> contactPersonIdList = customerContactList.stream().map(CustomerContact::getContactPersonId).collect(Collectors.toList());
        List<ContactPersonCacheData> contactPersonCacheDataList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(contactPersonIdList)) {
            List<ContactPerson> contactPersonList = contactPersonService.lambdaQuery().in(ContactPerson::getContactPersonId, contactPersonIdList).eq(ContactPerson::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();
            for (ContactPerson contactPerson : contactPersonList) {
                List<ContactInfo> contactInfoList = contactInfoService.lambdaQuery().eq(ContactInfo::getContactPersonId, contactPerson.getContactPersonId())
                        .eq(ContactInfo::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getCode()).list();
                List<ContactInfoCacheData> contactInfoCacheDataList = Optional.of(contactInfoList).orElse(Lists.newArrayList()).stream().map(record -> {
                    ContactInfoCacheData contactInfoCacheData = new ContactInfoCacheData();
                    BeanUtil.copyProperties(record, contactInfoCacheData);
                    return contactInfoCacheData;
                }).collect(Collectors.toList());
                ContactPersonCacheData contactPersonCacheData = BeanUtil.copyProperties(contactPerson, ContactPersonCacheData.class);
                contactPersonCacheData.setPositionName(ServiceConstant.POSITION_MAPPING.getOrDefault(contactPersonCacheData.getPosition(), contactPersonCacheData.getPosition()));
                contactPersonCacheData.setContactInfoList(contactInfoCacheDataList);
                contactPersonCacheDataList.add(contactPersonCacheData);
            }
        }

        CustomerContactCacheData customerContactCacheData = new CustomerContactCacheData();
        CustomerCacheData customerCacheData = BeanUtil.copyProperties(customer, CustomerCacheData.class);
        customerCacheData.setContactPersonCacheDataList(contactPersonCacheDataList);
        customerContactCacheData.setCustomerCacheData(customerCacheData);
        return customerContactCacheData;
    }
}