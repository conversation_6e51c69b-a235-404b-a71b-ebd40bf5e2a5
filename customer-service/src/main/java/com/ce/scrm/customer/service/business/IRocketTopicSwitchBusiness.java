package com.ce.scrm.customer.service.business;

import com.ce.scrm.customer.service.business.entity.dto.RocketTopicSwitchBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.RocketTopicSwitchBusinessView;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/7/10 17:15
 */
public interface IRocketTopicSwitchBusiness {

    /**
     * 根据topicName 获取 开关值
     * @author:
     * @date: 2023/7/10 18:30
     * @param topicName
     * @return java.lang.Integer
     */
    Integer getTopicSwitchByTopicName(String topicName);

    /**
     * topics 列表查询
     * @author:wangshoufang
     * @date: 2023/7/10 16:59
     * @param rocketTopicSwitchBusinessDto
     * @return com.ce.scrm.customer.dubbo.entity.response.DubboResult<java.util.List<com.ce.scrm.customer.dubbo.entity.view.IndustryDubboView>>
     */
    List<RocketTopicSwitchBusinessView> getTopicSwitchByConditation(RocketTopicSwitchBusinessDto rocketTopicSwitchBusinessDto);

    /**
     * 更新 rocketmq 开关
     * @author:
     * @date: 2023/7/10 18:10
     * @param rocketTopicSwitchBusinessDto
     * @return java.lang.Boolean
     */
    Boolean update(RocketTopicSwitchBusinessDto rocketTopicSwitchBusinessDto);
}
