package com.ce.scrm.customer.service.cache.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户缓存数据
 * <AUTHOR>
 * @date 2023/4/13 14:01
 * @version 1.0.0
 */
@Data
public class ContactInfoCacheData {

    /**
     * 联系方式ID
     */
    private String contactInfoId;

    /**
     * 联系人ID
     */
    private String contactPersonId;

    /**
     * 联系类型：1、手机，2、微信，3、邮箱，4、电话，5、qq，6、企业微信
     */
    private Integer contactType;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 是否会员
     */
    private Integer memberFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标记：1、删除，0、未删除
     */
    private Integer deleteFlag;

    /**
     * 默认签单人
     */
    private Integer signatoryFlag;

    /**
     * 手机号是否已验证 1是 0否
     */
    private Integer phoneVerifiedFlag;

    /**
     * 手机号验证时间
     */
    private LocalDateTime phoneVerifiedTime;
}