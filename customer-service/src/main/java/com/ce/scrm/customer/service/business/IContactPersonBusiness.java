package com.ce.scrm.customer.service.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ce.scrm.customer.service.business.entity.dto.BindUnionIdQueryBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.ContactPersonAddBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.ContactPersonBusinessDto;
import com.ce.scrm.customer.service.business.entity.dto.ContactPersonUpdateBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView;
import com.ce.scrm.customer.service.business.entity.view.CustomerBusinessView;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 联系人业务接口
 * <AUTHOR>
 * @date 2023/4/13
 * @version 1.0.0
 */
public interface IContactPersonBusiness {

    /**
     * 获取联系人数据
     * @param contactPersonBusinessDto   查询参数
     * <AUTHOR>
     * @date 2023/4/13 13:35
     * @return com.ce.scrm.customer.support.business.entity.view.ContactPersonBusinessView
     **/
    ContactPersonBusinessView detail(ContactPersonBusinessDto contactPersonBusinessDto);

    /**
     * 获取联系人数据
     * @param contactPersonId   联系人ID
     * <AUTHOR>
     * @date 2023/5/17 10:45
     * @return com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView
     **/
    ContactPersonBusinessView detail(String contactPersonId);

    /**
     * 获取联系人数据
     * @param customerId   客户ID
     * @param phone   手机号
     * <AUTHOR>
     * @date 2023/5/15 17:22
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView>
     **/
    List<ContactPersonBusinessView> detail(String customerId, String phone);

    /**
     * 获取签名联系人数据
     * @param contactPersonName  联系人姓名
     * @param phone   手机号
     * <AUTHOR>
     * @date 2024/9/5
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.SigningPersonDetailDataBusinessView>
     **/
    List<CustomerBusinessView> getSigningPersonDataDetail(String contactPersonName, String phone);

    /**
     * 添加联系人
     * @param contactPersonAddBusinessDto   联系人数据
     * <AUTHOR>
     * @date 2023/5/15 13:38
     * @return boolean
     **/
    boolean add(ContactPersonAddBusinessDto contactPersonAddBusinessDto);

    /**
     * 更新联系人
     * @param contactPersonUpdateBusinessDto   联系人数据
     * <AUTHOR>
     * @date 2023/5/15 13:38
     * @return boolean
     **/
    boolean update(ContactPersonUpdateBusinessDto contactPersonUpdateBusinessDto);

    /**
     * 更新联系人手机号是否已验证
     * @param contactPersonUpdateBusinessDto
     * <AUTHOR>
     * @date 2023/11/22 11:13
     * @version 1.0.0
     * @return boolean
     **/
    boolean updatePhoneVerifiedFlag(ContactPersonUpdateBusinessDto contactPersonUpdateBusinessDto);

    /**
     * 联系人绑定unionId
     *
     * @param contactPersonId 联系人ID
     * @param bindType  绑定类型
     * @param unionId         微信unionId
     * @param unionId         微信昵称
     * @param sourceKey       来源key
     * @param operator        操作人
     * @return boolean
     * <AUTHOR>
     * @date 2023/6/16 11:22
     **/
    boolean bindUnionId(String contactPersonId, Integer bindType, String unionId,String wechatNickName, String sourceKey, String operator);

    /**
     * 联系人解绑unionId
     * @param contactPersonId   联系人ID
     * @param sourceKey         来源key
     * @param operator          操作人ID
     * <AUTHOR>
     * @date 2023/6/16 11:23
     * @return boolean
     **/
    boolean unbindUnionId(String contactPersonId, String sourceKey, String operator);

    /**
     * 根据unionId获取联系人信息
     *
     * @param bindType  绑定类型
     * @param unionId  联系人unionId
     * @return com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView
     * <AUTHOR>
     * @date 2023/6/16 21:12
     **/
    ContactPersonBusinessView getByUnionId(Integer bindType, String unionId);

    /***
     * 根据unionId获取联系人信息集合
     * @param bindType
     * @param unionId
     * <AUTHOR>
     * @date 2025/7/14 09:38
     * @version 1.0.0
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView>
     **/
    List<ContactPersonBusinessView> getContactPersonsByUnionId(Integer bindType, String unionId);

    /**
     * 根据unionId列表获取联系人列表
     * @param unionIdList   unionId列表
     * <AUTHOR>
     * @date 2023/6/16 14:41
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView>
     **/
    List<ContactPersonBusinessView> list(List<String> unionIdList);

    /**
     * 删除联系人
     * @param contactPersonId   联系人ID
     * <AUTHOR>
     * @date 2024/1/15 14:01
     * @return boolean
     **/
    boolean del(String contactPersonId);

    /**
     * 刷新联系人是否为法人标识
     * <AUTHOR>
     * @date 2024/8/30
     * @return void
     */
    void refreshLegalPersonFlag();


    /***
     * 根据员工ID和unionIdList 查询联系人列表
     * @param empId
     * @param unionIdList
     * <AUTHOR>
     * @date 2024/11/25 19:40
     * @version 1.0.0
     * @return java.util.List<com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView>
    **/
    List<ContactPersonBusinessView> getContactPersonListByEmpIdAndUnionIdList(String empId,List<String> unionIdList);

    /***
     * 根据员工ID 查询绑定联系人列表
     * 分页
     * @param bindUnionIdQueryBusinessDto
     * <AUTHOR>
     * @date 2024/11/25 20:54
     * @version 1.0.0
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.ce.scrm.customer.service.business.entity.view.ContactPersonBusinessView>
    **/
    Page<ContactPersonBusinessView> getContactPersonListByEmpIdList(BindUnionIdQueryBusinessDto bindUnionIdQueryBusinessDto);
}