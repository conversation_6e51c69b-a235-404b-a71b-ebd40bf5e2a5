package com.ce.scrm.customer.service.enums;

/**
 * 联系方式枚举
 * <AUTHOR>
 * @date 2023/4/6 13:57
 * @version 1.0.0
 **/
public enum ContactInfoEnum {
    /**
     * 历史数据（其他）
     */
    other(0),
    /**
     * 手机
     */
    mobile(1),
    /**
     * 微信
     */
    wechat(2),
    /**
     * 邮箱
     */
    email(3),
    /**
     * 电话
     */
    telephone(4),
    /**
     * qq
     */
    qq(5),
    /**
     * 企业微信
     */
    wecome(6),
    ;

    private final Integer type;

    ContactInfoEnum(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public static ContactInfoEnum get(Integer type) {
        for (ContactInfoEnum contactInfoEnum : ContactInfoEnum.values()) {
            if (contactInfoEnum.type.equals(type)) {
                return contactInfoEnum;
            }
        }
        return null;
    }
}