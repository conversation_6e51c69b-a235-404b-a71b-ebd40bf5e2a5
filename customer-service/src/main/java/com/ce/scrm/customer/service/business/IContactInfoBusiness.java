package com.ce.scrm.customer.service.business;

import com.ce.scrm.customer.service.business.entity.dto.ContactInfoBusinessDto;
import com.ce.scrm.customer.service.business.entity.view.ContactInfoBusinessView;

import java.util.List;
import java.util.Optional;

/**
 * 客户业务接口
 * <AUTHOR>
 * @date 2023/4/13
 * @version 1.0.0
 */
public interface IContactInfoBusiness {

    /**
     * 获取联系方式数据
     * @param contactInfoBusinessDto 查询参数
     * <AUTHOR>
     * @date 2023/4/13 13:35
     * @return com.ce.scrm.customer.support.business.entity.view.ContactPersonBusinessView
     **/
    ContactInfoBusinessView getCustomerContactInfoData(ContactInfoBusinessDto contactInfoBusinessDto);

    /**
     * 根据联系方式和来源获取客户下联系人方式数据
     * @param customerId 客户ID
     * @param contactPersonName 联系人名称
     * @param sourceKey 来源key
     * @param phoneList 手机号列表
     * <AUTHOR>
     * @date 2023/5/17 13:02
     * @return java.util.Optional<com.ce.scrm.customer.service.business.entity.view.ContactInfoBusinessView>
     **/
    Optional<ContactInfoBusinessView> getCustomerContactInfoData(String customerId, String contactPersonName, String sourceKey, List<String> phoneList);

    /**
     * 获取联系方列表数据
     * @param contactInfoBusinessDto 查询参数
     * <AUTHOR>
     * @date 2023/4/13 13:35
     * @return com.ce.scrm.customer.support.business.entity.view.ContactPersonBusinessView
     **/
    List<ContactInfoBusinessView> detailList(ContactInfoBusinessDto contactInfoBusinessDto);

}